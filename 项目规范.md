# AI Portal 项目开发规范

## 📋 目录

- [🏗️ 项目概述](#️-项目概述)
- [🎯 业务领域模型](#-业务领域模型)
- [📁 项目架构](#-项目架构)
- [🔧 技术栈规范](#-技术栈规范)
- [📂 目录结构规范](#-目录结构规范)
- [💾 数据库设计规范](#-数据库设计规范)
- [🌐 API设计规范](#-api设计规范)
- [🎨 前端开发规范](#-前端开发规范)
- [⚙️ 后端开发规范](#️-后端开发规范)
- [📝 代码规范](#-代码规范)
- [🧪 测试规范](#-测试规范)
- [🚀 部署规范](#-部署规范)

## 🏗️ 项目概述

AI Portal 是一个面向AI工程化实践的知识分享与协作平台，支持个人空间和团队空间两大核心业务领域。

### 核心功能
- **个人空间**: 用户个人资料管理、内容创作与管理、学习进度跟踪
- **团队空间**: 团队协作、内容推荐与分享、成员管理
- **内容管理**: 支持Prompt、文章、工具、课程等多种知识类型
- **社交功能**: 关注、点赞、收藏、评论等互动功能

## 🎯 业务领域模型

### 1. 空间领域 (Space Domain)

#### 1.1 个人空间 (Personal Space)
- **核心实体**: User (用户)
- **业务概念**:
  - 个人资料 (Profile): 基础信息、简介、标签
  - 创作成就 (Creative Achievements): 发布数量、浏览量、点赞数、收藏数
  - 社交关系 (Social Relations): 关注者、关注中
  - 学习进度 (Learning Progress): 课程学习、学习时长、连续学习天数

#### 1.2 团队空间 (Team Space)
- **核心实体**: Team (团队)
- **业务概念**:
  - 团队信息 (Team Info): 名称、描述、头像、隐私设置
  - 成员管理 (Member Management): 角色权限、邀请设置
  - 团队成就 (Team Achievements): 推荐内容数、总浏览量、总互动数
  - 活动记录 (Activity Log): 成员活动、内容推荐记录

### 2. 内容领域 (Content Domain)

#### 2.1 知识类型 (Knowledge Types)
- **prompt**: AI提示词模板
- **article**: 技术文章
- **tool**: 工具介绍
- **course**: 课程内容
- **mcp**: MCP工具

#### 2.2 内容生命周期
- 创建 → 发布 → 推荐 → 互动 → 归档

### 3. 推荐领域 (Recommendation Domain)

#### 3.1 推荐机制
- 团队内容推荐
- 推荐理由记录
- 推荐人信息追踪

### 4. 用户交互领域 (Interaction Domain)

#### 4.1 社交互动
- 点赞 (Like)
- 收藏 (Favorite)
- 关注 (Follow)
- 评论 (Comment)

## 📁 项目架构

### 整体架构
```
AI Portal
├── frontend/          # Vue.js 前端应用
├── backend/           # Spring Boot 后端应用
├── Demo/             # 测试数据
├── Design/           # 设计文档
└── PRD/              # 产品需求文档
```

### 前后端分离架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (Vue.js)                       │
│  Components, Views, Stores, Router, Services              │
└─────────────────────────────────────────────────────────────┘
                                │ HTTP/REST API
┌─────────────────────────────────────────────────────────────┐
│                    Backend (Spring Boot)                   │
│  Web Layer → Service Layer → DAO Layer → Database         │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 技术栈规范

### 前端技术栈
- **框架**: Vue.js 3.x
- **构建工具**: Webpack 5.x
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **HTTP客户端**: Axios
- **UI组件**: 自定义组件 + Font Awesome
- **样式**: CSS3 + SCSS

### 后端技术栈
- **框架**: Spring Boot 2.7.18
- **安全**: Spring Security + JWT
- **ORM**: MyBatis 2.3.1
- **数据库**: MySQL 8.0
- **连接池**: Druid 1.2.16
- **认证**: OAuth2 (Google, GitHub)

### 开发工具
- **版本控制**: Git
- **包管理**: Maven (后端) + npm (前端)
- **IDE**: IntelliJ IDEA / VS Code

## 📂 目录结构规范

### 后端目录结构 (按领域划分)
```
backend/
├── common/                    # 公共模块
│   └── src/main/java/com/jdl/aic/portal/common/
│       ├── config/           # 配置类
│       ├── dto/              # 通用数据传输对象
│       ├── exception/        # 异常类
│       ├── result/           # 响应结果
│       └── utils/            # 工具类
├── dao/                      # 数据访问层
│   └── src/main/java/com/jdl/aic/portal/dao/
│       ├── entity/           # 实体类
│       └── mapper/           # MyBatis接口
├── service/                  # 业务逻辑层 (按领域划分)
│   └── src/main/java/com/jdl/aic/portal/
│       ├── space/            # 空间领域
│       │   ├── service/      # 空间领域服务接口
│       │   └── service/impl/ # 空间领域服务实现
│       ├── content/          # 内容领域
│       │   ├── service/      # 内容领域服务接口
│       │   └── service/impl/ # 内容领域服务实现
│       ├── recommendation/   # 推荐领域
│       │   ├── service/      # 推荐领域服务接口
│       │   └── service/impl/ # 推荐领域服务实现
│       └── auth/             # 认证领域
│           ├── service/      # 认证服务接口
│           └── service/impl/ # 认证服务实现
└── web/                      # 表现层 (按领域划分)
    └── src/main/java/com/jdl/aic/portal/
        ├── space/            # 空间领域
        │   ├── controller/   # 空间领域控制器
        │   └── dto/          # 空间领域DTO
        ├── content/          # 内容领域
        │   ├── controller/   # 内容领域控制器
        │   └── dto/          # 内容领域DTO
        ├── recommendation/   # 推荐领域
        │   ├── controller/   # 推荐领域控制器
        │   └── dto/          # 推荐领域DTO
        └── auth/             # 认证领域
            ├── controller/   # 认证控制器
            └── dto/          # 认证DTO
```

### 领域划分说明

#### 1. 空间领域 (Space Domain)
- **职责**: 个人空间和团队空间的管理
- **核心功能**:
  - 个人资料管理 (UserProfileController, UserProfileService)
  - 团队管理 (TeamController, TeamService)
  - 成员管理 (TeamMemberController, TeamMemberService)
- **主要实体**: User, Team, TeamMember

#### 2. 内容领域 (Content Domain)
- **职责**: 知识内容的创建、管理和展示
- **核心功能**:
  - 内容创建和编辑 (ContentController, ContentService)
  - 内容分类管理 (CategoryController, CategoryService)
  - 内容搜索和过滤 (ContentSearchController, ContentSearchService)
- **主要实体**: Content, Category, Tag

#### 3. 推荐领域 (Recommendation Domain)
- **职责**: 内容推荐和分享机制
- **核心功能**:
  - 团队内容推荐 (RecommendationController, RecommendationService)
  - 推荐算法和策略 (RecommendationEngine)
  - 推荐统计和分析 (RecommendationAnalyticsService)
- **主要实体**: Recommendation, RecommendationHistory

#### 4. 认证领域 (Auth Domain)
- **职责**: 用户认证和授权
- **核心功能**:
  - 用户登录注册 (AuthController, AuthService)
  - OAuth集成 (OAuthController, OAuthService)
  - JWT令牌管理 (JwtService)
- **主要实体**: User, JwtToken, LoginLog

### 前端目录结构
```
frontend/src/
├── components/               # 通用组件
│   ├── common/              # 基础组件
│   ├── personal/            # 个人空间组件
│   ├── team/                # 团队空间组件
│   └── recommendation/      # 推荐相关组件
├── views/                   # 页面组件
│   └── space/               # 空间领域页面
│       ├── personal/        # 个人空间页面
│       └── team/            # 团队空间页面
├── services/                # API服务层
├── stores/                  # 状态管理
├── router/                  # 路由配置
├── utils/                   # 工具函数
└── assets/                  # 静态资源
```

## 💾 数据库设计规范

### 表命名规范
- 系统表: `sys_` 前缀 (如: `sys_user`, `sys_role`)
- 业务表: 领域名称 (如: `team`, `content`, `team_member`)
- 关联表: `主表_关联表` (如: `team_recommendation`)

### 字段命名规范
- 主键: `id` 或 `表名_id` (如: `team_id`)
- 外键: `关联表_id` (如: `user_id`, `team_id`)
- 时间字段: `created_at`, `updated_at`, `create_time`, `update_time`
- 状态字段: `status`, `enabled`, `privacy`
- JSON字段: 使用 `JSON` 类型存储复杂数据 (如: `tags`)


## 🌐 API设计规范

### RESTful API规范
- **基础路径**: `/api/v1`
- **HTTP方法**: GET (查询), POST (创建), PUT (更新), DELETE (删除)
- **状态码**: 200 (成功), 201 (创建), 400 (请求错误), 401 (未授权), 403 (禁止), 404 (未找到), 500 (服务器错误)

### 响应格式规范
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### API路径规范
```
个人空间 API:
- GET    /api/v1/users/{userId}/profile     # 获取用户资料
- PUT    /api/v1/users/{userId}/profile     # 更新用户资料
- GET    /api/v1/users/{userId}/contents    # 获取用户内容
- GET    /api/v1/users/{userId}/teams       # 获取用户团队
- GET    /api/v1/users/{userId}/learnings   # 获取学习信息

团队空间 API:
- GET    /api/v1/teams                      # 获取团队列表
- POST   /api/v1/teams                      # 创建团队
- GET    /api/v1/teams/{teamId}             # 获取团队信息
- GET    /api/v1/teams/{teamId}/members     # 获取团队成员
- GET    /api/v1/teams/{teamId}/recommendations # 获取推荐内容
- POST   /api/v1/teams/{teamId}/apply       # 申请加入团队
- POST   /api/v1/teams/recommend            # 推荐内容到团队
```

## 🎨 前端开发规范

### 组件设计规范

#### 1. 组件分类
- **Layout组件**: 布局相关 (Header, Footer, Layout)
- **Common组件**: 通用基础组件 (Toast, Modal, Button)
- **Business组件**: 业务领域组件
  - `personal/`: 个人空间相关组件
  - `team/`: 团队空间相关组件
  - `recommendation/`: 推荐相关组件

#### 2. 组件命名规范
- **PascalCase**: 组件文件名和组件名 (如: `TeamCard.vue`)
- **kebab-case**: 组件使用时 (如: `<team-card>`)
- **领域前缀**: 业务组件使用领域前缀 (如: `TeamContentDisplay`, `PersonalProfile`)

#### 3. 组件结构规范
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
import { ref, computed, onMounted } from 'vue'
// 其他导入

export default {
  name: 'ComponentName',
  components: {
    // 子组件
  },
  props: {
    // 属性定义
  },
  emits: ['event-name'],
  setup(props, { emit }) {
    // 组合式API逻辑
    return {
      // 暴露的响应式数据和方法
    }
  }
}
</script>

<style scoped>
/* 组件样式 */
</style>
```

### 状态管理规范

#### 1. Store分类
- **user.js**: 用户相关状态 (登录状态、用户信息)
- **toast.js**: 消息提示状态
- **notification.js**: 通知状态

#### 2. Store结构规范
```javascript
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 状态定义
  }),
  getters: {
    // 计算属性
  },
  actions: {
    // 方法定义
  }
})
```

### 路由设计规范

#### 1. 路由结构
```javascript
{
  path: '/space',
  children: [
    {
      path: 'personal/:userId',
      name: 'PersonalSpace',
      component: () => import('@/views/space/personal/Profile.vue')
    },
    {
      path: 'team',
      name: 'TeamSpace',
      component: () => import('@/views/space/team/TeamSpace.vue')
    },
    {
      path: 'team/:teamId',
      name: 'TeamSpaceDetail',
      component: () => import('@/views/space/team/TeamSpaceDetail.vue')
    }
  ]
}
```

#### 2. 路由命名规范
- **领域分组**: 按业务领域组织路由
- **层级结构**: 体现页面层级关系
- **参数命名**: 使用有意义的参数名 (如: `:userId`, `:teamId`)

### API服务层规范

#### 1. 服务文件组织
```javascript
// services/userService.js - 个人空间相关API
// services/teamService.js - 团队空间相关API
// services/api.js - 通用API客户端
```

#### 2. 服务类结构
```javascript
class UserService {
  async getUserProfile(userId) {
    return api.get(`/v1/users/${userId}/profile`)
  }

  async updateUserProfile(userId, profileData) {
    return api.put(`/v1/users/${userId}/profile`, profileData)
  }
}

export default new UserService()
```

## ⚙️ 后端开发规范

### 分层架构规范

#### 1. Controller层规范
- **职责**: 接收HTTP请求，参数验证，调用Service，返回响应
- **命名**: `XxxController` (如: `TeamController`, `UserProfileController`)
- **包结构**: 按业务领域分包 (如: `space.controller`, `auth.controller`)

```java
@RestController
@RequestMapping("/api/v1/teams")
@CrossOrigin(origins = "*")
public class TeamController {

    @Autowired
    private TeamService teamService;

    @GetMapping("/{teamId}")
    public Result<TeamProfileDTO> getTeamProfile(@PathVariable Long teamId) {
        // 实现逻辑
    }
}
```

#### 2. Service层规范
- **接口定义**: 定义业务接口 (如: `TeamService`)
- **实现类**: 实现业务逻辑 (如: `TeamServiceImpl`)
- **事务管理**: 使用 `@Transactional` 注解

```java
public interface TeamService {
    TeamProfileDTO getTeamProfile(Long teamId);
    Map<String, Object> createTeam(Map<String, Object> teamData, Long creatorId);
}

@Service
public class TeamServiceImpl implements TeamService {
    @Autowired
    private DataLoader dataLoader;

    // 实现方法
}
```

#### 3. DAO层规范
- **实体类**: 对应数据库表 (如: `Team`, `User`, `Content`)
- **Mapper接口**: 数据访问接口 (如: `TeamMapper`, `UserMapper`)
- **XML映射**: MyBatis SQL映射文件

```java
// 实体类
public class Team {
    private Long teamId;
    private String name;
    private String description;
    // getter/setter
}

// Mapper接口
public interface TeamMapper {
    Team selectById(@Param("teamId") Long teamId);
    List<Team> selectAll();
    int insert(Team team);
}
```

### 数据传输对象规范

#### 1. DTO设计原则
- **领域分离**: 按业务领域设计DTO
- **数据聚合**: 聚合相关数据减少API调用
- **版本兼容**: 考虑向后兼容性

#### 2. DTO命名规范
```java
// 用户相关
UserProfileDTO, UserBasicInfoDTO, UserAchievementsDTO

// 团队相关
TeamProfileDTO, TeamBasicInfoDTO, TeamAchievementsDTO

// 请求DTO
CreateTeamRequest, UpdateProfileRequest
```

### 异常处理规范

#### 1. 异常分类
- **BusinessException**: 业务异常
- **ValidationException**: 参数验证异常
- **AuthenticationException**: 认证异常

#### 2. 全局异常处理
```java
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        return Result.error(e.getMessage());
    }
}
```

## 📝 代码规范

### Java代码规范

#### 1. 命名规范
- **类名**: PascalCase (如: `TeamService`, `UserController`)
- **方法名**: camelCase (如: `getUserProfile`, `createTeam`)
- **常量**: UPPER_SNAKE_CASE (如: `MAX_TEAM_SIZE`)
- **包名**: 小写字母 + 点分隔 (如: `com.jdl.aic.portal.space`)

#### 2. 注释规范
```java
/**
 * 团队空间服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TeamService {

    /**
     * 获取团队基础信息和成就
     *
     * @param teamId 团队ID
     * @return 团队资料DTO
     * @throws BusinessException 当团队不存在时
     */
    TeamProfileDTO getTeamProfile(Long teamId);
}
```

### JavaScript代码规范

#### 1. 命名规范
- **变量/函数**: camelCase (如: `userName`, `getUserProfile`)
- **常量**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- **组件**: PascalCase (如: `TeamCard`, `UserProfile`)

#### 2. 代码组织
```javascript
// 1. 导入
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 2. 组件定义
export default {
  name: 'ComponentName',

  // 3. 组件选项
  components: {},
  props: {},
  emits: [],

  // 4. 组合式API
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)

    // 计算属性
    const computedValue = computed(() => {
      // 计算逻辑
    })

    // 方法
    const handleClick = () => {
      // 处理逻辑
    }

    // 生命周期
    onMounted(() => {
      // 初始化逻辑
    })

    // 返回
    return {
      loading,
      computedValue,
      handleClick
    }
  }
}
```

## 🧪 测试规范

### 后端测试规范

#### 1. 单元测试
- **测试框架**: JUnit 5 + Mockito
- **覆盖率**: 核心业务逻辑 > 80%
- **命名**: `XxxTest` (如: `TeamServiceTest`)

#### 2. 集成测试
- **数据库测试**: 使用 `@DataJpaTest`
- **Web层测试**: 使用 `@WebMvcTest`
- **完整集成**: 使用 `@SpringBootTest`

### 前端测试规范

#### 1. 组件测试
- **测试框架**: Vue Test Utils + Jest
- **测试内容**: 组件渲染、用户交互、数据绑定

#### 2. API测试
- **Mock服务**: 使用 Mock Service Worker
- **测试覆盖**: 成功场景 + 异常场景

## 🚀 部署规范

### 环境配置

#### 1. 开发环境
- **前端**: `npm run dev` (端口: 4000)
- **后端**: `mvn spring-boot:run` (端口: 8001)
- **数据库**: MySQL 本地实例

#### 2. 生产环境
- **前端**: 静态文件部署 (Nginx)
- **后端**: JAR包部署 (Docker)
- **数据库**: MySQL 集群

### 配置管理

#### 1. 环境变量
```yaml
# application-dev.yml
server:
  port: 8001
spring:
  datasource:
    url: *************************************
```

#### 2. 前端环境配置
```javascript
// .env.development
VUE_APP_API_BASE_URL=http://localhost:8001/api

// .env.production
VUE_APP_API_BASE_URL=https://api.example.com/api
```

---

## 📚 附录

### 业务术语表

| 术语 | 英文 | 说明 |
|------|------|------|
| 个人空间 | Personal Space | 用户个人资料和内容管理区域 |
| 团队空间 | Team Space | 团队协作和内容分享区域 |
| 知识类型 | Knowledge Type | 内容分类：prompt/article/tool/course |
| 推荐 | Recommendation | 将内容推荐到团队空间 |
| 成就 | Achievement | 用户或团队的统计数据 |

### 开发流程

1. **需求分析** → 确定业务领域和功能边界
2. **设计阶段** → API设计、数据库设计、UI设计
3. **开发阶段** → 后端开发、前端开发、联调测试
4. **测试阶段** → 单元测试、集成测试、端到端测试
5. **部署上线** → 环境配置、部署验证、监控告警

### 版本管理

- **主分支**: `main` - 生产环境代码
- **开发分支**: `develop` - 开发环境代码
- **功能分支**: `feature/功能名称` - 新功能开发
- **修复分支**: `hotfix/问题描述` - 紧急修复

---

## 🚀 快速开始指南

### 环境要求

#### 开发环境
- **Java**: JDK 8+
- **Node.js**: 16.x+
- **MySQL**: 8.0+
- **Maven**: 3.6+
- **Git**: 2.x+

#### IDE推荐
- **后端**: IntelliJ IDEA Ultimate
- **前端**: VS Code + Vetur插件

### 项目启动步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd portal_space
```

#### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE ai_portal CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入初始化脚本
mysql -u root -p ai_portal < backend/init_personal_team_space.sql
```

#### 3. 后端启动
```bash
cd backend/web
mvn spring-boot:run
```
后端服务将在 http://localhost:8001 启动

#### 4. 前端启动
```bash
cd frontend
npm install
npm run dev
```
前端服务将在 http://localhost:4000 启动

#### 5. 验证部署
- 访问 http://localhost:4000 查看前端页面
- 访问 http://localhost:8001/api/v1/teams/1 测试API

### 常见问题

#### Q: 端口冲突怎么办？
A: 修改配置文件中的端口号
- 后端: `backend/web/src/main/resources/application-dev.yml`
- 前端: `frontend/package.json` 中的 `--port` 参数

#### Q: 数据库连接失败？
A: 检查 `application-dev.yml` 中的数据库配置

#### Q: 前端API调用失败？
A: 检查 `frontend/src/services/api.js` 中的 `baseURL` 配置

### 开发工作流

#### 1. 新功能开发
```bash
# 创建功能分支
git checkout -b feature/新功能名称

# 开发完成后提交
git add .
git commit -m "feat: 添加新功能描述"

# 推送分支
git push origin feature/新功能名称
```

#### 2. 代码规范检查
```bash
# 前端代码检查
cd frontend
npm run lint

# 后端代码检查 (使用IDE内置工具)
```

#### 3. 测试验证
```bash
# 前端测试
cd frontend
npm run test

# 后端测试
cd backend
mvn test
```

---

## 🔄 模块重构计划

### 当前状态分析

目前项目的模块组织还不完全符合领域驱动设计的要求，存在以下问题：

1. **Service层混合**: 所有服务接口和实现都在同一个包下，没有按领域分离
2. **Controller层集中**: 控制器按功能而非领域组织
3. **DTO分散**: 数据传输对象没有按领域归类

### 重构目标

按照领域驱动设计原则，将代码按业务领域重新组织：

#### 阶段一：Service层重构
```bash
# 当前结构
service/src/main/java/com/jdl/aic/portal/
├── space/service/TeamService.java
├── space/service/impl/TeamServiceImpl.java
└── auth/service/AuthService.java

# 目标结构
service/src/main/java/com/jdl/aic/portal/
├── space/
│   ├── service/
│   │   ├── TeamService.java
│   │   ├── UserProfileService.java
│   │   └── TeamMemberService.java
│   └── service/impl/
│       ├── TeamServiceImpl.java
│       ├── UserProfileServiceImpl.java
│       └── TeamMemberServiceImpl.java
├── content/
│   ├── service/
│   │   ├── ContentService.java
│   │   └── CategoryService.java
│   └── service/impl/
│       ├── ContentServiceImpl.java
│       └── CategoryServiceImpl.java
├── recommendation/
│   ├── service/
│   │   └── RecommendationService.java
│   └── service/impl/
│       └── RecommendationServiceImpl.java
└── auth/
    ├── service/
    │   └── AuthService.java
    └── service/impl/
        └── AuthServiceImpl.java
```

#### 阶段二：Controller层重构
```bash
# 当前结构
web/src/main/java/com/jdl/aic/portal/
├── space/controller/TeamController.java
└── auth/controller/AuthController.java

# 目标结构
web/src/main/java/com/jdl/aic/portal/
├── space/
│   ├── controller/
│   │   ├── TeamController.java
│   │   ├── UserProfileController.java
│   │   └── TeamMemberController.java
│   └── dto/
│       ├── TeamDTO.java
│       ├── UserProfileDTO.java
│       └── TeamMemberDTO.java
├── content/
│   ├── controller/
│   │   └── ContentController.java
│   └── dto/
│       └── ContentDTO.java
├── recommendation/
│   ├── controller/
│   │   └── RecommendationController.java
│   └── dto/
│       └── RecommendationDTO.java
└── auth/
    ├── controller/
    │   └── AuthController.java
    └── dto/
        └── AuthDTO.java
```

#### 阶段三：前端模块重构
```bash
# 目标结构
frontend/src/
├── domains/                  # 按领域组织
│   ├── space/               # 空间领域
│   │   ├── components/      # 空间相关组件
│   │   ├── views/           # 空间页面
│   │   ├── services/        # 空间API服务
│   │   └── stores/          # 空间状态管理
│   ├── content/             # 内容领域
│   │   ├── components/
│   │   ├── views/
│   │   ├── services/
│   │   └── stores/
│   └── recommendation/      # 推荐领域
│       ├── components/
│       ├── views/
│       ├── services/
│       └── stores/
└── shared/                  # 共享模块
    ├── components/          # 通用组件
    ├── utils/               # 工具函数
    └── constants/           # 常量定义
```

### 重构实施步骤

1. **第一步**: 创建新的目录结构
2. **第二步**: 逐步迁移现有代码到新结构
3. **第三步**: 更新导入和依赖关系
4. **第四步**: 更新测试用例
5. **第五步**: 更新文档和配置

### 重构收益

- **清晰的边界**: 每个领域有明确的职责边界
- **高内聚**: 相关功能聚合在同一个领域内
- **低耦合**: 不同领域之间依赖最小化
- **易维护**: 新功能开发和bug修复更容易定位
- **可扩展**: 新领域可以独立添加而不影响现有代码

---

*本规范文档将随着项目发展持续更新和完善。*

**文档版本**: v1.1.0
**最后更新**: 2024-01-20
**维护者**: AI Portal 开发团队
