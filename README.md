# AIC Portal

<div align="center">
  <img src="https://img.shields.io/badge/Vue-3.3.0-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white" alt="Vue 3">
  <img src="https://img.shields.io/badge/Spring%20Boot-2.7.18-6DB33F?style=for-the-badge&logo=spring-boot&logoColor=white" alt="Spring Boot">
  <img src="https://img.shields.io/badge/MySQL-8.0-4479A1?style=for-the-badge&logo=mysql&logoColor=white" alt="MySQL">
  <img src="https://img.shields.io/badge/JWT-Authentication-000000?style=for-the-badge&logo=JSON%20web%20tokens&logoColor=white" alt="JWT">
  <img src="https://img.shields.io/badge/OAuth2-Google%20%7C%20GitHub-4285F4?style=for-the-badge&logo=oauth&logoColor=white" alt="OAuth2">
</div>

<div align="center">
  <h3>🚀 现代化的AI社区门户系统</h3>
  <p>基于Vue 3 + Spring Boot的全栈Web应用，支持完整的用户认证、OAuth第三方登录和JWT令牌管理</p>
</div>

## 📋 目录

- [✨ 项目特性](#-项目特性)
- [🏗️ 系统架构](#️-系统架构)
- [🚀 快速开始](#-快速开始)
- [🔧 环境配置](#-环境配置)
- [📁 项目结构](#-项目结构)
- [🔐 认证系统](#-认证系统)
- [🛡️ 安全特性](#️-安全特性)
- [📊 数据库设计](#-数据库设计)
- [🔌 API文档](#-api文档)
- [🎨 UI组件](#-ui组件)
- [🧪 测试](#-测试)
- [🚀 部署](#-部署)
- [📖 开发指南](#-开发指南)
- [🤝 贡献指南](#-贡献指南)
- [📄 许可证](#-许可证)

## ✨ 项目特性

### 🔐 完整的用户认证系统
- **本地认证**: 用户注册/登录，BCrypt密码加密
- **OAuth登录**: 支持Google、GitHub第三方登录
- **JWT令牌**: 完整的令牌生命周期管理
- **会话管理**: 多设备登录控制，令牌撤销机制
- **安全审计**: 登录日志记录，设备指纹识别

### 🎨 现代化前端界面
- **Vue 3**: 基于Composition API的响应式设计
- **Pinia**: 现代化状态管理
- **路由守卫**: 完整的页面权限控制
- **UI/UX**: 渐变背景，优雅动画，移动端适配
- **组件化**: 可复用的UI组件库

### 🏗️ 企业级后端架构
- **Spring Boot**: 2.7.18版本，生产就绪
- **多模块设计**: 分层架构，职责清晰
- **MyBatis**: 灵活的ORM框架
- **异常处理**: 全局异常处理和统一响应格式
- **配置管理**: 环境隔离，配置外部化

### 🛡️ 安全防护
- **JWT令牌**: 无状态认证，支持令牌撤销
- **OAuth2**: 标准化第三方登录流程
- **密码加密**: BCrypt强加密算法
- **CORS配置**: 跨域安全策略
- **SQL注入防护**: MyBatis参数化查询

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   Vue 3 + Pinia │◄──►│   Spring Boot   │◄──►│   MySQL 8.0     │
│   Port: 4000    │    │   Port: 8000    │    │   Port: 3306    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌─────────┐              ┌─────────┐            ┌─────────┐
    │ Router  │              │ Security│            │ MyBatis │
    │ Guards  │              │ + JWT   │            │ Mappers │
    └─────────┘              └─────────┘            └─────────┘
         │                       │                       │
    ┌─────────┐              ┌─────────┐            ┌─────────┐
    │ Stores  │              │ OAuth2  │            │ Entities│
    │ (Pinia) │              │ Google  │            │ + DTOs  │
    └─────────┘              │ GitHub  │            └─────────┘
                             └─────────┘
```

## 🚀 快速开始

### 前置要求
- **Java**: JDK 8+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Maven**: 3.6+
- **Git**: 最新版本

### 一键启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd aic_portal

# 2. 初始化数据库
mysql -u root -p < backend/init.sql

# 3. 启动服务（推荐）
chmod +x start-dev.sh
./start-dev.sh
```

### 手动启动
```bash
# 后端服务
cd backend/web
mvn spring-boot:run

# 前端服务（新终端）
cd frontend
npm install
npm run dev
```

### 访问应用
- 🌐 **前端应用**: http://localhost:4000
- 🔐 **登录页面**: http://localhost:4000/login
- 📡 **后端API**: http://localhost:8000
- 📊 **API文档**: http://localhost:8000/doc.html

## 🔧 环境配置

### 数据库配置
```yaml
spring:
  datasource:
    url: ***************************************
    username: root
    password: root
```

### OAuth配置
```yaml
oauth:
  google:
    client-id: YOUR_GOOGLE_CLIENT_ID
    client-secret: YOUR_GOOGLE_CLIENT_SECRET
    redirect-uri: http://localhost:8000/api/auth/oauth/google/callback
  github:
    client-id: YOUR_GITHUB_CLIENT_ID
    client-secret: YOUR_GITHUB_CLIENT_SECRET
    redirect-uri: http://localhost:8000/api/auth/oauth/github/callback
```

### JWT配置
```yaml
jwt:
  secret: bXlTZWNyZXRLZXlGb3JKV1RUb2tlbjEyMzQ1Njc4OTA=
  expiration: 86400  # 24小时
```

## 📁 项目结构

```
aic_portal/
├── backend/                    # 后端模块
│   ├── common/                # 公共组件
│   │   ├── config/           # 配置类
│   │   ├── dto/              # 数据传输对象
│   │   ├── exception/        # 异常处理
│   │   ├── result/           # 统一响应
│   │   └── utils/            # 工具类
│   ├── dao/                  # 数据访问层
│   │   ├── entity/           # 实体类
│   │   ├── mapper/           # MyBatis接口
│   │   └── resources/mapper/ # MyBatis XML
│   ├── service/              # 业务逻辑层
│   │   ├── auth/            # 认证服务
│   │   ├── jwt/             # JWT令牌管理
│   │   ├── oauth/           # OAuth服务
│   │   └── task/            # 定时任务
│   ├── web/                  # Web层
│   │   ├── controller/       # 控制器
│   │   ├── exception/        # 全局异常处理
│   │   └── resources/        # 配置文件
│   ├── init.sql              # 数据库初始化
│   ├── OAUTH_SETUP.md        # OAuth配置指南
│   └── README.md             # 后端README
├── frontend/                  # 前端模块
│   ├── public/               # 静态资源
│   ├── src/
│   │   ├── assets/          # 资源文件
│   │   ├── components/      # 组件
│   │   ├── router/          # 路由配置
│   │   ├── stores/          # 状态管理
│   │   ├── utils/           # 工具函数
│   │   └── views/           # 页面组件
│   ├── package.json         # 依赖配置
│   └── README.md            # 前端README
├── start-dev.sh             # 开发启动脚本
├── CLAUDE.md                # Claude Code指南
└── README.md                # 项目主README
```

## 🔐 认证系统

### 认证流程
1. **用户注册/登录** → 生成JWT令牌
2. **令牌存储** → 数据库持久化，设备信息记录
3. **自动验证** → 前端路由守卫，后端拦截器
4. **令牌管理** → 撤销机制，过期清理，会话限制

### OAuth集成
```javascript
// 前端OAuth登录
const handleGoogleLogin = async () => {
  const data = await ApiClient.get('/api/auth/oauth/google')
  window.location.href = data.data // 跳转到Google授权页面
}
```

### 路由守卫
```javascript
// 自动检查认证状态
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  await userStore.initialize()
  
  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})
```

## 🛡️ 安全特性

### JWT令牌管理
- **唯一标识**: 每个令牌包含UUID标识
- **数据库追踪**: 令牌状态实时同步
- **撤销机制**: 登出时立即撤销令牌
- **过期清理**: 定时任务清理过期令牌
- **会话限制**: 防止令牌滥用

### 密码安全
```java
// BCrypt加密存储
String hashedPassword = passwordUtil.encode(rawPassword);
boolean matches = passwordUtil.matches(rawPassword, hashedPassword);
```

### 设备指纹
```java
// 设备信息采集
String deviceInfo = getDeviceInfo(request);
String ipAddress = getClientIpAddress(request);
```

## 📊 数据库设计

### 核心表结构
```sql
-- 用户表
sys_user (id, username, password, email, provider, ...)

-- JWT令牌表
sys_jwt_token (id, user_id, token_id, token_value, expires_at, ...)

-- 登录日志表
sys_login_log (id, user_id, login_type, ip_address, login_status, ...)

-- OAuth信息表
sys_user_oauth (id, user_id, provider, access_token, ...)
```

### 索引优化
- 用户名、邮箱唯一索引
- 令牌ID、过期时间索引
- 登录时间、状态索引

## 🔌 API文档

### 认证接口
```http
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册
POST /api/auth/logout         # 用户登出
GET  /api/auth/user           # 获取用户信息
GET  /api/auth/oauth/google   # Google OAuth授权
GET  /api/auth/oauth/github   # GitHub OAuth授权
```

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "username": "john_doe",
    "email": "<EMAIL>"
  }
}
```

## 🎨 UI组件

### 核心组件
- **Header**: 导航栏，用户信息，通知
- **Footer**: 页脚信息
- **Toast**: 消息提示
- **LoginPrompt**: 登录引导
- **BackToTop**: 返回顶部

### 样式特性
- 渐变背景设计
- 响应式布局
- 优雅动画效果
- 统一设计语言

## 🧪 测试

### 后端测试
```bash
cd backend
mvn test                      # 运行所有测试
mvn test -Dtest=AuthServiceTest # 运行特定测试
```

### 前端测试
```bash
cd frontend
npm run test                  # 运行单元测试
npm run test:e2e             # 运行端到端测试
```

### 手动测试
1. **认证流程测试**
   - 用户注册/登录
   - OAuth第三方登录
   - 令牌验证和撤销

2. **权限控制测试**
   - 路由守卫
   - API访问控制
   - 登录状态维护

## 🚀 部署

### 开发环境
```bash
./start-dev.sh
```

### 生产环境
```bash
# 前端构建
cd frontend
npm run build

# 后端打包
cd backend
mvn clean package

# 部署到服务器
java -jar web/target/web-1.0.0.jar
```

### Docker部署
```dockerfile
# 构建镜像
docker build -t ai-portal .

# 运行容器
docker run -p 8000:8000 ai-portal
```

## 📖 开发指南

### 代码规范
- **Java**: 遵循Spring Boot最佳实践
- **JavaScript**: 使用ESLint + Prettier
- **Git**: 使用Conventional Commits规范

### 开发流程
1. 创建功能分支
2. 开发功能
3. 编写测试
4. 代码审查
5. 合并主分支

### 调试技巧
- **后端**: 使用IDE调试，查看日志
- **前端**: 使用Vue DevTools
- **数据库**: 使用MyBatis SQL日志

## 🤝 贡献指南

### 参与贡献
1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 问题反馈
- 🐛 [Bug Report](https://github.com/your-repo/issues/new?template=bug_report.md)
- 💡 [Feature Request](https://github.com/your-repo/issues/new?template=feature_request.md)

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

<div align="center">
  <p><strong>Built with ❤️ by the AIC Portal Team</strong></p>
  <p>
    <a href="#aic-portal">回到顶部</a> |
    <a href="mailto:<EMAIL>">联系我们</a> |
    <a href="https://github.com/your-repo">GitHub</a>
  </p>
</div>