<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>AI Portal API 测试</h1>
    
    <div>
        <button onclick="testTeamProfile(1)">测试团队1信息</button>
        <button onclick="testTeamProfile(2)">测试团队2信息</button>
        <button onclick="testTeamMembers(2)">测试团队2成员</button>
        <button onclick="testTeamRecommendations(2)">测试团队2推荐</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:8001/api';
        
        async function makeRequest(url) {
            try {
                console.log('请求URL:', url);
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('响应数据:', data);
                return data;
            } catch (error) {
                console.error('请求失败:', error);
                throw error;
            }
        }
        
        function displayResult(title, data) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testTeamProfile(teamId) {
            try {
                const data = await makeRequest(`${API_BASE}/v1/teams/${teamId}`);
                displayResult(`团队${teamId}信息`, data);
            } catch (error) {
                displayResult(`团队${teamId}信息 - 错误`, { error: error.message });
            }
        }
        
        async function testTeamMembers(teamId) {
            try {
                const data = await makeRequest(`${API_BASE}/v1/teams/${teamId}/members`);
                displayResult(`团队${teamId}成员`, data);
            } catch (error) {
                displayResult(`团队${teamId}成员 - 错误`, { error: error.message });
            }
        }
        
        async function testTeamRecommendations(teamId) {
            try {
                const data = await makeRequest(`${API_BASE}/v1/teams/${teamId}/recommendations`);
                displayResult(`团队${teamId}推荐`, data);
            } catch (error) {
                displayResult(`团队${teamId}推荐 - 错误`, { error: error.message });
            }
        }
    </script>
</body>
</html>
