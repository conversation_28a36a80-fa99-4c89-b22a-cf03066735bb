#!/bin/bash

# AI Portal Vue 应用启动脚本

echo "🚀 启动 AI Portal Vue 应用..."

# 检查是否在 vue-app 目录中
if [ ! -f "package.json" ]; then
    echo "❌ 请在 vue-app 目录中运行此脚本"
    exit 1
fi

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 请先安装 Node.js"
    exit 1
fi

# 检查是否需要安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 启动开发服务器
echo "🔧 启动开发服务器..."
echo "📱 应用将在 http://localhost:8080 启动"
echo "🛑 按 Ctrl+C 停止服务器"

npm run serve