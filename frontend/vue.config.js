const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 开发服务器配置
  devServer: {
    port: 4000,
    open: true,
    hot: true
  },
  
  // 生产构建配置
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  
  // CSS 相关配置
  css: {
    loaderOptions: {
      sass: {
        // 使用新的Sass API
        api: 'modern',
        // 如果有全局变量或混合宏，可以在这里导入
        // additionalData: `@import "@/assets/styles/variables.scss";`
      }
    }
  },
  
  // 配置别名和特性标志
  configureWebpack: {
    resolve: {
      alias: {
        '@': require('path').resolve(__dirname, 'src')
      }
    },
    plugins: [
      new (require('webpack')).DefinePlugin({
        __VUE_OPTIONS_API__: true,
        __VUE_PROD_DEVTOOLS__: false,
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
      })
    ]
  }
})