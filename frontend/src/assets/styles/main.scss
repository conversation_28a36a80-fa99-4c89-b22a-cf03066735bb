/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部导航 */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 60px;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 40px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    font-weight: bold;
    color: #4f46e5;
    text-decoration: none;
}

.logo i {
    font-size: 24px;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-item {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
    cursor: pointer;
}

.nav-item:hover,
.nav-item.active {
    color: #4f46e5;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: #4f46e5;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    display: flex;
    align-items: center;
    background: #f3f4f6;
    border-radius: 8px;
    padding: 0 15px;
    width: 300px;
}

.search-box i {
    color: #9ca3af;
    margin-right: 10px;
}

.search-box input {
    border: none;
    background: none;
    outline: none;
    padding: 12px 0;
    width: 100%;
    font-size: 13px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info i {
    font-size: 18px;
    color: #666;
    cursor: pointer;
}

.notification-badge {
    background: #ef4444;
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    position: relative;
    top: -5px;
    left: -10px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* 主要内容区域 */
.main-content {
    padding-top: 20px;
}

/* 英雄区域 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-subtitle {
    font-size: 16px;
    margin-bottom: 20px;
    opacity: 0.9;
}

.hero-title {
    font-size: 40px;
    font-weight: bold;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-description {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-search {
    display: flex;
    max-width: 600px;
    margin: 0 auto 40px;
    background: white;
    border-radius: 12px;
    padding: 5px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.hero-search input {
    flex: 1;
    border: none;
    padding: 15px 20px;
    font-size: 16px;
    border-radius: 8px;
    outline: none;
    color: #333;
}

.search-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background: #3730a3;
}

/* 统计数据 */
.stats-container {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 13px;
    opacity: 0.8;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background: #3730a3;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

.btn-outline {
    background: transparent;
    color: #4f46e5;
    border: 2px solid #4f46e5;
}

.btn-outline:hover {
    background: #4f46e5;
    color: white;
}

.hero-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
}

/* 分类标签 */
.category-section {
    background: white;
    padding: 30px 0;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.category-tabs {
    display: flex;
    gap: 8px;
    align-items: center;
    overflow-x: auto;
    padding: 15px 0;
    margin-bottom: 20px;
    
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }
}

.tab-btn {
    background: #f8f9fa;
    border: 1px solid #e5e7eb;
    padding: 12px 24px;
    border-radius: 30px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    color: #374151;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: fit-content;
    flex-shrink: 0;
    
    &:hover {
        background: #f1f3f4;
        border-color: #d1d5db;
        transform: translateY(-1px);
    }
}

.tab-btn.active {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
    
    &:hover {
        background: #3730a3;
        border-color: #3730a3;
    }
}

.count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.tab-btn:not(.active) .count {
    background: #e5e7eb;
    color: #6b7280;
}

.category-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-size: 13px;
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 13px;
    cursor: pointer;
    outline: none;
    transition: all 0.2s ease;
    
    &:hover {
        border-color: #9ca3af;
    }
    
    &:focus {
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
}

/* 控制面板 */
.control-panel {
    background: white;
    padding: 20px 0;
    border-bottom: 1px solid #e5e7eb;
}

.control-panel .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.control-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.control-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sort-select {
    background: #f3f4f6;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    outline: none;
}

.view-toggle {
    display: flex;
    background: #f3f4f6;
    border-radius: 6px;
    padding: 4px;
}

.view-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    color: #666;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: white;
    color: #4f46e5;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 内容区域 */
.content-section {
    background: #f8f9fa;
    padding: 40px 0;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
    
    i {
        color: #4f46e5;
        font-size: 24px;
    }
}

.section-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin-left: 36px;
    margin-bottom: 30px;
    font-weight: 400;
}

.featured-section {
    background: white;
    padding: 50px 0;
    margin-bottom: 40px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.featured-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 20px;
}

.popular-section {
    background: white;
    padding: 50px 0;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.prompts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.prompts-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.prompts-list .prompt-card {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    min-height: 180px;
}

.prompts-list .card-content {
    flex: 1;
}

.prompts-list .card-actions {
    flex-shrink: 0;
    align-self: flex-end;
}

.prompt-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-width: 400px;
    min-height: 160px;
    display: flex;
    flex-direction: column;
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #4f46e5, #7c3aed);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
    }
    
    &:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transform: translateY(-4px);
        border-color: #d1d5db;
        
        &::before {
            transform: scaleX(1);
        }
    }
}

.featured-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    min-width: 400px;
    min-height: 160px;
    
    .card-title {
        color: white;
        font-size: 18px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 260px;
    }
    
    .card-description {
        color: rgba(255, 255, 255, 0.9);
    }
    
    .tag {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
        flex-shrink: 0;
    }
    
    .model-tag {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
        flex-shrink: 0;
    }
    
    .card-stats {
        color: rgba(255, 255, 255, 0.9);
    }
    
    .author-info {
        color: rgba(255, 255, 255, 0.8);
        max-width: 120px;
    }
    
    .author-avatar {
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    min-height: 40px;
}

.card-title {
    font-size: 17px;
    font-weight: 600;
    color: #111827;
    margin: 0;
    line-height: 1.3;
    flex: 1;
    margin-right: 12px;
    
    /* 防止换行，超出显示省略号 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 260px;
}

.card-badge {
    background: #fef3c7;
    color: #d97706;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
    white-space: nowrap;
}

.card-badge:contains("精选") {
    background: #fef2f2;
    color: #dc2626;
}

.card-badge:contains("热门") {
    background: #fff7ed;
    color: #ea580c;
}

.card-badge:contains("推荐") {
    background: #f0f9ff;
    color: #0369a1;
}

.card-description {
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex-grow: 1;
}

.card-tags {
    display: flex;
    gap: 6px;
    margin-bottom: 10px;
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    
    &::-webkit-scrollbar {
        display: none;
    }
}

.tag {
    background: #f3f4f6;
    color: #374151;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
    
    &:hover {
        background: #e5e7eb;
        border-color: #d1d5db;
    }
}

.card-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: nowrap;
    gap: 8px;
}

.model-info {
    display: flex;
    gap: 4px;
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    
    &::-webkit-scrollbar {
        display: none;
    }
}

.model-tag {
    background: #eff6ff;
    color: #1d4ed8;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
    border: 1px solid #dbeafe;
    white-space: nowrap;
    flex-shrink: 0;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #6b7280;
    flex-shrink: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.author-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    color: #6b7280;
    flex-shrink: 0;
}

.card-stats {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 10px;
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    
    &::-webkit-scrollbar {
        display: none;
    }
}

.stat {
    display: flex;
    align-items: center;
    gap: 3px;
    white-space: nowrap;
    flex-shrink: 0;
    
    i {
        font-size: 12px;
        color: #9ca3af;
    }
}

.card-actions {
    display: flex;
    gap: 6px;
    flex-wrap: nowrap;
    align-items: center;
    margin-top: auto;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    
    &::-webkit-scrollbar {
        display: none;
    }
}

.btn {
    padding: 6px 12px;
    border-radius: 5px;
    border: none;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    white-space: nowrap;
    flex-shrink: 0;
    
    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.btn-primary {
    background: #4f46e5;
    color: white;
    
    &:hover:not(:disabled) {
        background: #3730a3;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    }
}

.btn-secondary {
    background: #f8f9fa;
    color: #374151;
    border: 1px solid #e5e7eb;
    
    &:hover:not(:disabled) {
        background: #f1f3f4;
        border-color: #d1d5db;
    }
}

.like-btn {
    &.liked {
        background: #fef2f2;
        color: #dc2626;
        border-color: #fecaca;
        
        &:hover {
            background: #fee2e2;
            border-color: #fca5a5;
        }
    }
}

.collect-btn {
    &.bookmarked {
        background: #fef7ed;
        color: #ea580c;
        border-color: #fed7aa;
        
        &:hover {
            background: #fef3c7;
            border-color: #fde68a;
        }
    }
}

.copy-to-space-btn {
    &:hover {
        background: #f0f9ff;
        color: #0369a1;
        border-color: #bae6fd;
    }
}

.share-btn {
    &:hover {
        background: #f0fdf4;
        color: #16a34a;
        border-color: #bbf7d0;
    }
}

.like-count {
    font-size: 11px;
    font-weight: 600;
}

.load-more {
    text-align: center;
    margin-top: 40px;
}

.load-more-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.load-more-btn:hover {
    background: #3730a3;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .prompts-grid {
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 20px;
    }
    
    .featured-cards {
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 1024px) {
    .prompts-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
    }
    
    .featured-cards {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
    }
    
    .category-filters {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .card-title {
        font-size: 17px;
        max-width: 250px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }
    
    .nav-menu {
        display: none;
    }

    .search-box {
        width: 200px;
    }

    .hero-title {
        font-size: 28px;
    }

    .stats-container {
        flex-wrap: wrap;
        gap: 20px;
    }

    .category-tabs {
        gap: 8px;
    }
    
    .tab-btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .category-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
        gap: 6px;
    }
    
    .filter-group select {
        width: 100%;
    }

    .prompts-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .featured-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .prompt-card {
        padding: 20px;
        min-width: unset;
    }
    
    .card-title {
        font-size: 16px;
        max-width: 200px;
    }
    
    .card-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .author-info {
        max-width: 100%;
    }
    
    .card-stats {
        gap: 8px;
    }
    
    .card-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .card-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .section-title {
        font-size: 24px;
    }
    
    .section-subtitle {
        font-size: 13px;
        margin-left: 30px;
    }
}

@media (max-width: 480px) {
    .hero-search {
        flex-direction: column;
        gap: 8px;
    }
    
    .hero-search input {
        width: 100%;
    }
    
    .hero-search .search-btn {
        width: 100%;
    }
    
    .hero-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .hero-actions .btn {
        width: 100%;
    }
    
    .prompt-card {
        min-height: 160px;
    }
    
    .card-title {
        font-size: 15px;
        max-width: 180px;
    }
    
    .card-tags {
        gap: 4px;
    }
    
    .tag {
        padding: 3px 8px;
        font-size: 11px;
    }
    
    .model-tag {
        padding: 2px 6px;
        font-size: 10px;
    }
    
    .card-stats {
        gap: 8px;
        font-size: 13px;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 40px;
    color: #6b7280;
}

.spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #4f46e5;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    background: #ef4444;
}

.toast.warning {
    background: #f59e0b;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-title {
    font-size: 20px;
    font-weight: 600;
    color: #111827;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
}

.modal-content {
    margin-bottom: 20px;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #374151;
}

.form-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 13px;
    outline: none;
    transition: border-color 0.2s ease;
}

.form-input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 13px;
    outline: none;
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
    transition: border-color 0.2s ease;
}

.form-textarea:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-select {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 13px;
    outline: none;
    background: white;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.form-select:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 页脚 */
.footer {
    background: #111827;
    color: white;
    padding: 40px 0 20px;
    margin-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 15px;
    font-size: 16px;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 8px;
}

.footer-section a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 20px;
    text-align: center;
    color: #9ca3af;
}

/* 回到顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: #4f46e5;
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top:hover {
    background: #3730a3;
    transform: translateY(-2px);
}

.back-to-top.hidden {
    opacity: 0;
    visibility: hidden;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease;
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    background-color: #555;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 5px 10px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 徽章 */
.badge {
    display: inline-block;
    padding: 4px 8px;
    background: #ef4444;
    color: white;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.badge.success {
    background: #10b981;
}

.badge.warning {
    background: #f59e0b;
}

.badge.info {
    background: #3b82f6;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 40px 0;
}

.pagination-btn {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.pagination-btn:hover {
    background: #f3f4f6;
}

.pagination-btn.active {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.3;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #374151;
}

.empty-state-description {
    font-size: 16px;
    margin-bottom: 20px;
}

/* 搜索结果高亮 */
.highlight {
    background: #fef3c7;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 500;
}

/* 进度条 */
.progress {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #4f46e5;
    transition: width 0.3s ease;
}

/* 开关按钮 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 28px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 28px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4f46e5;
}

input:checked + .slider:before {
    transform: translateX(22px);
}

/* 标签输入 */
.tag-input {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    min-height: 44px;
    align-items: center;
}

.tag-input input {
    border: none;
    outline: none;
    flex: 1;
    min-width: 120px;
    font-size: 13px;
}

.tag-input .tag {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #4f46e5;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.tag-input .tag-remove {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 12px;
    padding: 0;
    margin-left: 4px;
}

/* 卡片网格布局 */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.card-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.card-list .prompt-card {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
}

.card-list .card-content {
    flex: 1;
}

.card-list .card-actions {
    flex-shrink: 0;
}

/* 骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, transparent 37%, transparent 63%, #f0f0f0 75%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0 50%;
    }
}

.skeleton-text {
    height: 16px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-text:last-child {
    margin-bottom: 0;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
}

/* 打字机效果 */
.typewriter {
    overflow: hidden;
    white-space: nowrap;
    border-right: 2px solid #4f46e5;
    animation: typing 3s steps(40, end), blink 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    50% { border-color: transparent; }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 选择文本样式 */
::selection {
    background-color: #4f46e5;
    color: white;
}

::-moz-selection {
    background-color: #4f46e5;
    color: white;
}

/* 焦点样式 */
.focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        --bg-color: #1f2937;
        --text-color: #f9fafb;
        --card-bg: #374151;
        --border-color: #4b5563;
    }
}

/* 订阅中心专用样式 */
.subscription-center {
    // 平滑滚动
    scroll-behavior: smooth;

    // 自定义滚动条
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 4px;

        &:hover {
            background: #94a3b8;
        }
    }

    // 加载动画
    .fade-enter-active,
    .fade-leave-active {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .fade-enter-from {
        opacity: 0;
        transform: translateY(20px);
    }

    .fade-leave-to {
        opacity: 0;
        transform: translateY(-20px);
    }

    // 悬浮效果
    .hover-lift {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
    }

    // 脉冲动画
    .pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
    }

    // 播放指示器
    .playing-indicator {
        position: relative;

        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 4px;
            height: 4px;
            background: #f59e0b;
            border-radius: 50%;
            animation: playing-pulse 1s infinite;
        }
    }

    @keyframes playing-pulse {
        0%, 100% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
        50% {
            transform: translate(-50%, -50%) scale(1.5);
            opacity: 0.7;
        }
    }

    // 响应式网格
    .responsive-grid {
        display: grid;
        gap: 1rem;

        &.auto-fit {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        &.auto-fill {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        }
    }

    // 卡片样式增强
    .enhanced-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;

        &:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        &.active {
            box-shadow: 0 0 0 2px #4f46e5;
        }
    }

    // 按钮增强
    .enhanced-btn {
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        &:active::before {
            width: 300px;
            height: 300px;
        }
    }
}