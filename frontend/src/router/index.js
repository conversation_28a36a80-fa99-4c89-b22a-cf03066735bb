import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import MyPrompts from '../views/MyPrompts.vue'
import TeamSpace from '../views/TeamSpace.vue'
import Tools from '../views/Tools.vue'
import Auth from '../views/Auth.vue'
import Login from '../views/Login.vue'
import OAuthCallback from '../views/OAuthCallback.vue'
import Profile from '../views/Profile.vue'
import PromptDetail from '../views/PromptDetail.vue'
import ToolDetail from '../views/ToolDetail.vue'
import Search from '../views/Search.vue'
import PromptEdit from '../views/PromptEdit.vue'
import NotFound from '../views/NotFound.vue'
import Knowledge from '../views/Knowledge.vue'
import KnowledgeTypeGrid from '../views/KnowledgeTypeGrid.vue'
import KnowledgeList from '../views/KnowledgeList.vue'
import KnowledgeDetail from '../views/KnowledgeDetail.vue'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/auth/callback/:provider',
    name: 'OAuthCallback',
    component: OAuthCallback
  },
  {
    path: '/my-prompts',
    name: 'MyPrompts',
    component: MyPrompts,
    meta: { requiresAuth: true }
  },
  {
    path: '/team-space',
    name: 'TeamSpace',
    component: TeamSpace,
    meta: { requiresAuth: true }
  },
  {
    path: '/tools',
    name: 'Tools',
    component: Tools,
    meta: { requiresAuth: true }
  },
  {
    path: '/community-test',
    name: 'CommunityTest',
    component: () => import('../views/CommunityTest.vue')
  },
  {
    path: '/auth',
    name: 'Auth',
    component: Auth
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/prompt/:id',
    name: 'PromptDetail',
    component: PromptDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/tool/:id',
    name: 'ToolDetail',
    component: ToolDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/search',
    name: 'Search',
    component: Search,
    meta: { requiresAuth: true }
  },
  {
    path: '/prompt-edit/:id?',
    name: 'PromptEdit',
    component: PromptEdit,
    meta: { requiresAuth: true }
  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: Knowledge,
    meta: { requiresAuth: true }
  },
  {
    path: '/knowledge-types',
    name: 'KnowledgeTypeGrid',
    component: KnowledgeTypeGrid,
    meta: { requiresAuth: true }
  },
  {
    path: '/knowledge/:type',
    name: 'KnowledgeList',
    component: KnowledgeList,
    meta: { requiresAuth: true }
  },
  {
    path: '/knowledge/:type/:id',
    name: 'KnowledgeDetail',
    component: KnowledgeDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: { requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（比如通过浏览器后退/前进按钮），返回到该位置
    if (savedPosition) {
      return savedPosition
    }
    // 否则滚动到页面顶部
    return { top: 0 }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 首先初始化用户状态
  await userStore.initialize()
  
  // 需要认证的路由
  if (to.meta.requiresAuth) {
    if (!userStore.isAuthenticated) {
      // 保存用户尝试访问的页面
      localStorage.setItem('redirectAfterLogin', to.fullPath)
      next('/login')
      return
    }
  }
  
  // 只允许访客访问的路由（如登录页面）
  if (to.meta.requiresGuest && userStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

export default router