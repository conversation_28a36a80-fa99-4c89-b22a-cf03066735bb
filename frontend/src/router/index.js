import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Profile from '../views/space/personal/Profile.vue'
import UserProfile from '../views/space/personal/UserProfile.vue'
import TeamSpace from '../views/space/team/TeamSpaceTest.vue'
import TeamSpaceDetail from '../views/space/team/TeamSpaceDetail.vue'
import Tools from '../views/Tools.vue'
import Auth from '../views/Auth.vue'
import Login from '../views/Login.vue'
import OAuthCallback from '../views/OAuthCallback.vue'
import PromptDetail from '../views/PromptDetail.vue'
import ToolDetail from '../views/ToolDetail.vue'
import Search from '../views/Search.vue'
import PromptEdit from '../views/PromptEdit.vue'
import RecommendationSquare from '../views/RecommendationSquare.vue'
import ApiTest from '../views/ApiTest.vue'
import SubscriptionCenter from '../views/SubscriptionCenter.vue'
import SubscriptionTest from '../views/SubscriptionTest.vue'
import NotFound from '../views/NotFound.vue'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/auth/callback/:provider',
    name: 'OAuthCallback',
    component: OAuthCallback
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/user/:userId',
    name: 'UserProfile',
    component: UserProfile,
    props: true
  },
  {
    path: '/team-space',
    name: 'TeamSpace',
    component: TeamSpace,
    meta: { requiresAuth: true }
  },

  {
    path: '/team-space/:id',
    name: 'TeamSpaceDetail',
    component: TeamSpaceDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/tools',
    name: 'Tools',
    component: Tools,
    meta: { requiresAuth: true }
  },
  {
    path: '/recommendation',
    name: 'RecommendationSquare',
    component: RecommendationSquare,
    meta: { requiresAuth: true }
  },
  {
    path: '/subscription',
    name: 'SubscriptionCenter',
    component: SubscriptionCenter,
    meta: { requiresAuth: false }
  },
  {
    path: '/subscription-test',
    name: 'SubscriptionTest',
    component: SubscriptionTest,
    meta: { requiresAuth: false }
  },
  {
    path: '/api-test',
    name: 'ApiTest',
    component: ApiTest,
    meta: { requiresAuth: false }
  },
  {
    path: '/auth',
    name: 'Auth',
    component: Auth
  },

  {
    path: '/prompt/:id',
    name: 'PromptDetail',
    component: PromptDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/tool/:id',
    name: 'ToolDetail',
    component: ToolDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/search',
    name: 'Search',
    component: Search,
    meta: { requiresAuth: true }
  },
  {
    path: '/prompt-edit/:id?',
    name: 'PromptEdit',
    component: PromptEdit,
    meta: { requiresAuth: true }
  },

  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: { requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（比如通过浏览器后退/前进按钮），返回到该位置
    if (savedPosition) {
      return savedPosition
    }
    // 否则滚动到页面顶部
    return { top: 0 }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 首先初始化用户状态
  await userStore.initialize()
  
  // 需要认证的路由
  if (to.meta.requiresAuth) {
    if (!userStore.isAuthenticated) {
      // 保存用户尝试访问的页面
      localStorage.setItem('redirectAfterLogin', to.fullPath)
      next('/login')
      return
    }
  }
  
  // 只允许访客访问的路由（如登录页面）
  if (to.meta.requiresGuest && userStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

export default router