.audio-subscription {
  height: 100vh;
  background: #f8fafc;
}

.subscription-layout {
  display: flex;
  height: 100%;
  gap: 1px;
  background: #e5e7eb;
}

// 左侧订阅列表 (15%)
.subscription-sidebar {
  width: 15%;
  background: white;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .header-actions {
      .refresh-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        i.spinning {
          animation: spin 1s linear infinite;
          display: inline-block;
        }
      }
    }
  }

  .subscription-list {
    flex: 1;
    overflow-y: auto;

    .subscription-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      .subscription-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;

        .subscription-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          background: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #6b7280;
        }

        .subscription-details {
          flex: 1;
          min-width: 0;

          .subscription-name {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 500;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .subscription-count {
            margin: 0;
            font-size: 0.75rem;
            color: #6b7280;
          }
        }
      }

      .subscription-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
      }
    }
  }
}

// 中间音频列表 (20%)
.audio-list {
  width: 20%;
  background: white;
  display: flex;
  flex-direction: column;

  .list-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .search-box {
      position: relative;

      .search-input {
        width: 100%;
        padding: 0.5rem 2.5rem 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;

        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }

      .icon-search {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
      }
    }
  }

  .audio-items {
    flex: 1;
    overflow-y: auto;

    .audio-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 1rem;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      &.playing {
        background: #fef3c7;
        border-right: 3px solid #f59e0b;
      }

      .audio-cover {
        position: relative;
        width: 60px;
        height: 60px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-overlay {
          position: absolute;
          inset: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;

          .play-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: white;
            color: #1f2937;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }

        &:hover .play-overlay {
          opacity: 1;
        }
      }

      .audio-content {
        flex: 1;
        min-width: 0;

        .audio-title {
          margin: 0 0 0.25rem 0;
          font-size: 0.875rem;
          font-weight: 500;
          color: #1f2937;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .audio-host {
          margin: 0 0 0.25rem 0;
          font-size: 0.75rem;
          color: #6b7280;
        }

        .audio-meta {
          display: flex;
          gap: 0.5rem;
          font-size: 0.75rem;
          color: #9ca3af;

          span {
            white-space: nowrap;
          }
        }
      }

      .audio-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
      }
    }
  }

  // 音频播放器
  .audio-player {
    border-top: 1px solid #e5e7eb;
    padding: 1rem 1.5rem;
    background: #f9fafb;

    .player-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.75rem;

      .player-cover {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        object-fit: cover;
      }

      .player-details {
        flex: 1;
        min-width: 0;

        .player-title {
          margin: 0 0 0.25rem 0;
          font-size: 0.875rem;
          font-weight: 500;
          color: #1f2937;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .player-host {
          margin: 0;
          font-size: 0.75rem;
          color: #6b7280;
        }
      }
    }

    .player-controls {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1rem;
      margin-bottom: 0.75rem;

      .control-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 50%;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.play-pause {
          width: 40px;
          height: 40px;
          background: #3b82f6;
          color: white;

          &:hover {
            background: #2563eb;
          }
        }
      }
    }

    .player-progress {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .time-current,
      .time-total {
        font-size: 0.75rem;
        color: #6b7280;
        min-width: 40px;
        text-align: center;
      }

      .progress-bar {
        flex: 1;
        cursor: pointer;

        .progress-track {
          position: relative;
          height: 4px;
          background: #e5e7eb;
          border-radius: 2px;

          .progress-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 2px;
            transition: width 0.1s ease;
          }

          .progress-handle {
            position: absolute;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 12px;
            height: 12px;
            background: #3b82f6;
            border-radius: 50%;
            transition: left 0.1s ease;
          }
        }
      }
    }
  }

  .pagination {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    .page-btn {
      width: 32px;
      height: 32px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      background: white;
      color: #6b7280;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        border-color: #3b82f6;
        color: #3b82f6;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .page-info {
      font-size: 0.875rem;
      color: #6b7280;
    }
  }
}

// 右侧音频详情 (65%)
.audio-detail {
  width: 65%;
  background: white;
  display: flex;
  flex-direction: column;

  .detail-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #9ca3af;

    .icon-audio-large {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.25rem;
      font-weight: 500;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
    }
  }

  .detail-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .detail-header {
      padding: 1.5rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      gap: 1rem;

      .detail-cover {
        width: 120px;
        height: 120px;
        border-radius: 12px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .detail-info {
        flex: 1;

        .detail-title {
          margin: 0 0 0.5rem 0;
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          line-height: 1.4;
        }

        .detail-host {
          margin: 0 0 0.75rem 0;
          font-size: 1rem;
          color: #6b7280;
        }

        .detail-meta {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
          margin-bottom: 1rem;
          font-size: 0.875rem;
          color: #6b7280;
        }

        .detail-actions {
          display: flex;
          gap: 0.5rem;

          .action-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;

            &:hover {
              border-color: #3b82f6;
              color: #3b82f6;
            }

            &.primary {
              background: #3b82f6;
              color: white;
              border-color: #3b82f6;

              &:hover {
                background: #2563eb;
              }
            }
          }
        }
      }
    }

    .detail-body {
      flex: 1;
      padding: 1.5rem;
      overflow-y: auto;

      .audio-description {
        margin-bottom: 2rem;

        h4 {
          margin: 0 0 1rem 0;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
        }

        .description-content {
          line-height: 1.6;
          color: #374151;

          :deep(p) {
            margin: 1rem 0;
          }

          :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
            margin: 1.5rem 0 1rem 0;
            font-weight: 600;
            color: #1f2937;
          }
        }
      }

      .chapters-list {
        h4 {
          margin: 0 0 1rem 0;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
        }

        .chapters {
          .chapter-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: #f9fafb;
            }

            .chapter-time {
              font-size: 0.875rem;
              color: #6b7280;
              min-width: 60px;
            }

            .chapter-title {
              flex: 1;
              font-size: 0.875rem;
              color: #374151;
            }
          }
        }
      }

      .no-content {
        text-align: center;
        color: #9ca3af;
        padding: 2rem;
      }
    }
  }

  // 公共状态样式
  .loading-state,
  .empty-state {
    padding: 2rem;
    text-align: center;
    color: #9ca3af;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f4f6;
      border-top: 3px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
    }
  }
}