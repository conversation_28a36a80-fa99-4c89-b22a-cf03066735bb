<template>
  <div class="crawler-manager">
    <div class="manager-header">
      <h3>爬虫管理</h3>
      <p class="subtitle">管理和配置订阅源的数据爬取</p>
    </div>

    <!-- 预定义订阅源 -->
    <div class="predefined-feeds">
      <h4>预定义订阅源</h4>
      <div class="feed-cards">
        <div
          v-for="(feed, key) in predefinedFeeds"
          :key="key"
          class="feed-card"
        >
          <div class="feed-info">
            <h5>{{ feed.name }}</h5>
            <p>类型: {{ feed.type }}</p>
            <p>条目限制: {{ feed.entriesLimit || 8 }}</p>
          </div>
          <div class="feed-actions">
            <button
              :class="['action-btn', 'add-btn', { loading: loadingFeeds.has(key) }]"
              @click="addPredefinedFeed(key)"
              :disabled="loadingFeeds.has(key)"
            >
              <i v-if="loadingFeeds.has(key)" class="icon-loading"></i>
              <i v-else class="icon-plus"></i>
              {{ loadingFeeds.has(key) ? '添加中...' : '添加订阅' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义爬虫配置 -->
    <div class="custom-crawler">
      <h4>自定义爬虫</h4>
      <div class="crawler-form">
        <div class="form-group">
          <label>订阅源类型</label>
          <select v-model="customConfig.type">
            <option value="folo">Folo.is API</option>
            <option value="rss">RSS订阅</option>
          </select>
        </div>

        <div v-if="customConfig.type === 'folo'" class="form-group">
          <label>Feed ID</label>
          <input
            v-model="customConfig.feedId"
            type="text"
            placeholder="请输入Folo.is的Feed ID"
          />
        </div>

        <div v-if="customConfig.type === 'rss'" class="form-group">
          <label>RSS URL</label>
          <input
            v-model="customConfig.rssUrl"
            type="url"
            placeholder="请输入RSS订阅地址"
          />
        </div>

        <div class="form-group">
          <label>订阅名称</label>
          <input
            v-model="customConfig.name"
            type="text"
            placeholder="请输入订阅名称"
          />
        </div>

        <div class="form-group">
          <label>条目数量限制</label>
          <input
            v-model.number="customConfig.entriesLimit"
            type="number"
            min="1"
            max="50"
            placeholder="默认10"
          />
        </div>

        <div class="form-actions">
          <button
            :class="['action-btn', 'test-btn', { loading: testing }]"
            @click="testCustomConfig"
            :disabled="!canTest || testing"
          >
            <i v-if="testing" class="icon-loading"></i>
            <i v-else class="icon-test"></i>
            {{ testing ? '测试中...' : '测试连接' }}
          </button>

          <button
            :class="['action-btn', 'add-btn', { loading: adding }]"
            @click="addCustomFeed"
            :disabled="!canAdd || adding"
          >
            <i v-if="adding" class="icon-loading"></i>
            <i v-else class="icon-plus"></i>
            {{ adding ? '添加中...' : '添加订阅' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 爬取历史 -->
    <div class="crawl-history">
      <h4>爬取历史</h4>
      <div v-if="crawlHistory.length === 0" class="empty-history">
        <p>暂无爬取历史</p>
      </div>
      <div v-else class="history-list">
        <div
          v-for="record in crawlHistory"
          :key="record.id"
          class="history-item"
        >
          <div class="history-info">
            <h5>{{ record.name }}</h5>
            <p>{{ record.type }} - {{ record.itemCount }} 条内容</p>
            <span class="time">{{ formatTime(record.timestamp) }}</span>
          </div>
          <div class="history-status">
            <span :class="['status', record.status]">
              {{ record.status === 'success' ? '成功' : '失败' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 结果显示 -->
    <div v-if="testResult" class="test-result">
      <h4>测试结果</h4>
      <div :class="['result-content', testResult.success ? 'success' : 'error']">
        <div v-if="testResult.success" class="success-result">
          <h5>✅ 连接成功</h5>
          <p>订阅名称: {{ testResult.data.subscription.name }}</p>
          <p>内容类型: {{ testResult.data.subscription.type }}</p>
          <p>获取到 {{ testResult.data.contents.length }} 条内容</p>
          
          <div class="content-preview">
            <h6>内容预览:</h6>
            <ul>
              <li
                v-for="content in testResult.data.contents.slice(0, 3)"
                :key="content.id"
              >
                {{ content.title }}
              </li>
            </ul>
          </div>
        </div>
        
        <div v-else class="error-result">
          <h5>❌ 连接失败</h5>
          <p>{{ testResult.error }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, reactive } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'
import { PREDEFINED_FEEDS, contentCrawler } from '@/utils/crawler.js'

export default {
  name: 'CrawlerManager',
  setup() {
    const subscriptionStore = useSubscriptionStore()

    // 响应式数据
    const loadingFeeds = ref(new Set())
    const testing = ref(false)
    const adding = ref(false)
    const testResult = ref(null)
    const crawlHistory = ref([])

    // 自定义配置
    const customConfig = reactive({
      type: 'folo',
      feedId: '',
      rssUrl: '',
      name: '',
      entriesLimit: 10
    })

    // 计算属性
    const predefinedFeeds = computed(() => PREDEFINED_FEEDS)

    const canTest = computed(() => {
      if (customConfig.type === 'folo') {
        return customConfig.feedId.trim() !== ''
      } else if (customConfig.type === 'rss') {
        return customConfig.rssUrl.trim() !== ''
      }
      return false
    })

    const canAdd = computed(() => {
      return canTest.value && customConfig.name.trim() !== ''
    })

    // 方法
    const addPredefinedFeed = async (feedKey) => {
      try {
        loadingFeeds.value.add(feedKey)
        
        const result = await subscriptionStore.addPredefinedSubscription(feedKey)
        
        // 添加到历史记录
        crawlHistory.value.unshift({
          id: Date.now(),
          name: result.subscription.name,
          type: result.subscription.type,
          itemCount: result.contents.length,
          timestamp: new Date().toISOString(),
          status: 'success'
        })
        
        // 显示成功消息
        alert(`成功添加订阅: ${result.subscription.name}`)
        
      } catch (error) {
        console.error('添加预定义订阅失败:', error)
        alert(`添加失败: ${error.message}`)
        
        // 添加失败记录
        crawlHistory.value.unshift({
          id: Date.now(),
          name: PREDEFINED_FEEDS[feedKey].name,
          type: 'unknown',
          itemCount: 0,
          timestamp: new Date().toISOString(),
          status: 'error'
        })
      } finally {
        loadingFeeds.value.delete(feedKey)
      }
    }

    const testCustomConfig = async () => {
      try {
        testing.value = true
        testResult.value = null
        
        let result
        if (customConfig.type === 'folo') {
          result = await contentCrawler.fetchFoloFeed(customConfig.feedId, customConfig.entriesLimit)
        } else if (customConfig.type === 'rss') {
          result = await contentCrawler.fetchRSSFeed(customConfig.rssUrl)
        }
        
        testResult.value = {
          success: true,
          data: result
        }
        
      } catch (error) {
        console.error('测试连接失败:', error)
        testResult.value = {
          success: false,
          error: error.message
        }
      } finally {
        testing.value = false
      }
    }

    const addCustomFeed = async () => {
      try {
        adding.value = true
        
        let result
        if (customConfig.type === 'folo') {
          result = await subscriptionStore.fetchSubscriptionFromCrawler(customConfig.feedId, 'folo')
        }
        
        if (result) {
          // 更新订阅名称
          const subscriptionType = result.subscription.type
          const subIndex = subscriptionStore.subscriptions[subscriptionType].findIndex(
            sub => sub.id === result.subscription.id
          )
          
          if (subIndex >= 0) {
            subscriptionStore.subscriptions[subscriptionType][subIndex].name = customConfig.name
          }
          
          // 添加到历史记录
          crawlHistory.value.unshift({
            id: Date.now(),
            name: customConfig.name,
            type: result.subscription.type,
            itemCount: result.contents.length,
            timestamp: new Date().toISOString(),
            status: 'success'
          })
          
          // 重置表单
          Object.assign(customConfig, {
            type: 'folo',
            feedId: '',
            rssUrl: '',
            name: '',
            entriesLimit: 10
          })
          
          testResult.value = null
          
          alert(`成功添加自定义订阅: ${customConfig.name}`)
        }
        
      } catch (error) {
        console.error('添加自定义订阅失败:', error)
        alert(`添加失败: ${error.message}`)
      } finally {
        adding.value = false
      }
    }

    const formatTime = (timeString) => {
      const date = new Date(timeString)
      return date.toLocaleString()
    }

    return {
      predefinedFeeds,
      loadingFeeds,
      testing,
      adding,
      testResult,
      crawlHistory,
      customConfig,
      canTest,
      canAdd,
      addPredefinedFeed,
      testCustomConfig,
      addCustomFeed,
      formatTime
    }
  }
}
</script>

<style lang="scss" scoped>
.crawler-manager {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.manager-header {
  margin-bottom: 2rem;
  
  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
  }
  
  .subtitle {
    margin: 0;
    color: #6b7280;
  }
}

.predefined-feeds,
.custom-crawler,
.crawl-history {
  margin-bottom: 3rem;
  
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
  }
}

.feed-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.feed-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .feed-info {
    h5 {
      margin: 0 0 0.5rem 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }
    
    p {
      margin: 0.25rem 0;
      font-size: 0.875rem;
      color: #6b7280;
    }
  }
}

.crawler-form {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 2rem;
  
  .form-group {
    margin-bottom: 1.5rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #374151;
    }
    
    input,
    select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 1rem;
      
      &:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
      }
    }
  }
  
  .form-actions {
    display: flex;
    gap: 1rem;
  }
}

.action-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.add-btn {
    background: #4f46e5;
    color: white;
    
    &:hover:not(:disabled) {
      background: #4338ca;
    }
  }
  
  &.test-btn {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    
    &:hover:not(:disabled) {
      background: #e5e7eb;
    }
  }
  
  &.loading {
    pointer-events: none;
  }
}

.history-list {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
}

.history-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &:last-child {
    border-bottom: none;
  }
  
  .history-info {
    h5 {
      margin: 0 0 0.25rem 0;
      font-size: 1rem;
      font-weight: 600;
      color: #1f2937;
    }
    
    p {
      margin: 0 0 0.25rem 0;
      font-size: 0.875rem;
      color: #6b7280;
    }
    
    .time {
      font-size: 0.75rem;
      color: #9ca3af;
    }
  }
  
  .status {
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    
    &.success {
      background: #d1fae5;
      color: #065f46;
    }
    
    &.error {
      background: #fee2e2;
      color: #991b1b;
    }
  }
}

.test-result {
  margin-top: 2rem;
  
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
  }
}

.result-content {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  
  &.success {
    border-color: #10b981;
    background: #f0fdf4;
  }
  
  &.error {
    border-color: #ef4444;
    background: #fef2f2;
  }
  
  h5 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 600;
  }
  
  p {
    margin: 0.5rem 0;
    color: #374151;
  }
  
  .content-preview {
    margin-top: 1rem;
    
    h6 {
      margin: 0 0 0.5rem 0;
      font-weight: 500;
      color: #374151;
    }
    
    ul {
      margin: 0;
      padding-left: 1.5rem;
      
      li {
        margin: 0.25rem 0;
        color: #6b7280;
      }
    }
  }
}

.empty-history {
  text-align: center;
  padding: 2rem;
  color: #9ca3af;
}

// 图标
.icon-plus::before { content: "+"; }
.icon-test::before { content: "🔍"; }
.icon-loading::before { content: "⏳"; }
</style>
