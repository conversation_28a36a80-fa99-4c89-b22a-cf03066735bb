<template>
  <div class="audio-subscription">
    <!-- 三列布局容器 -->
    <div class="subscription-layout">
      <!-- 左侧：订阅列表 (18%) -->
      <div class="subscription-sidebar">
        <div class="sidebar-header">
          <h3>音频订阅</h3>
          <div class="header-actions">
            <button class="refresh-btn" @click="refreshSubscriptions" :disabled="loading">
              <i class="icon-refresh" :class="{ spinning: loading }"></i>
            </button>
          </div>
        </div>

        <div class="subscription-list">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载中...</p>
          </div>
          
          <div v-else-if="subscriptions.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无订阅源</p>
          </div>
          
          <div
            v-for="subscription in subscriptions"
            :key="subscription"
            :class="['subscription-item', { active: selectedSubscription === subscription }]"
            @click="selectSubscription(subscription)"
          >
            <div class="subscription-info">
              <div class="subscription-icon">
                <i class="icon-audio"></i>
              </div>
              <div class="subscription-details">
                <h4 class="subscription-name">{{ subscription }}</h4>
                <p class="subscription-count">{{ getSubscriptionCount(subscription) }} 个音频</p>
              </div>
            </div>
            <div class="subscription-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：音频列表 (32%) -->
      <div class="audio-list">
        <div class="list-header">
          <h3>{{ selectedSubscription || '全部音频' }}</h3>
          <div class="header-actions">
            <div class="search-box">
              <input
                v-model="searchKeyword"
                type="text"
                placeholder="搜索音频..."
                @input="handleSearch"
                class="search-input"
              >
              <i class="icon-search"></i>
            </div>
          </div>
        </div>

        <div class="audio-items">
          <div v-if="loadingAudios" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载音频中...</p>
          </div>
          
          <div v-else-if="filteredAudios.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无音频</p>
          </div>
          
          <div
            v-for="audio in filteredAudios"
            :key="audio.id"
            :class="['audio-item', { 
              active: selectedAudio?.id === audio.id, 
              playing: currentPlayingId === audio.id 
            }]"
            @click="selectAudio(audio)"
          >
            <div class="audio-cover">
              <img :src="audio.cover || '/img/default-audio-cover.jpg'" :alt="audio.title">
              <div class="play-overlay">
                <button
                  class="play-btn"
                  @click.stop="togglePlay(audio)"
                  :disabled="audioLoading"
                >
                  <i v-if="currentPlayingId === audio.id && isPlaying" class="icon-pause"></i>
                  <i v-else class="icon-play"></i>
                </button>
              </div>
            </div>
            <div class="audio-content">
              <h4 class="audio-title">{{ audio.title }}</h4>
              <p class="audio-host">主播：{{ audio.host || audio.author }}</p>
              <div class="audio-meta">
                <span class="audio-duration">{{ formatDuration(audio.duration) }}</span>
                <span class="audio-date">{{ formatDate(audio.pubDate) }}</span>
                <span class="audio-plays">{{ formatPlays(audio.plays) }} 播放</span>
              </div>
            </div>
            <div class="audio-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>

        <!-- 音频播放器 -->
        <div v-if="currentAudio" class="audio-player">
          <div class="player-info">
            <img :src="currentAudio.cover || '/img/default-audio-cover.jpg'" :alt="currentAudio.title" class="player-cover">
            <div class="player-details">
              <h4 class="player-title">{{ currentAudio.title }}</h4>
              <p class="player-host">{{ currentAudio.host || currentAudio.author }}</p>
            </div>
          </div>
          <div class="player-controls">
            <button class="control-btn" @click="previousTrack">
              <i class="icon-previous"></i>
            </button>
            <button class="control-btn play-pause" @click="toggleCurrentPlay" :disabled="audioLoading">
              <i v-if="isPlaying" class="icon-pause"></i>
              <i v-else class="icon-play"></i>
            </button>
            <button class="control-btn" @click="nextTrack">
              <i class="icon-next"></i>
            </button>
          </div>
          <div class="player-progress">
            <span class="time-current">{{ formatTime(currentTime) }}</span>
            <div class="progress-bar" @click="seekTo">
              <div class="progress-track">
                <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                <div class="progress-handle" :style="{ left: progressPercentage + '%' }"></div>
              </div>
            </div>
            <span class="time-total">{{ formatTime(duration) }}</span>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="page-btn"
          >
            <i class="icon-chevron-left"></i>
          </button>
          <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="page-btn"
          >
            <i class="icon-chevron-right"></i>
          </button>
        </div>
      </div>

      <!-- 右侧：音频详情 (50%) -->
      <div class="audio-detail">
        <div v-if="!selectedAudio" class="detail-placeholder">
          <i class="icon-audio-large"></i>
          <h3>选择音频查看详情</h3>
          <p>点击左侧音频列表中的任意音频查看详细信息</p>
        </div>
        
        <div v-else class="detail-content">
          <div class="detail-header">
            <div class="detail-cover">
              <img :src="selectedAudio.cover || '/img/default-audio-cover.jpg'" :alt="selectedAudio.title">
            </div>
            <div class="detail-info">
              <h2 class="detail-title">{{ selectedAudio.title }}</h2>
              <p class="detail-host">主播：{{ selectedAudio.host || selectedAudio.author }}</p>
              <div class="detail-meta">
                <span class="detail-duration">时长：{{ formatDuration(selectedAudio.duration) }}</span>
                <span class="detail-date">发布：{{ formatDate(selectedAudio.pubDate) }}</span>
                <span class="detail-plays">播放：{{ formatPlays(selectedAudio.plays) }}</span>
              </div>
              <div class="detail-actions">
                <button class="action-btn primary" @click="togglePlay(selectedAudio)">
                  <i v-if="currentPlayingId === selectedAudio.id && isPlaying" class="icon-pause"></i>
                  <i v-else class="icon-play"></i>
                  {{ currentPlayingId === selectedAudio.id && isPlaying ? '暂停' : '播放' }}
                </button>
                <button class="action-btn" @click="downloadAudio" title="下载">
                  <i class="icon-download"></i>
                </button>
                <button class="action-btn" @click="shareAudio" title="分享">
                  <i class="icon-share"></i>
                </button>
                <button class="action-btn" @click="favoriteAudio" title="收藏">
                  <i class="icon-heart"></i>
                </button>
              </div>
            </div>
          </div>
          
          <div class="detail-body">
            <div v-if="loadingDetail" class="loading-state">
              <div class="loading-spinner"></div>
              <p>加载详情中...</p>
            </div>
            <div v-else-if="audioDescription" class="audio-description">
              <h4>节目简介</h4>
              <div class="description-content" v-html="audioDescription"></div>
            </div>
            <div v-else class="no-content">
              <p>暂无详细介绍</p>
            </div>

            <!-- 章节列表 -->
            <div v-if="selectedAudio.chapters && selectedAudio.chapters.length" class="chapters-list">
              <h4>章节列表</h4>
              <div class="chapters">
                <div
                  v-for="chapter in selectedAudio.chapters"
                  :key="chapter.id"
                  class="chapter-item"
                  @click="seekToChapter(chapter.startTime)"
                >
                  <div class="chapter-time">{{ formatTime(chapter.startTime) }}</div>
                  <div class="chapter-title">{{ chapter.title }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 音频元素 -->
    <audio
      ref="audioElement"
      @loadstart="audioLoading = true"
      @canplay="audioLoading = false"
      @timeupdate="updateTime"
      @ended="onAudioEnded"
      @error="onAudioError"
      preload="metadata"
    ></audio>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { ApiClient } from '@/utils/api'

export default {
  name: 'AudioSubscription',
  emits: ['stats-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const loadingAudios = ref(false)
    const loadingDetail = ref(false)
    const audioLoading = ref(false)
    const subscriptions = ref([])
    const audios = ref([])
    const selectedSubscription = ref(null)
    const selectedAudio = ref(null)
    const audioDescription = ref('')
    const searchKeyword = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const totalCount = ref(0)

    // 音频播放相关
    const audioElement = ref(null)
    const currentAudio = ref(null)
    const currentPlayingId = ref(null)
    const isPlaying = ref(false)
    const currentTime = ref(0)
    const duration = ref(0)

    // 计算属性
    const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
    
    const filteredAudios = computed(() => {
      if (!searchKeyword.value) return audios.value
      
      const keyword = searchKeyword.value.toLowerCase()
      return audios.value.filter(audio =>
        audio.title?.toLowerCase().includes(keyword) ||
        audio.description?.toLowerCase().includes(keyword) ||
        audio.host?.toLowerCase().includes(keyword) ||
        audio.author?.toLowerCase().includes(keyword)
      )
    })

    const progressPercentage = computed(() => {
      if (!duration.value) return 0
      return (currentTime.value / duration.value) * 100
    })

    // 方法
    const refreshSubscriptions = async () => {
      loading.value = true
      try {
        const response = await ApiClient.get('http://localhost:8001/api/crawler/content/list?type=audio')
        if (response.code === 200) {
          subscriptions.value = response.data.subscriptions || []
          if (subscriptions.value.length > 0 && !selectedSubscription.value) {
            selectedSubscription.value = subscriptions.value[0]
          }

          // 发送统计更新事件
          emit('stats-updated', {
            audioCount: response.data.total || 0,
            audioSubscriptionCount: subscriptions.value.length
          })
        }
      } catch (error) {
        console.error('获取订阅列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    const selectSubscription = async (subscription) => {
      if (selectedSubscription.value === subscription) return

      selectedSubscription.value = subscription
      selectedAudio.value = null
      audioDescription.value = ''
      currentPage.value = 1
      await loadAudios()
    }

    const loadAudios = async () => {
      if (!selectedSubscription.value) return

      loadingAudios.value = true
      try {
        const response = await ApiClient.get(
          `http://localhost:8001/api/crawler/content/by-subscription?type=audio&taskName=${encodeURIComponent(selectedSubscription.value)}&page=${currentPage.value}&size=${pageSize.value}`
        )
        if (response.code === 200) {
          audios.value = response.data.contents || []
          totalCount.value = response.data.total || 0
        }
      } catch (error) {
        console.error('获取音频列表失败:', error)
      } finally {
        loadingAudios.value = false
      }
    }

    const selectAudio = async (audio) => {
      if (selectedAudio.value?.id === audio.id) return

      selectedAudio.value = audio
      await loadAudioDetail(audio.id)
    }

    const loadAudioDetail = async (audioId) => {
      loadingDetail.value = true
      try {
        const response = await ApiClient.get(`http://localhost:8001/api/crawler/content/${audioId}`)
        if (response.code === 200) {
          audioDescription.value = response.data.content || response.data.description || ''
        }
      } catch (error) {
        console.error('获取音频详情失败:', error)
      } finally {
        loadingDetail.value = false
      }
    }

    const getSubscriptionCount = (subscription) => {
      return Math.floor(Math.random() * 100) + 10
    }

    const handleSearch = () => {
      // 搜索逻辑已在计算属性中处理
    }

    const changePage = (page) => {
      if (page < 1 || page > totalPages.value) return
      currentPage.value = page
      loadAudios()
    }

    // 音频播放控制
    const togglePlay = (audio) => {
      if (currentPlayingId.value === audio.id) {
        toggleCurrentPlay()
      } else {
        playAudio(audio)
      }
    }

    const playAudio = (audio) => {
      if (!audioElement.value || !audio.url) return

      currentAudio.value = audio
      currentPlayingId.value = audio.id
      audioElement.value.src = audio.url
      audioElement.value.play()
      isPlaying.value = true
    }

    const toggleCurrentPlay = () => {
      if (!audioElement.value) return

      if (isPlaying.value) {
        audioElement.value.pause()
        isPlaying.value = false
      } else {
        audioElement.value.play()
        isPlaying.value = true
      }
    }

    const previousTrack = () => {
      const currentIndex = audios.value.findIndex(audio => audio.id === currentPlayingId.value)
      if (currentIndex > 0) {
        playAudio(audios.value[currentIndex - 1])
      }
    }

    const nextTrack = () => {
      const currentIndex = audios.value.findIndex(audio => audio.id === currentPlayingId.value)
      if (currentIndex < audios.value.length - 1) {
        playAudio(audios.value[currentIndex + 1])
      }
    }

    const seekTo = (event) => {
      if (!audioElement.value || !duration.value) return

      const rect = event.currentTarget.getBoundingClientRect()
      const percentage = (event.clientX - rect.left) / rect.width
      const newTime = percentage * duration.value
      audioElement.value.currentTime = newTime
    }

    const seekToChapter = (startTime) => {
      if (!audioElement.value) return
      audioElement.value.currentTime = startTime
    }

    const updateTime = () => {
      if (audioElement.value) {
        currentTime.value = audioElement.value.currentTime
        duration.value = audioElement.value.duration || 0
      }
    }

    const onAudioEnded = () => {
      isPlaying.value = false
      nextTrack()
    }

    const onAudioError = () => {
      console.error('音频播放错误')
      isPlaying.value = false
      audioLoading.value = false
    }

    // 格式化函数
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const formatDuration = (seconds) => {
      if (!seconds) return '00:00'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)

      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }

    const formatTime = (seconds) => {
      return formatDuration(seconds)
    }

    const formatPlays = (plays) => {
      if (!plays) return '0'
      if (plays >= 10000) {
        return `${(plays / 10000).toFixed(1)}万`
      }
      return plays.toString()
    }

    // 操作函数
    const downloadAudio = () => {
      if (selectedAudio.value?.url) {
        const link = document.createElement('a')
        link.href = selectedAudio.value.url
        link.download = selectedAudio.value.title + '.mp3'
        link.click()
      }
    }

    const shareAudio = () => {
      console.log('分享音频:', selectedAudio.value)
    }

    const favoriteAudio = () => {
      console.log('收藏音频:', selectedAudio.value)
    }

    // 监听选中订阅的变化
    watch(selectedSubscription, () => {
      if (selectedSubscription.value) {
        loadAudios()
      }
    })

    // 组件挂载和卸载
    onMounted(() => {
      refreshSubscriptions()
    })

    onUnmounted(() => {
      if (audioElement.value) {
        audioElement.value.pause()
        audioElement.value.src = ''
      }
    })

    return {
      loading,
      loadingAudios,
      loadingDetail,
      audioLoading,
      subscriptions,
      audios,
      selectedSubscription,
      selectedAudio,
      audioDescription,
      searchKeyword,
      currentPage,
      totalPages,
      filteredAudios,
      audioElement,
      currentAudio,
      currentPlayingId,
      isPlaying,
      currentTime,
      duration,
      progressPercentage,
      refreshSubscriptions,
      selectSubscription,
      selectAudio,
      getSubscriptionCount,
      handleSearch,
      changePage,
      togglePlay,
      toggleCurrentPlay,
      previousTrack,
      nextTrack,
      seekTo,
      seekToChapter,
      formatDate,
      formatDuration,
      formatTime,
      formatPlays,
      downloadAudio,
      shareAudio,
      favoriteAudio
    }
  }
}
</script>

<style lang="scss" scoped>
.audio-subscription {
  height: 100vh;
  background: #f8fafc;
}

.subscription-layout {
  display: flex;
  height: 100%;
  gap: 1px;
  background: #e5e7eb;
}

// 左侧订阅列表 (18%)
.subscription-sidebar {
  width: 18%;
  background: white;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .header-actions {
      .refresh-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .subscription-list {
    flex: 1;
    overflow-y: auto;

    .subscription-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      .subscription-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;

        .subscription-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          background: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #6b7280;
        }

        .subscription-details {
          flex: 1;
          min-width: 0;

          .subscription-name {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 500;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .subscription-count {
            margin: 0;
            font-size: 0.75rem;
            color: #6b7280;
          }
        }
      }

      .subscription-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
      }
    }
  }
}

// 中间音频列表 (32%)
.audio-list {
  width: 32%;
  background: white;
  display: flex;
  flex-direction: column;

  .list-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .search-box {
      position: relative;

      .search-input {
        width: 100%;
        padding: 0.5rem 2.5rem 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;

        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }

      .icon-search {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
      }
    }
  }

  .audio-items {
    flex: 1;
    overflow-y: auto;

    .audio-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 1rem;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      &.playing {
        background: #fef3c7;
        border-right: 3px solid #f59e0b;
      }

      .audio-cover {
        position: relative;
        width: 60px;
        height: 60px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-overlay {
          position: absolute;
          inset: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;

          .play-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: white;
            color: #1f2937;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }

        &:hover .play-overlay {
          opacity: 1;
        }
      }

      .audio-content {
        flex: 1;
        min-width: 0;

        .audio-title {
          margin: 0 0 0.25rem 0;
          font-size: 0.875rem;
          font-weight: 500;
          color: #1f2937;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .audio-host {
          margin: 0 0 0.25rem 0;
          font-size: 0.75rem;
          color: #6b7280;
        }

        .audio-meta {
          display: flex;
          gap: 0.5rem;
          font-size: 0.75rem;
          color: #9ca3af;

          span {
            white-space: nowrap;
          }
        }
      }

      .audio-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
      }
    }
  }

  // 音频播放器
  .audio-player {
    border-top: 1px solid #e5e7eb;
    padding: 1rem 1.5rem;
    background: #f9fafb;

    .player-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.75rem;

      .player-cover {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        object-fit: cover;
      }

      .player-details {
        flex: 1;
        min-width: 0;

        .player-title {
          margin: 0 0 0.25rem 0;
          font-size: 0.875rem;
          font-weight: 500;
          color: #1f2937;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .player-host {
          margin: 0;
          font-size: 0.75rem;
          color: #6b7280;
        }
      }
    }

    .player-controls {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1rem;
      margin-bottom: 0.75rem;

      .control-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 50%;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.play-pause {
          width: 40px;
          height: 40px;
          background: #3b82f6;
          color: white;

          &:hover {
            background: #2563eb;
          }
        }
      }
    }

    .player-progress {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .time-current,
      .time-total {
        font-size: 0.75rem;
        color: #6b7280;
        min-width: 40px;
        text-align: center;
      }

      .progress-bar {
        flex: 1;
        cursor: pointer;

        .progress-track {
          position: relative;
          height: 4px;
          background: #e5e7eb;
          border-radius: 2px;

          .progress-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 2px;
            transition: width 0.1s ease;
          }

          .progress-handle {
            position: absolute;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 12px;
            height: 12px;
            background: #3b82f6;
            border-radius: 50%;
            transition: left 0.1s ease;
          }
        }
      }
    }
  }

  .pagination {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    .page-btn {
      width: 32px;
      height: 32px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      background: white;
      color: #6b7280;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        border-color: #3b82f6;
        color: #3b82f6;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .page-info {
      font-size: 0.875rem;
      color: #6b7280;
    }
  }
}

// 右侧音频详情 (50%)
.audio-detail {
  width: 50%;
  background: white;
  display: flex;
  flex-direction: column;

  .detail-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #9ca3af;

    .icon-audio-large {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.25rem;
      font-weight: 500;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
    }
  }

  .detail-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .detail-header {
      padding: 1.5rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      gap: 1rem;

      .detail-cover {
        width: 120px;
        height: 120px;
        border-radius: 12px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .detail-info {
        flex: 1;

        .detail-title {
          margin: 0 0 0.5rem 0;
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          line-height: 1.4;
        }

        .detail-host {
          margin: 0 0 0.75rem 0;
          font-size: 1rem;
          color: #6b7280;
        }

        .detail-meta {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
          margin-bottom: 1rem;
          font-size: 0.875rem;
          color: #6b7280;
        }

        .detail-actions {
          display: flex;
          gap: 0.5rem;

          .action-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;

            &:hover {
              border-color: #3b82f6;
              color: #3b82f6;
            }

            &.primary {
              background: #3b82f6;
              color: white;
              border-color: #3b82f6;

              &:hover {
                background: #2563eb;
              }
            }
          }
        }
      }
    }
  }

    .detail-body {
      flex: 1;
      padding: 1.5rem;
      overflow-y: auto;

      .audio-description {
        margin-bottom: 2rem;

        h4 {
          margin: 0 0 1rem 0;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
        }

        .description-content {
          line-height: 1.6;
          color: #374151;

          :deep(p) {
            margin: 1rem 0;
          }

          :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
            margin: 1.5rem 0 1rem 0;
            font-weight: 600;
            color: #1f2937;
          }
        }
      }

      .chapters-list {
        h4 {
          margin: 0 0 1rem 0;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
        }

        .chapters {
          .chapter-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: #f9fafb;
            }

            .chapter-time {
              font-size: 0.875rem;
              color: #6b7280;
              min-width: 60px;
            }

            .chapter-title {
              flex: 1;
              font-size: 0.875rem;
              color: #374151;
            }
          }
        }
      }

      .no-content {
        text-align: center;
        color: #9ca3af;
        padding: 2rem;
      }
    }
  }
}

// 公共状态样式
.loading-state,
.empty-state {
  padding: 2rem;
  text-align: center;
  color: #9ca3af;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 图标
.icon-refresh::before { content: "🔄"; }
.icon-audio::before { content: "🎵"; }
.icon-chevron-right::before { content: "›"; }
.icon-chevron-left::before { content: "‹"; }
.icon-empty::before { content: "🎵"; }
.icon-search::before { content: "🔍"; }
.icon-play::before { content: "▶"; }
.icon-pause::before { content: "⏸"; }
.icon-previous::before { content: "⏮"; }
.icon-next::before { content: "⏭"; }
.icon-audio-large::before { content: "🎵"; }
.icon-download::before { content: "⬇"; }
.icon-share::before { content: "📤"; }
.icon-heart::before { content: "♡"; }
</style>
