<template>
  <div class="audio-subscription">
    <!-- 三列布局 -->
    <div class="subscription-layout">
      <!-- 左侧：订阅列表 -->
      <div class="subscription-list">
        <div class="list-header">
          <h3>音频订阅</h3>
          <button class="add-btn" title="添加订阅">
            <i class="icon-plus"></i>
          </button>
        </div>
        
        <div class="subscription-items">
          <div
            v-for="subscription in subscriptions"
            :key="subscription.id"
            :class="['subscription-item', { active: selectedSubscription?.id === subscription.id }]"
            @click="handleSubscriptionSelect(subscription)"
          >
            <div class="subscription-avatar">
              <img :src="subscription.avatar" :alt="subscription.name" />
            </div>
            <div class="subscription-info">
              <h4 class="subscription-name">{{ subscription.name }}</h4>
              <p class="subscription-desc">{{ subscription.description }}</p>
              <div class="subscription-meta">
                <span class="last-update">{{ formatTime(subscription.lastUpdate) }}</span>
                <span v-if="subscription.unreadCount" class="unread-count">
                  {{ subscription.unreadCount }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：音频简介列表 -->
      <div class="audio-preview">
        <div class="preview-header">
          <h3 v-if="selectedSubscription">{{ selectedSubscription.name }}</h3>
          <h3 v-else>选择订阅查看音频</h3>
        </div>

        <div v-if="selectedSubscription" class="audio-list">
          <div
            v-for="audio in currentAudios"
            :key="audio.id"
            :class="['audio-item', { 
              active: selectedContent?.id === audio.id,
              playing: audioPlayer.isPlaying && audioPlayer.currentTrack?.id === audio.id
            }]"
            @click="handleAudioSelect(audio)"
          >
            <div class="audio-cover">
              <img :src="audio.cover" :alt="audio.title" />
              <div class="play-overlay">
                <button 
                  class="play-btn"
                  @click.stop="toggleAudioPlay(audio)"
                >
                  <i v-if="audioPlayer.isPlaying && audioPlayer.currentTrack?.id === audio.id" class="icon-pause"></i>
                  <i v-else class="icon-play"></i>
                </button>
              </div>
            </div>
            
            <div class="audio-info">
              <h4 class="audio-title">{{ audio.title }}</h4>
              <div class="audio-meta">
                <span class="host">{{ audio.host }}</span>
                <span class="publish-time">{{ formatTime(audio.publishTime) }}</span>
                <span class="duration">{{ formatDuration(audio.duration) }}</span>
              </div>
              <p class="audio-summary">{{ audio.summary }}</p>
              
              <div class="audio-stats">
                <span class="plays">
                  <i class="icon-headphones"></i>
                  {{ audio.plays }}
                </span>
                <span class="likes">
                  <i class="icon-heart"></i>
                  {{ audio.likes }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <div class="empty-icon">🎵</div>
          <p>请从左侧选择一个订阅来查看音频</p>
        </div>
      </div>

      <!-- 右侧：音频详细内容 -->
      <div class="audio-detail">
        <div v-if="selectedContent" class="detail-content">
          <div class="detail-header">
            <div class="audio-cover-large">
              <img :src="selectedContent.cover" :alt="selectedContent.title" />
            </div>
            
            <div class="audio-info-large">
              <h1 class="detail-title">{{ selectedContent.title }}</h1>
              <div class="detail-meta">
                <span class="host">主播：{{ selectedContent.host }}</span>
                <span class="publish-time">{{ formatTime(selectedContent.publishTime) }}</span>
                <span class="duration">时长：{{ formatDuration(selectedContent.duration) }}</span>
              </div>
              
              <div class="audio-controls">
                <button 
                  class="main-play-btn"
                  @click="toggleAudioPlay(selectedContent)"
                >
                  <i v-if="audioPlayer.isPlaying && audioPlayer.currentTrack?.id === selectedContent.id" class="icon-pause"></i>
                  <i v-else class="icon-play"></i>
                  <span v-if="audioPlayer.isPlaying && audioPlayer.currentTrack?.id === selectedContent.id">暂停</span>
                  <span v-else>播放</span>
                </button>
                
                <div class="secondary-controls">
                  <button class="control-btn">
                    <i class="icon-heart"></i>
                    {{ selectedContent.likes }}
                  </button>
                  <button class="control-btn">
                    <i class="icon-share"></i>
                    分享
                  </button>
                  <button class="control-btn">
                    <i class="icon-download"></i>
                    下载
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 音频播放进度条 -->
          <div v-if="audioPlayer.currentTrack?.id === selectedContent.id" class="audio-progress">
            <div class="progress-info">
              <span class="current-time">{{ formatDuration(audioPlayer.currentTime) }}</span>
              <span class="total-time">{{ formatDuration(audioPlayer.duration) }}</span>
            </div>
            <div class="progress-bar" @click="seekAudio">
              <div 
                class="progress-fill" 
                :style="{ width: progressPercentage + '%' }"
              ></div>
            </div>
          </div>

          <div class="detail-body">
            <div class="audio-description">
              <h3>节目简介</h3>
              <p>{{ selectedContent.description }}</p>
            </div>
            
            <div v-if="selectedContent.chapters" class="audio-chapters">
              <h3>章节目录</h3>
              <div class="chapter-list">
                <div
                  v-for="chapter in selectedContent.chapters"
                  :key="chapter.id"
                  class="chapter-item"
                  @click="seekToChapter(chapter.startTime)"
                >
                  <span class="chapter-time">{{ formatDuration(chapter.startTime) }}</span>
                  <span class="chapter-title">{{ chapter.title }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="detail-footer">
            <div class="audio-tags">
              <span v-for="tag in selectedContent.tags" :key="tag" class="tag">
                {{ tag }}
              </span>
            </div>
            
            <div class="audio-stats">
              <span class="stat-item">
                <i class="icon-headphones"></i>
                {{ selectedContent.plays }} 播放
              </span>
              <span class="stat-item">
                <i class="icon-heart"></i>
                {{ selectedContent.likes }} 点赞
              </span>
              <span class="stat-item">
                <i class="icon-comment"></i>
                {{ selectedContent.comments }} 评论
              </span>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <div class="empty-icon">🎧</div>
          <p>选择一个音频来查看详细内容</p>
        </div>
      </div>
    </div>

    <!-- 隐藏的音频播放器 -->
    <audio
      ref="audioElement"
      @loadedmetadata="onAudioLoaded"
      @timeupdate="onTimeUpdate"
      @ended="onAudioEnded"
      @error="onAudioError"
    ></audio>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'

export default {
  name: 'AudioSubscription',
  setup() {
    const subscriptionStore = useSubscriptionStore()
    
    // 响应式数据
    const audioElement = ref(null)

    // 计算属性
    const subscriptions = computed(() => subscriptionStore.currentSubscriptions)
    const selectedSubscription = computed(() => subscriptionStore.selectedSubscription)
    const selectedContent = computed(() => subscriptionStore.selectedContent)
    const audioPlayer = computed(() => subscriptionStore.audioPlayer)
    
    const currentAudios = computed(() => {
      if (!selectedSubscription.value) return []
      const content = subscriptionStore.getContentBySubscription(selectedSubscription.value.id)
      return content.audios || []
    })

    const progressPercentage = computed(() => {
      if (audioPlayer.value.duration === 0) return 0
      return (audioPlayer.value.currentTime / audioPlayer.value.duration) * 100
    })

    // 方法
    const handleSubscriptionSelect = (subscription) => {
      subscriptionStore.selectSubscription(subscription)
    }

    const handleAudioSelect = (audio) => {
      subscriptionStore.selectContent(audio)
    }

    const toggleAudioPlay = (audio) => {
      if (audioPlayer.value.currentTrack?.id === audio.id) {
        if (audioPlayer.value.isPlaying) {
          pauseAudio()
        } else {
          playAudio()
        }
      } else {
        loadAndPlayAudio(audio)
      }
    }

    const loadAndPlayAudio = (audio) => {
      if (audioElement.value) {
        audioElement.value.src = audio.url
        audioElement.value.load()
        subscriptionStore.playAudio(audio)
        audioElement.value.play()
      }
    }

    const playAudio = () => {
      if (audioElement.value) {
        audioElement.value.play()
        subscriptionStore.audioPlayer.isPlaying = true
      }
    }

    const pauseAudio = () => {
      if (audioElement.value) {
        audioElement.value.pause()
        subscriptionStore.pauseAudio()
      }
    }

    const seekAudio = (event) => {
      if (audioElement.value && audioPlayer.value.duration > 0) {
        const rect = event.currentTarget.getBoundingClientRect()
        const percentage = (event.clientX - rect.left) / rect.width
        const newTime = percentage * audioPlayer.value.duration
        audioElement.value.currentTime = newTime
        subscriptionStore.setAudioTime(newTime)
      }
    }

    const seekToChapter = (startTime) => {
      if (audioElement.value) {
        audioElement.value.currentTime = startTime
        subscriptionStore.setAudioTime(startTime)
      }
    }

    const onAudioLoaded = () => {
      if (audioElement.value) {
        subscriptionStore.audioPlayer.duration = audioElement.value.duration
      }
    }

    const onTimeUpdate = () => {
      if (audioElement.value) {
        subscriptionStore.setAudioTime(audioElement.value.currentTime)
      }
    }

    const onAudioEnded = () => {
      subscriptionStore.stopAudio()
    }

    const onAudioError = () => {
      console.error('音频播放错误')
      subscriptionStore.stopAudio()
    }

    const formatTime = (timeString) => {
      const date = new Date(timeString)
      const now = new Date()
      const diff = now - date
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      return date.toLocaleDateString()
    }

    const formatDuration = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }

    // 生命周期
    onMounted(() => {
      // 添加示例音频数据
      if (!subscriptionStore.contents[8]) {
        subscriptionStore.contents[8] = {
          audios: [
            {
              id: 401,
              title: '技术人的职业规划与成长',
              summary: '资深技术专家分享从初级工程师到技术领导者的成长路径，包括技能提升、团队管理等方面的经验。',
              description: '在这期节目中，我们邀请了多位技术大咖分享他们的职业成长经历。从技术深度到管理广度，从个人贡献者到团队领导者，每一个转变都充满挑战和机遇。我们将深入探讨技术人员在不同职业阶段应该关注的重点，以及如何在快速变化的技术环境中保持竞争力。',
              url: '/audio/career-growth.mp3',
              cover: '/img/career-cover.jpg',
              host: '技术大咖',
              publishTime: '2024-01-20T13:00:00Z',
              duration: 3600, // 60分钟
              plays: 25420,
              likes: 1892,
              comments: 256,
              tags: ['职业规划', '技术成长', '管理'],
              chapters: [
                { id: 1, title: '开场介绍', startTime: 0 },
                { id: 2, title: '初级工程师阶段', startTime: 300 },
                { id: 3, title: '高级工程师进阶', startTime: 900 },
                { id: 4, title: '技术领导力培养', startTime: 1800 },
                { id: 5, title: 'Q&A环节', startTime: 2700 }
              ]
            }
          ]
        }
      }
    })

    onUnmounted(() => {
      // 清理音频播放器
      if (audioElement.value) {
        audioElement.value.pause()
      }
    })

    return {
      audioElement,
      subscriptions,
      selectedSubscription,
      selectedContent,
      audioPlayer,
      currentAudios,
      progressPercentage,
      handleSubscriptionSelect,
      handleAudioSelect,
      toggleAudioPlay,
      seekAudio,
      seekToChapter,
      onAudioLoaded,
      onTimeUpdate,
      onAudioEnded,
      onAudioError,
      formatTime,
      formatDuration
    }
  }
}
</script>

<style lang="scss" scoped>
.audio-subscription {
  height: 100%;
}

.subscription-layout {
  display: grid;
  grid-template-columns: 260px 320px 1fr;
  height: calc(100vh - 180px);
  gap: 1px;
  background: #e5e7eb;

  @media (max-width: 1200px) {
    grid-template-columns: 240px 300px 1fr;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
    height: auto;
  }
}

// 左侧订阅列表（复用之前的样式）
.subscription-list {
  background: white;
  display: flex;
  flex-direction: column;

  .list-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }

    .add-btn {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 8px;
      background: #4f46e5;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: #4338ca;
        transform: scale(1.05);
      }
    }
  }

  .subscription-items {
    flex: 1;
    overflow-y: auto;
  }

  .subscription-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    gap: 0.75rem;

    &:hover {
      background: #f9fafb;
    }

    &.active {
      background: #eff6ff;
      border-left: 4px solid #4f46e5;
    }

    .subscription-avatar {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .subscription-info {
      flex: 1;
      min-width: 0;

      .subscription-name {
        margin: 0 0 0.25rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .subscription-desc {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .subscription-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .last-update {
          font-size: 0.75rem;
          color: #9ca3af;
        }

        .unread-count {
          background: #ef4444;
          color: white;
          font-size: 0.75rem;
          padding: 0.125rem 0.5rem;
          border-radius: 10px;
          min-width: 1.25rem;
          text-align: center;
        }
      }
    }
  }
}

// 中间音频预览
.audio-preview {
  background: white;
  display: flex;
  flex-direction: column;

  .preview-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .audio-list {
    flex: 1;
    overflow-y: auto;
  }

  .audio-item {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    gap: 0.75rem;

    &:hover {
      background: #f9fafb;
    }

    &.active {
      background: #eff6ff;
      border-left: 4px solid #4f46e5;
    }

    &.playing {
      background: #fef3c7;
      border-left: 4px solid #f59e0b;
    }

    .audio-cover {
      width: 60px;
      height: 60px;
      border-radius: 10px;
      overflow: hidden;
      position: relative;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .play-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.2s ease;

        .play-btn {
          width: 40px;
          height: 40px;
          border: none;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.9);
          color: #1f2937;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.25rem;
          transition: all 0.2s ease;

          &:hover {
            background: white;
            transform: scale(1.1);
          }
        }
      }

      &:hover .play-overlay {
        opacity: 1;
      }
    }

    .audio-info {
      flex: 1;
      min-width: 0;

      .audio-title {
        margin: 0 0 0.5rem 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .audio-meta {
        display: flex;
        gap: 1rem;
        margin-bottom: 0.75rem;
        font-size: 0.875rem;
        color: #6b7280;

        @media (max-width: 1200px) {
          flex-wrap: wrap;
          gap: 0.5rem;
        }
      }

      .audio-summary {
        margin: 0 0 1rem 0;
        font-size: 0.875rem;
        color: #4b5563;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .audio-stats {
        display: flex;
        gap: 1rem;
        font-size: 0.875rem;
        color: #6b7280;

        .plays, .likes {
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }
      }
    }
  }
}

// 右侧音频详情
.audio-detail {
  background: white;
  display: flex;
  flex-direction: column;

  .detail-content {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;

    .detail-header {
      display: flex;
      gap: 1.5rem;
      margin-bottom: 2rem;
      padding-bottom: 1.5rem;
      border-bottom: 1px solid #e5e7eb;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .audio-cover-large {
        width: 150px;
        height: 150px;
        border-radius: 16px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .audio-info-large {
        flex: 1;
        min-width: 0;

        .detail-title {
          margin: 0 0 1rem 0;
          font-size: 1.875rem;
          font-weight: 700;
          color: #1f2937;
          line-height: 1.3;
        }

        .detail-meta {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          margin-bottom: 1.5rem;
          font-size: 0.875rem;
          color: #6b7280;

          @media (max-width: 768px) {
            align-items: center;
          }
        }

        .audio-controls {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .main-play-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            background: #4f46e5;
            color: white;
            font-size: 1.125rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            align-self: flex-start;

            @media (max-width: 768px) {
              align-self: stretch;
            }

            &:hover {
              background: #4338ca;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
            }

            i {
              font-size: 1.25rem;
            }
          }

          .secondary-controls {
            display: flex;
            gap: 0.75rem;

            @media (max-width: 768px) {
              justify-content: center;
            }

            .control-btn {
              padding: 0.75rem 1rem;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              background: white;
              color: #6b7280;
              font-size: 0.875rem;
              cursor: pointer;
              transition: all 0.2s ease;
              display: flex;
              align-items: center;
              gap: 0.5rem;

              &:hover {
                border-color: #4f46e5;
                color: #4f46e5;
              }
            }
          }
        }
      }
    }

    .audio-progress {
      margin-bottom: 2rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 12px;

      .progress-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
      }

      .progress-bar {
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        cursor: pointer;
        position: relative;

        .progress-fill {
          height: 100%;
          background: #4f46e5;
          border-radius: 4px;
          transition: width 0.1s ease;
        }
      }
    }

    .detail-body {
      margin-bottom: 2rem;

      .audio-description {
        margin-bottom: 2rem;

        h3 {
          margin: 0 0 1rem 0;
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
        }

        p {
          margin: 0;
          font-size: 1rem;
          line-height: 1.7;
          color: #374151;
        }
      }

      .audio-chapters {
        h3 {
          margin: 0 0 1rem 0;
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
        }

        .chapter-list {
          .chapter-item {
            padding: 0.75rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            gap: 1rem;

            &:hover {
              border-color: #4f46e5;
              background: #f8f9fa;
            }

            .chapter-time {
              font-size: 0.875rem;
              color: #6b7280;
              font-weight: 500;
              min-width: 60px;
            }

            .chapter-title {
              font-size: 0.875rem;
              color: #1f2937;
              font-weight: 500;
            }
          }
        }
      }
    }

    .detail-footer {
      padding-top: 1.5rem;
      border-top: 1px solid #e5e7eb;

      .audio-tags {
        margin-bottom: 1rem;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .tag {
          background: #eff6ff;
          color: #1d4ed8;
          font-size: 0.875rem;
          padding: 0.375rem 0.75rem;
          border-radius: 8px;
          font-weight: 500;
        }
      }

      .audio-stats {
        display: flex;
        gap: 1.5rem;
        font-size: 0.875rem;
        color: #6b7280;

        @media (max-width: 768px) {
          flex-wrap: wrap;
          gap: 1rem;
        }

        .stat-item {
          display: flex;
          align-items: center;
          gap: 0.375rem;
        }
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  padding: 2rem;

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 1rem;
  }
}

// 图标
.icon-plus::before { content: "+"; }
.icon-play::before { content: "▶️"; }
.icon-pause::before { content: "⏸️"; }
.icon-heart::before { content: "❤️"; }
.icon-share::before { content: "🔗"; }
.icon-download::before { content: "⬇️"; }
.icon-headphones::before { content: "🎧"; }
.icon-comment::before { content: "💬"; }
</style>
