<template>
  <div class="audio-subscription">
    <!-- 三列布局容器 -->
    <div class="subscription-layout">
      <!-- 左侧：订阅列表 (18%) -->
      <div class="subscription-sidebar">
        <div class="sidebar-header">
          <h3>音频订阅</h3>
          <div class="header-actions">
            <button class="refresh-btn" @click="refreshSubscriptions" :disabled="loading">
              <i class="icon-refresh" :class="{ spinning: loading }">&#x1F504;</i>
            </button>
          </div>
        </div>

        <div class="subscription-list">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载中...</p>
          </div>
          
          <div v-else-if="subscriptions.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无订阅源</p>
          </div>
          
          <div
            v-for="subscription in subscriptions"
            :key="subscription"
            :class="['subscription-item', { active: selectedSubscription === subscription }]"
            @click="selectSubscription(subscription)"
          >
            <div class="subscription-info">
              <div class="subscription-icon">
                <i class="icon-audio"></i>
              </div>
              <div class="subscription-details">
                <h4 class="subscription-name">{{ subscription }}</h4>
              </div>
            </div>
            <div class="subscription-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：音频列表 (32%) -->
      <div class="audio-list">
        <div class="list-header">
          <h3>{{ selectedSubscription || '全部音频' }} ({{ audioCount }} 个音频)</h3>
        </div>

        <div class="audio-items">
          <div v-if="loadingAudios" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载音频中...</p>
          </div>
          
          <div v-else-if="filteredAudios.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无音频</p>
          </div>
          
          <div
            v-for="audio in filteredAudios"
            :key="audio.id"
            :class="['audio-item', { 
              active: selectedAudio?.id === audio.id, 
              playing: currentPlayingId === audio.id 
            }]"
            @click="selectAudio(audio)"
          >
            <div class="audio-cover">
              <img :src="audio.media?.[0]?.url || '/img/default-audio-cover.jpg'" :alt="audio.title">
              <div class="play-overlay">
                <button
                  class="play-btn"
                  @click.stop="togglePlay(audio)"
                  :disabled="audioLoading"
                >
                  <i v-if="currentPlayingId === audio.id && isPlaying" class="icon-pause"></i>
                  <i v-else class="icon-play"></i>
                </button>
              </div>
            </div>
            <div class="audio-content">
              <h4 class="audio-title">{{ audio.title }}</h4>
              <p class="audio-host">主播：{{ audio.host || audio.author }}</p>
              <div class="audio-meta">
                <span class="audio-duration">{{ formatDuration(audio.duration) }}</span>
                <span class="audio-date">{{ formatDate(audio.pubDate) }}</span>
                <span class="audio-plays">{{ formatPlays(audio.plays) }} 播放</span>
              </div>
            </div>
            <div class="audio-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>

        <!-- 音频播放器 -->
        <div v-if="currentAudio" class="audio-player">
          <div class="player-info">
            <img :src="currentAudio.media?.[0]?.url || '/img/default-audio-cover.jpg'" :alt="currentAudio.title" class="player-cover">
            <div class="player-details">
              <h4 class="player-title">{{ currentAudio.title }}</h4>
              <p class="player-host">{{ currentAudio.host || currentAudio.author }}</p>
            </div>
          </div>
          <div class="player-controls">
            <button class="control-btn" @click="previousTrack">
              <i class="icon-previous"></i>
            </button>
            <button class="control-btn play-pause" @click="toggleCurrentPlay" :disabled="audioLoading">
              <i v-if="isPlaying" class="icon-pause"></i>
              <i v-else class="icon-play"></i>
            </button>
            <button class="control-btn" @click="nextTrack">
              <i class="icon-next"></i>
            </button>
          </div>
          <div class="player-progress">
            <span class="time-current">{{ formatTime(currentTime) }}</span>
            <div class="progress-bar" @click="seekTo">
              <div class="progress-track">
                <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                <div class="progress-handle" :style="{ left: progressPercentage + '%' }"></div>
              </div>
            </div>
            <span class="time-total">{{ formatTime(duration) }}</span>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="page-btn"
          >
            <i class="icon-chevron-left"></i>
          </button>
          <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="page-btn"
          >
            <i class="icon-chevron-right"></i>
          </button>
        </div>
      </div>

      <!-- 右侧：音频详情 (50%) -->
      <div class="audio-detail">
        <div v-if="!selectedAudio" class="detail-placeholder">
          <i class="icon-audio-large"></i>
          <h3>选择音频查看详情</h3>
          <p>点击左侧音频列表中的任意音频查看详细信息</p>
        </div>
        
        <div v-else class="detail-content">
          <div class="detail-header">
            <div class="detail-cover">
              <img :src="selectedAudio.media?.[0]?.url || '/img/default-audio-cover.jpg'" :alt="selectedAudio.title">
            </div>
            <div class="detail-info">
              <h2 class="detail-title">{{ selectedAudio.title }}</h2>
              <p class="detail-host">主播：{{ selectedAudio.host || selectedAudio.author }}</p>
              <div class="detail-meta">
                <span class="detail-duration">时长：{{ formatDuration(selectedAudio.duration) }}</span>
                <span class="detail-date">发布：{{ formatDate(selectedAudio.pubDate) }}</span>
                <span class="detail-plays">播放：{{ formatPlays(selectedAudio.plays) }}</span>
              </div>
              <div class="detail-actions">
                <button class="action-btn primary" @click="togglePlay(selectedAudio)">
                  <i v-if="currentPlayingId === selectedAudio.id && isPlaying" class="icon-pause"></i>
                  <i v-else class="icon-play"></i>
                  {{ currentPlayingId === selectedAudio.id && isPlaying ? '暂停' : '播放' }}
                </button>
              </div>
            </div>
          </div>
          
          <div class="detail-body">
            <div v-if="loadingDetail" class="loading-state">
              <div class="loading-spinner"></div>
              <p>加载详情中...</p>
            </div>
            <div v-else-if="audioDescription" class="audio-description">
              <h4>节目简介</h4>
              <div class="description-content" v-html="audioDescription"></div>
            </div>
            <div v-else class="no-content">
              <p>暂无详细介绍</p>
            </div>

            <!-- 章节列表 -->
            <div v-if="selectedAudio.chapters && selectedAudio.chapters.length" class="chapters-list">
              <h4>章节列表</h4>
              <div class="chapters">
                <div
                  v-for="chapter in selectedAudio.chapters"
                  :key="chapter.id"
                  class="chapter-item"
                  @click="seekToChapter(chapter.startTime)"
                >
                  <div class="chapter-time">{{ formatTime(chapter.startTime) }}</div>
                  <div class="chapter-title">{{ chapter.title }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 音频元素 -->
    <audio
      ref="audioElement"
      @loadstart="audioLoading = true"
      @canplay="audioLoading = false"
      @timeupdate="updateTime"
      @ended="onAudioEnded"
      @error="onAudioError"
      preload="metadata"
    ></audio>
  </div>
</template>

<script>
import useAudioSubscription from './AudioSubscription.js'

export default {
  name: 'AudioSubscription',
  emits: ['stats-updated'],
  setup(props, { emit }) {
    return useAudioSubscription(emit)
  }
}
</script>

<style lang="scss" scoped>
@import './AudioSubscription.scss';

// 图标
.icon-audio::before { content: "🎵"; }
.icon-chevron-right::before { content: "›"; }
.icon-chevron-left::before { content: "‹"; }
.icon-empty::before { content: "🎵"; }
.icon-search::before { content: "🔍"; }
.icon-play::before { content: "▶"; }
.icon-pause::before { content: "⏸"; }
.icon-previous::before { content: "⏮"; }
.icon-next::before { content: "⏭"; }
.icon-audio-large::before { content: "🎵"; }
.icon-download::before { content: "⬇"; }
.icon-share::before { content: "📤"; }
.icon-heart::before { content: "♡"; }

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 公共状态样式
.loading-state,
.empty-state {
  padding: 2rem;
  text-align: center;
  color: #9ca3af;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}
</style>
