<template>
  <div class="video-subscription">
    <!-- 两列布局容器 -->
    <div class="subscription-layout">
      <!-- 左侧：视频订阅列表 (25%) -->
      <div class="subscription-sidebar">
        <div class="sidebar-header">
          <h3>视频订阅</h3>
          <div class="header-actions">
            <button class="refresh-btn" @click="refreshSubscriptions" :disabled="loading">
              <i class="icon-refresh" :class="{ spinning: loading }"></i>
            </button>
          </div>
        </div>

        <div class="subscription-list">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载中...</p>
          </div>
          
          <div v-else-if="subscriptions.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无订阅源</p>
          </div>
          
          <div
            v-for="subscription in subscriptions"
            :key="subscription"
            :class="['subscription-item', { active: selectedSubscription === subscription }]"
            @click="selectSubscription(subscription)"
          >
            <div class="subscription-info">
              <div class="subscription-icon">
                <i class="icon-video"></i>
              </div>
              <div class="subscription-details">
                <h4 class="subscription-name">{{ subscription }}</h4>
                <p class="subscription-count">{{ getSubscriptionCount(subscription) }} 个视频</p>
              </div>
            </div>
            <div class="subscription-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：视频展示区域 (75%) -->
      <div class="video-content">
        <div class="content-header">
          <div class="header-left">
            <h3>{{ selectedSubscription || '全部视频' }}</h3>
            <div class="search-box">
              <input
                v-model="searchKeyword"
                type="text"
                placeholder="搜索视频..."
                @input="handleSearch"
                class="search-input"
              >
              <i class="icon-search"></i>
            </div>
          </div>
          <div class="header-right">
            <div class="view-toggle">
              <button
                :class="['toggle-btn', { active: viewMode === 'grid' }]"
                @click="viewMode = 'grid'"
                title="网格视图"
              >
                <i class="icon-grid"></i>
              </button>
              <button
                :class="['toggle-btn', { active: viewMode === 'list' }]"
                @click="viewMode = 'list'"
                title="列表视图"
              >
                <i class="icon-list"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="video-container">
          <div v-if="loadingVideos" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载视频中...</p>
          </div>
          
          <div v-else-if="filteredVideos.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无视频</p>
          </div>
          
          <!-- 网格视图 -->
          <div v-else-if="viewMode === 'grid'" class="video-grid">
            <div
              v-for="video in filteredVideos"
              :key="video.id"
              class="video-card"
              @click="playVideo(video)"
            >
              <div class="video-thumbnail">
                <img :src="video.thumbnail || '/img/default-video-thumbnail.jpg'" :alt="video.title">
                <div class="play-overlay">
                  <button class="play-btn">
                    <i class="icon-play"></i>
                  </button>
                </div>
                <div class="video-duration">{{ formatDuration(video.duration) }}</div>
              </div>
              <div class="video-info">
                <h4 class="video-title">{{ video.title }}</h4>
                <p class="video-author">{{ video.author || video.channel }}</p>
                <div class="video-meta">
                  <span class="video-views">{{ formatViews(video.views) }} 观看</span>
                  <span class="video-date">{{ formatDate(video.pubDate) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 列表视图 -->
          <div v-else class="video-list">
            <div
              v-for="video in filteredVideos"
              :key="video.id"
              class="video-item"
              @click="playVideo(video)"
            >
              <div class="video-thumbnail">
                <img :src="video.thumbnail || '/img/default-video-thumbnail.jpg'" :alt="video.title">
                <div class="play-overlay">
                  <button class="play-btn">
                    <i class="icon-play"></i>
                  </button>
                </div>
                <div class="video-duration">{{ formatDuration(video.duration) }}</div>
              </div>
              <div class="video-content">
                <h4 class="video-title">{{ video.title }}</h4>
                <p class="video-description">{{ video.description || '暂无描述' }}</p>
                <div class="video-meta">
                  <span class="video-author">{{ video.author || video.channel }}</span>
                  <span class="video-views">{{ formatViews(video.views) }} 观看</span>
                  <span class="video-date">{{ formatDate(video.pubDate) }}</span>
                </div>
              </div>
              <div class="video-actions">
                <button class="action-btn" @click.stop="shareVideo(video)" title="分享">
                  <i class="icon-share"></i>
                </button>
                <button class="action-btn" @click.stop="favoriteVideo(video)" title="收藏">
                  <i class="icon-heart"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="page-btn"
          >
            <i class="icon-chevron-left"></i>
          </button>
          <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="page-btn"
          >
            <i class="icon-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 视频播放模态框 -->
    <div v-if="showVideoModal" class="video-modal" @click="closeVideoModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ currentVideo?.title }}</h3>
          <button class="close-btn" @click="closeVideoModal">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="video-player">
            <video
              ref="videoPlayer"
              :src="currentVideo?.url"
              controls
              autoplay
              @loadstart="videoLoading = true"
              @canplay="videoLoading = false"
              @error="onVideoError"
            ></video>
            <div v-if="videoLoading" class="video-loading">
              <div class="loading-spinner"></div>
              <p>视频加载中...</p>
            </div>
          </div>
          <div class="video-details">
            <div class="video-meta">
              <span class="video-author">{{ currentVideo?.author || currentVideo?.channel }}</span>
              <span class="video-views">{{ formatViews(currentVideo?.views) }} 观看</span>
              <span class="video-date">{{ formatDate(currentVideo?.pubDate) }}</span>
            </div>
            <div v-if="currentVideo?.description" class="video-description">
              <p>{{ currentVideo.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ApiClient } from '@/utils/api'

export default {
  name: 'VideoSubscription',
  emits: ['stats-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const loadingVideos = ref(false)
    const videoLoading = ref(false)
    const subscriptions = ref([])
    const videos = ref([])
    const selectedSubscription = ref(null)
    const searchKeyword = ref('')
    const viewMode = ref('grid') // 'grid' 或 'list'
    const currentPage = ref(1)
    const pageSize = ref(20)
    const totalCount = ref(0)
    
    // 视频播放相关
    const showVideoModal = ref(false)
    const currentVideo = ref(null)
    const videoPlayer = ref(null)

    // 计算属性
    const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
    
    const filteredVideos = computed(() => {
      if (!searchKeyword.value) return videos.value
      
      const keyword = searchKeyword.value.toLowerCase()
      return videos.value.filter(video =>
        video.title?.toLowerCase().includes(keyword) ||
        video.description?.toLowerCase().includes(keyword) ||
        video.author?.toLowerCase().includes(keyword) ||
        video.channel?.toLowerCase().includes(keyword)
      )
    })

    // 方法
    const refreshSubscriptions = async () => {
      loading.value = true
      try {
        const response = await ApiClient.get('http://localhost:8001/api/crawler/content/list?type=video')
        if (response.code === 200) {
          subscriptions.value = response.data.subscriptions || []
          if (subscriptions.value.length > 0 && !selectedSubscription.value) {
            selectedSubscription.value = subscriptions.value[0]
          }
          
          // 发送统计更新事件
          emit('stats-updated', {
            videoCount: response.data.total || 0,
            videoSubscriptionCount: subscriptions.value.length
          })
        }
      } catch (error) {
        console.error('获取订阅列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    const selectSubscription = async (subscription) => {
      if (selectedSubscription.value === subscription) return
      
      selectedSubscription.value = subscription
      currentPage.value = 1
      await loadVideos()
    }

    const loadVideos = async () => {
      if (!selectedSubscription.value) return
      
      loadingVideos.value = true
      try {
        const response = await ApiClient.get(
          `http://localhost:8001/api/crawler/content/by-subscription?type=video&taskName=${encodeURIComponent(selectedSubscription.value)}&page=${currentPage.value}&size=${pageSize.value}`
        )
        if (response.code === 200) {
          videos.value = response.data.contents || []
          totalCount.value = response.data.total || 0
        }
      } catch (error) {
        console.error('获取视频列表失败:', error)
      } finally {
        loadingVideos.value = false
      }
    }

    const playVideo = (video) => {
      currentVideo.value = video
      showVideoModal.value = true
    }

    const closeVideoModal = () => {
      showVideoModal.value = false
      if (videoPlayer.value) {
        videoPlayer.value.pause()
        videoPlayer.value.currentTime = 0
      }
      currentVideo.value = null
    }

    const onVideoError = () => {
      console.error('视频播放错误')
      videoLoading.value = false
    }

    const getSubscriptionCount = (subscription) => {
      return Math.floor(Math.random() * 50) + 5
    }

    const handleSearch = () => {
      // 搜索逻辑已在计算属性中处理
    }

    const changePage = (page) => {
      if (page < 1 || page > totalPages.value) return
      currentPage.value = page
      loadVideos()
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const formatDuration = (seconds) => {
      if (!seconds) return '00:00'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }

    const formatViews = (views) => {
      if (!views) return '0'
      if (views >= 10000) {
        return `${(views / 10000).toFixed(1)}万`
      }
      return views.toString()
    }

    const shareVideo = (video) => {
      console.log('分享视频:', video)
    }

    const favoriteVideo = (video) => {
      console.log('收藏视频:', video)
    }

    // 监听选中订阅的变化
    watch(selectedSubscription, () => {
      if (selectedSubscription.value) {
        loadVideos()
      }
    })

    // 组件挂载
    onMounted(() => {
      refreshSubscriptions()
    })

    return {
      loading,
      loadingVideos,
      videoLoading,
      subscriptions,
      videos,
      selectedSubscription,
      searchKeyword,
      viewMode,
      currentPage,
      totalPages,
      filteredVideos,
      showVideoModal,
      currentVideo,
      videoPlayer,
      refreshSubscriptions,
      selectSubscription,
      playVideo,
      closeVideoModal,
      getSubscriptionCount,
      handleSearch,
      changePage,
      formatDate,
      formatDuration,
      formatViews,
      shareVideo,
      favoriteVideo
    }
  }
}
</script>

<style lang="scss" scoped>
.video-subscription {
  height: 100vh;
  background: #f8fafc;
}

.subscription-layout {
  display: flex;
  height: 100%;
  gap: 1px;
  background: #e5e7eb;
}

// 左侧订阅列表 (25%)
.subscription-sidebar {
  width: 25%;
  background: white;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .header-actions {
      .refresh-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .subscription-list {
    flex: 1;
    overflow-y: auto;

    .subscription-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      .subscription-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;

        .subscription-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          background: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #6b7280;
        }

        .subscription-details {
          flex: 1;
          min-width: 0;

          .subscription-name {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 500;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .subscription-count {
            margin: 0;
            font-size: 0.75rem;
            color: #6b7280;
          }
        }
      }

      .subscription-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
      }
    }
  }
}

// 右侧视频内容区域 (75%)
.video-content {
  width: 75%;
  background: white;
  display: flex;
  flex-direction: column;

  .content-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;

    .header-left {
      flex: 1;

      h3 {
        margin: 0 0 1rem 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
      }

      .search-box {
        position: relative;
        max-width: 300px;

        .search-input {
          width: 100%;
          padding: 0.5rem 2.5rem 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 0.875rem;

          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }

        .icon-search {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          color: #9ca3af;
        }
      }
    }

    .header-right {
      .view-toggle {
        display: flex;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        overflow: hidden;

        .toggle-btn {
          padding: 0.5rem 0.75rem;
          border: none;
          background: white;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #f9fafb;
          }

          &.active {
            background: #3b82f6;
            color: white;
          }

          &:not(:last-child) {
            border-right: 1px solid #d1d5db;
          }
        }
      }
    }
  }

  .video-container {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
  }
}

// 视频网格视图
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;

  .video-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .video-thumbnail {
      position: relative;
      width: 100%;
      height: 160px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .play-overlay {
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .play-btn {
          width: 60px;
          height: 60px;
          border: none;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.9);
          color: #1f2937;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          transition: all 0.2s ease;

          &:hover {
            background: white;
            transform: scale(1.1);
          }
        }
      }

      .video-duration {
        position: absolute;
        bottom: 0.5rem;
        right: 0.5rem;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
      }

      &:hover {
        .play-overlay {
          opacity: 1;
        }

        img {
          transform: scale(1.05);
        }
      }
    }

    .video-info {
      padding: 1rem;

      .video-title {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        font-weight: 500;
        color: #1f2937;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .video-author {
        margin: 0 0 0.5rem 0;
        font-size: 0.75rem;
        color: #6b7280;
      }

      .video-meta {
        display: flex;
        gap: 0.5rem;
        font-size: 0.75rem;
        color: #9ca3af;

        span {
          white-space: nowrap;
        }
      }
    }
  }
}

// 视频列表视图
.video-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .video-item {
    display: flex;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .video-thumbnail {
      position: relative;
      width: 200px;
      height: 120px;
      flex-shrink: 0;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .play-overlay {
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .play-btn {
          width: 48px;
          height: 48px;
          border: none;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.9);
          color: #1f2937;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.25rem;

          &:hover {
            background: white;
            transform: scale(1.1);
          }
        }
      }

      .video-duration {
        position: absolute;
        bottom: 0.5rem;
        right: 0.5rem;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
      }

      &:hover {
        .play-overlay {
          opacity: 1;
        }

        img {
          transform: scale(1.05);
        }
      }
    }

    .video-content {
      flex: 1;
      padding: 1rem;
      display: flex;
      flex-direction: column;

      .video-title {
        margin: 0 0 0.5rem 0;
        font-size: 1rem;
        font-weight: 500;
        color: #1f2937;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .video-description {
        margin: 0 0 0.75rem 0;
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        flex: 1;
      }

      .video-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.75rem;
        color: #9ca3af;
        margin-top: auto;

        span {
          white-space: nowrap;
        }
      }
    }

    .video-actions {
      padding: 1rem;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      align-items: center;

      .action-btn {
        width: 32px;
        height: 32px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
        }
      }
    }
  }
}

// 分页
.pagination {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;

  .page-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      border-color: #3b82f6;
      color: #3b82f6;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .page-info {
    font-size: 0.875rem;
    color: #6b7280;
  }
}

// 视频播放模态框
.video-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;

  .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .modal-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        flex: 1;
        margin-right: 1rem;
      }

      .close-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }
      }
    }

    .modal-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;

      .video-player {
        position: relative;
        background: #000;

        video {
          width: 100%;
          height: auto;
          max-height: 60vh;
        }

        .video-loading {
          position: absolute;
          inset: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: white;

          .loading-spinner {
            width: 48px;
            height: 48px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
          }

          p {
            margin: 0;
            font-size: 0.875rem;
          }
        }
      }

      .video-details {
        padding: 1.5rem;

        .video-meta {
          display: flex;
          gap: 1rem;
          margin-bottom: 1rem;
          font-size: 0.875rem;
          color: #6b7280;
        }

        .video-description {
          p {
            margin: 0;
            line-height: 1.6;
            color: #374151;
          }
        }
      }
    }
  }
}

// 公共状态样式
.loading-state,
.empty-state {
  padding: 2rem;
  text-align: center;
  color: #9ca3af;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 图标
.icon-refresh::before { content: "🔄"; }
.icon-video::before { content: "🎥"; }
.icon-chevron-right::before { content: "›"; }
.icon-chevron-left::before { content: "‹"; }
.icon-empty::before { content: "🎥"; }
.icon-search::before { content: "🔍"; }
.icon-grid::before { content: "⊞"; }
.icon-list::before { content: "☰"; }
.icon-play::before { content: "▶"; }
.icon-share::before { content: "📤"; }
.icon-heart::before { content: "♡"; }
.icon-close::before { content: "✕"; }
</style>
