<template>
  <div class="video-subscription">
    <!-- 两列布局 -->
    <div class="subscription-layout">
      <!-- 左侧：订阅列表 -->
      <div class="subscription-list">
        <div class="list-header">
          <h3>视频订阅</h3>
          <button class="add-btn" title="添加订阅">
            <i class="icon-plus"></i>
          </button>
        </div>
        
        <div class="subscription-items">
          <div
            v-for="subscription in subscriptions"
            :key="subscription.id"
            :class="['subscription-item', { active: selectedSubscription?.id === subscription.id }]"
            @click="handleSubscriptionSelect(subscription)"
          >
            <div class="subscription-avatar">
              <img :src="subscription.avatar" :alt="subscription.name" />
            </div>
            <div class="subscription-info">
              <h4 class="subscription-name">{{ subscription.name }}</h4>
              <p class="subscription-desc">{{ subscription.description }}</p>
              <div class="subscription-meta">
                <span class="last-update">{{ formatTime(subscription.lastUpdate) }}</span>
                <span v-if="subscription.unreadCount" class="unread-count">
                  {{ subscription.unreadCount }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：视频播放区域 -->
      <div class="video-player-area">
        <div class="player-header">
          <h3 v-if="selectedSubscription">{{ selectedSubscription.name }}</h3>
          <h3 v-else>选择订阅查看视频</h3>
        </div>

        <div v-if="selectedSubscription" class="video-content">
          <!-- 主视频播放器 -->
          <div class="main-player">
            <div v-if="currentVideo" class="video-player">
              <video
                ref="videoPlayer"
                :src="currentVideo.url"
                :poster="currentVideo.thumbnail"
                controls
                @loadedmetadata="onVideoLoaded"
                @timeupdate="onTimeUpdate"
                @ended="onVideoEnded"
              >
                您的浏览器不支持视频播放。
              </video>
              
              <div class="video-info">
                <h2 class="video-title">{{ currentVideo.title }}</h2>
                <div class="video-meta">
                  <div class="meta-left">
                    <span class="author">{{ currentVideo.author }}</span>
                    <span class="upload-time">{{ formatTime(currentVideo.uploadTime) }}</span>
                    <span class="duration">{{ formatDuration(currentVideo.duration) }}</span>
                  </div>
                  <div class="video-actions">
                    <button class="action-btn like-btn">
                      <i class="icon-heart"></i>
                      {{ currentVideo.likes }}
                    </button>
                    <button class="action-btn share-btn">
                      <i class="icon-share"></i>
                      分享
                    </button>
                    <button class="action-btn download-btn">
                      <i class="icon-download"></i>
                      下载
                    </button>
                  </div>
                </div>
                <p class="video-description">{{ currentVideo.description }}</p>
              </div>
            </div>
            
            <div v-else class="no-video-selected">
              <div class="placeholder-icon">🎥</div>
              <p>请从下方选择一个视频开始播放</p>
            </div>
          </div>

          <!-- 视频列表 -->
          <div class="video-list">
            <div class="list-header">
              <h4>视频列表</h4>
              <span class="video-count">{{ currentVideos.length }} 个视频</span>
            </div>
            
            <div class="video-items">
              <div
                v-for="video in currentVideos"
                :key="video.id"
                :class="['video-item', { active: currentVideo?.id === video.id, playing: isPlaying && currentVideo?.id === video.id }]"
                @click="selectVideo(video)"
              >
                <div class="video-thumbnail">
                  <img :src="video.thumbnail" :alt="video.title" />
                  <div class="play-overlay">
                    <i class="icon-play"></i>
                  </div>
                  <span class="video-duration">{{ formatDuration(video.duration) }}</span>
                </div>
                
                <div class="video-details">
                  <h5 class="video-title">{{ video.title }}</h5>
                  <div class="video-meta">
                    <span class="author">{{ video.author }}</span>
                    <span class="upload-time">{{ formatTime(video.uploadTime) }}</span>
                  </div>
                  <div class="video-stats">
                    <span class="views">
                      <i class="icon-eye"></i>
                      {{ video.views }}
                    </span>
                    <span class="likes">
                      <i class="icon-heart"></i>
                      {{ video.likes }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <div class="empty-icon">📺</div>
          <p>请从左侧选择一个订阅来查看视频</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'

export default {
  name: 'VideoSubscription',
  setup() {
    const subscriptionStore = useSubscriptionStore()
    
    // 响应式数据
    const videoPlayer = ref(null)
    const currentVideo = ref(null)
    const isPlaying = ref(false)
    const currentTime = ref(0)
    const duration = ref(0)

    // 计算属性
    const subscriptions = computed(() => subscriptionStore.currentSubscriptions)
    const selectedSubscription = computed(() => subscriptionStore.selectedSubscription)
    
    const currentVideos = computed(() => {
      if (!selectedSubscription.value) return []
      const content = subscriptionStore.getContentBySubscription(selectedSubscription.value.id)
      return content.videos || []
    })

    // 方法
    const handleSubscriptionSelect = (subscription) => {
      subscriptionStore.selectSubscription(subscription)
      currentVideo.value = null
      isPlaying.value = false
    }

    const selectVideo = (video) => {
      currentVideo.value = video
      isPlaying.value = false
      // 重置播放器
      if (videoPlayer.value) {
        videoPlayer.value.currentTime = 0
      }
    }

    const onVideoLoaded = () => {
      if (videoPlayer.value) {
        duration.value = videoPlayer.value.duration
      }
    }

    const onTimeUpdate = () => {
      if (videoPlayer.value) {
        currentTime.value = videoPlayer.value.currentTime
        isPlaying.value = !videoPlayer.value.paused
      }
    }

    const onVideoEnded = () => {
      isPlaying.value = false
      // 自动播放下一个视频
      const currentIndex = currentVideos.value.findIndex(v => v.id === currentVideo.value.id)
      if (currentIndex < currentVideos.value.length - 1) {
        selectVideo(currentVideos.value[currentIndex + 1])
        setTimeout(() => {
          if (videoPlayer.value) {
            videoPlayer.value.play()
          }
        }, 100)
      }
    }

    const formatTime = (timeString) => {
      const date = new Date(timeString)
      const now = new Date()
      const diff = now - date
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      return date.toLocaleDateString()
    }

    const formatDuration = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }

    // 生命周期
    onMounted(() => {
      // 添加示例视频数据
      if (!subscriptionStore.contents[6]) {
        subscriptionStore.contents[6] = {
          videos: [
            {
              id: 301,
              title: 'Vue 3 Composition API 深度解析',
              description: '详细讲解Vue 3 Composition API的核心概念和实际应用，帮助开发者快速掌握新特性。',
              url: '/videos/vue3-composition-api.mp4',
              thumbnail: '/img/vue3-thumb.jpg',
              author: '前端大师',
              uploadTime: '2024-01-20T12:00:00Z',
              duration: 1800, // 30分钟
              views: 15420,
              likes: 892,
              comments: 156
            },
            {
              id: 302,
              title: 'React Hooks 最佳实践',
              description: '分享React Hooks在实际项目中的最佳实践和常见陷阱，提升开发效率。',
              url: '/videos/react-hooks.mp4',
              thumbnail: '/img/react-thumb.jpg',
              author: 'React专家',
              uploadTime: '2024-01-20T10:30:00Z',
              duration: 2100, // 35分钟
              views: 12350,
              likes: 743,
              comments: 89
            }
          ]
        }
      }
    })

    onUnmounted(() => {
      // 清理视频播放器
      if (videoPlayer.value) {
        videoPlayer.value.pause()
      }
    })

    return {
      videoPlayer,
      currentVideo,
      isPlaying,
      currentTime,
      duration,
      subscriptions,
      selectedSubscription,
      currentVideos,
      handleSubscriptionSelect,
      selectVideo,
      onVideoLoaded,
      onTimeUpdate,
      onVideoEnded,
      formatTime,
      formatDuration
    }
  }
}
</script>

<style lang="scss" scoped>
.video-subscription {
  height: 100%;
}

.subscription-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  height: 80vh;
  gap: 1px;
  background: #e5e7eb;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    height: auto;
  }
}

// 左侧订阅列表（复用之前的样式）
.subscription-list {
  background: white;
  display: flex;
  flex-direction: column;

  .list-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }

    .add-btn {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 8px;
      background: #4f46e5;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: #4338ca;
        transform: scale(1.05);
      }
    }
  }

  .subscription-items {
    flex: 1;
    overflow-y: auto;
  }

  .subscription-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    gap: 1rem;

    &:hover {
      background: #f9fafb;
    }

    &.active {
      background: #eff6ff;
      border-left: 4px solid #4f46e5;
    }

    .subscription-avatar {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .subscription-info {
      flex: 1;
      min-width: 0;

      .subscription-name {
        margin: 0 0 0.25rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .subscription-desc {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .subscription-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .last-update {
          font-size: 0.75rem;
          color: #9ca3af;
        }

        .unread-count {
          background: #ef4444;
          color: white;
          font-size: 0.75rem;
          padding: 0.125rem 0.5rem;
          border-radius: 10px;
          min-width: 1.25rem;
          text-align: center;
        }
      }
    }
  }
}

// 右侧视频播放区域
.video-player-area {
  background: white;
  display: flex;
  flex-direction: column;

  .player-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .video-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .main-player {
    flex: 1;
    min-height: 400px;
    display: flex;
    flex-direction: column;

    .video-player {
      flex: 1;
      display: flex;
      flex-direction: column;

      video {
        width: 100%;
        height: 300px;
        background: #000;
        object-fit: contain;

        @media (max-width: 768px) {
          height: 200px;
        }
      }

      .video-info {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;

        .video-title {
          margin: 0 0 1rem 0;
          font-size: 1.5rem;
          font-weight: 600;
          color: #1f2937;
          line-height: 1.3;
        }

        .video-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
          gap: 1rem;

          @media (max-width: 768px) {
            flex-direction: column;
            align-items: stretch;
          }

          .meta-left {
            display: flex;
            gap: 1rem;
            font-size: 0.875rem;
            color: #6b7280;

            @media (max-width: 768px) {
              justify-content: center;
              flex-wrap: wrap;
            }
          }

          .video-actions {
            display: flex;
            gap: 0.5rem;

            .action-btn {
              padding: 0.5rem 1rem;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              background: white;
              color: #6b7280;
              font-size: 0.875rem;
              cursor: pointer;
              transition: all 0.2s ease;
              display: flex;
              align-items: center;
              gap: 0.25rem;

              &:hover {
                border-color: #4f46e5;
                color: #4f46e5;
              }

              &.like-btn:hover {
                border-color: #ef4444;
                color: #ef4444;
              }
            }
          }
        }

        .video-description {
          margin: 0;
          font-size: 0.875rem;
          color: #4b5563;
          line-height: 1.6;
        }
      }
    }

    .no-video-selected {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #9ca3af;
      background: #f9fafb;

      .placeholder-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
      }

      p {
        margin: 0;
        font-size: 1.125rem;
      }
    }
  }

  .video-list {
    border-top: 1px solid #e5e7eb;
    max-height: 300px;
    display: flex;
    flex-direction: column;

    .list-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h4 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
      }

      .video-count {
        font-size: 0.875rem;
        color: #6b7280;
      }
    }

    .video-items {
      flex: 1;
      overflow-y: auto;
    }

    .video-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      gap: 1rem;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-left: 4px solid #4f46e5;
      }

      &.playing {
        background: #fef3c7;
        border-left: 4px solid #f59e0b;
      }

      .video-thumbnail {
        width: 120px;
        height: 68px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 32px;
          height: 32px;
          background: rgba(0, 0, 0, 0.7);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          opacity: 0;
          transition: all 0.2s ease;
        }

        .video-duration {
          position: absolute;
          bottom: 4px;
          right: 4px;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          font-size: 0.75rem;
          padding: 0.125rem 0.375rem;
          border-radius: 4px;
        }

        &:hover .play-overlay {
          opacity: 1;
        }
      }

      .video-details {
        flex: 1;
        min-width: 0;

        .video-title {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
          line-height: 1.3;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .video-meta {
          display: flex;
          gap: 1rem;
          margin-bottom: 0.5rem;
          font-size: 0.875rem;
          color: #6b7280;
        }

        .video-stats {
          display: flex;
          gap: 1rem;
          font-size: 0.75rem;
          color: #9ca3af;

          .views, .likes {
            display: flex;
            align-items: center;
            gap: 0.25rem;
          }
        }
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  padding: 2rem;

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 1rem;
  }
}

// 图标
.icon-plus::before { content: "+"; }
.icon-play::before { content: "▶️"; }
.icon-heart::before { content: "❤️"; }
.icon-share::before { content: "🔗"; }
.icon-download::before { content: "⬇️"; }
.icon-eye::before { content: "👁️"; }
</style>
