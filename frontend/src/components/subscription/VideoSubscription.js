import { ref, computed, onMounted, watch } from 'vue'
import { ApiClient } from '@/utils/api'

export default {
  name: 'VideoSubscription',
  emits: ['stats-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const loadingVideos = ref(false)
    const videoLoading = ref(false)
    const subscriptions = ref([])
    const videos = ref([])
    const selectedSubscription = ref(null)
    const searchKeyword = ref('')
    const viewMode = ref('grid') // 'grid' 或 'list'
    const currentPage = ref(1)
    const pageSize = ref(20)
    const totalCount = ref(0) // 用于分页的总数
    const subscriptionTotalCount = ref(0) // 当前选中订阅的视频总数
    const subscriptionCounts = ref({}) // 存储每个订阅的视频数量
    
    // 视频播放相关
    const showVideoModal = ref(false)
    const currentVideo = ref(null)
    const videoPlayer = ref(null)

    // 计算属性
    const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
    
    const filteredVideos = computed(() => {
      if (!searchKeyword.value) return videos.value
      
      const keyword = searchKeyword.value.toLowerCase()
      return videos.value.filter(video =>
        video.title?.toLowerCase().includes(keyword) ||
        video.description?.toLowerCase().includes(keyword) ||
        video.author?.toLowerCase().includes(keyword) ||
        video.channel?.toLowerCase().includes(keyword)
      )
    })

    // 方法
    const refreshSubscriptions = async () => {
      loading.value = true
      try {
        const response = await ApiClient.get('http://localhost:8001/api/crawler/content/list?type=video')
        if (response.code === 200) {
          subscriptions.value = response.data.subscriptions || []
          if (subscriptions.value.length > 0 && !selectedSubscription.value) {
            selectedSubscription.value = subscriptions.value[0]
          }
          
          // 获取每个订阅的视频数量
          if (response.data.subscriptionCounts) {
            subscriptionCounts.value = response.data.subscriptionCounts
          } else {
            // 如果后端没有提供订阅数量，则使用默认值
            subscriptions.value.forEach(sub => {
              subscriptionCounts.value[sub] = Math.floor(Math.random() * 50) + 5
            })
          }
          
          // 发送统计更新事件
          emit('stats-updated', {
            videoCount: response.data.total || 0,
            videoSubscriptionCount: subscriptions.value.length
          })
        }
      } catch (error) {
        console.error('获取订阅列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    const selectSubscription = async (subscription) => {
      if (selectedSubscription.value === subscription) return
      
      selectedSubscription.value = subscription
      currentPage.value = 1
      await loadVideos()
    }

    const loadVideos = async () => {
      if (!selectedSubscription.value) return
      
      loadingVideos.value = true
      try {
        const response = await ApiClient.get(
          `http://localhost:8001/api/crawler/content/by-subscription?type=video&taskName=${encodeURIComponent(selectedSubscription.value)}&page=${currentPage.value}&size=${pageSize.value}`
        )
        if (response.code === 200) {
          videos.value = response.data.contents || []
          totalCount.value = response.data.total || 0
          subscriptionTotalCount.value = response.data.totalCount || response.data.total || 0
        }
      } catch (error) {
        console.error('获取视频列表失败:', error)
      } finally {
        loadingVideos.value = false
      }
    }

    const playVideo = (video) => {
      // 详细记录视频对象
      console.log('播放视频对象:', JSON.stringify(video, null, 2));
      
      // 创建一个新的视频对象
      const processedVideo = { ...video };
      
      // 确保视频对象有 attachments 属性
      if (!processedVideo.attachments || processedVideo.attachments.length === 0) {
        console.warn('视频缺少 attachments 数据，尝试从其他属性创建');
        
        // 尝试从 media 属性获取 URL
        let url = '';
        if (processedVideo.media && processedVideo.media.length > 0) {
          url = processedVideo.media[0].url || '';
          console.log('从 media 属性获取 URL:', url);
        }
        
        // 如果没有 media 属性，尝试从 url 属性获取
        if (!url && processedVideo.url) {
          url = processedVideo.url;
          console.log('从 url 属性获取 URL:', url);
        }
        
        // 创建 attachments 属性
        processedVideo.attachments = [{
          url: url,
          duration_in_seconds: processedVideo.duration || 0,
          type: 'video'
        }];
      }
      
      // 检查 attachments 中是否有 URL
      if (processedVideo.attachments && processedVideo.attachments.length > 0) {
        console.log('原始 attachments.url:', processedVideo.attachments[0].url);
        
        // 如果 URL 不是以 http 开头，添加基础 URL
        const url = processedVideo.attachments[0].url;
        if (url) {
          if (!url.startsWith('http')) {
            console.log('添加基础URL到 attachments.url');
            processedVideo.attachments[0].url = `http://localhost:8001${url}`;
          }
        } else {
          console.error('attachments.url 为空');
        }
      }
      
      currentVideo.value = processedVideo;
      console.log('处理后的视频对象:', JSON.stringify(currentVideo.value, null, 2));
      
      // 检查是否有有效的 URL
      if (!currentVideo.value.attachments?.[0]?.url) {
        console.error('无法找到有效的视频URL');
      } else {
        console.log('最终视频URL:', currentVideo.value.attachments[0].url);
      }
      
      // 延迟显示模态框，确保数据已经处理完成
      setTimeout(() => {
        showVideoModal.value = true;
      }, 100);
    }

    const closeVideoModal = () => {
      showVideoModal.value = false
      if (videoPlayer.value) {
        videoPlayer.value.pause()
        videoPlayer.value.currentTime = 0
      }
      currentVideo.value = null
    }

    const onVideoError = (event) => {
      console.error('视频播放错误:', event)
      console.error('视频对象:', currentVideo.value)
      console.error('视频URL:', currentVideo.value?.attachments?.[0]?.url)
      
      // 尝试修复 URL
      if (currentVideo.value && currentVideo.value.attachments && currentVideo.value.attachments.length > 0) {
        // 如果视频URL是相对路径，尝试添加基础URL
        const currentUrl = currentVideo.value.attachments[0].url;
        if (currentUrl && !currentUrl.startsWith('http')) {
          console.log('尝试添加基础URL')
          currentVideo.value.attachments[0].url = `http://localhost:8001${currentUrl}`;
        }
      }
      
      videoLoading.value = false;
    }

    // 将视频 URL 转换为嵌入 URL
    const getEmbedUrl = (url) => {
      if (!url) return '';
      
      // 检查是否是 YouTube URL
      const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i;
      const youtubeMatch = url.match(youtubeRegex);
      
      if (youtubeMatch && youtubeMatch[1]) {
        // 返回 YouTube 嵌入 URL
        return `https://www.youtube-nocookie.com/embed/${youtubeMatch[1]}?controls=1&autoplay=1&mute=0`;
      }
      
      // 如果不是 YouTube URL，尝试使用 iframe 嵌入原始 URL
      // 确保 URL 是完整的
      if (!url.startsWith('http')) {
        url = `http://localhost:8001${url}`;
      }
      
      return url;
    };

    const getSubscriptionCount = (subscription) => {
      if (subscription === selectedSubscription.value) {
        return subscriptionTotalCount.value;
      }
      return subscriptionCounts.value[subscription] || 0;
    }

    const handleSearch = () => {
      // 搜索逻辑已在计算属性中处理
    }

    const changePage = (page) => {
      if (page < 1 || page > totalPages.value) return
      currentPage.value = page
      loadVideos()
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const formatDuration = (seconds) => {
      if (!seconds) return '00:00'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }

    const formatViews = (views) => {
      if (!views) return '0'
      if (views >= 10000) {
        return `${(views / 10000).toFixed(1)}万`
      }
      return views.toString()
    }

    const shareVideo = (video) => {
      console.log('分享视频:', video)
    }

    const favoriteVideo = (video) => {
      console.log('收藏视频:', video)
    }

    // 监听选中订阅的变化
    watch(selectedSubscription, () => {
      if (selectedSubscription.value) {
        loadVideos()
      }
    })

    // 组件挂载
    onMounted(() => {
      refreshSubscriptions()
    })

    return {
      loading,
      loadingVideos,
      videoLoading,
      subscriptions,
      videos,
      selectedSubscription,
      searchKeyword,
      viewMode,
      currentPage,
      totalPages,
      filteredVideos,
      showVideoModal,
      currentVideo,
      videoPlayer,
      refreshSubscriptions,
      selectSubscription,
      playVideo,
      closeVideoModal,
      getSubscriptionCount,
      subscriptionCounts,
      subscriptionTotalCount,
      getEmbedUrl,
      handleSearch,
      changePage,
      formatDate,
      formatDuration,
      formatViews,
      shareVideo,
      favoriteVideo,
      onVideoError
    }
  }
}
