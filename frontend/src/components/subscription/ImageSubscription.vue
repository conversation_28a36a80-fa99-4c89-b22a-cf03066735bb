<template>
  <div class="image-subscription">
    <!-- 两列布局 -->
    <div class="subscription-layout">
      <!-- 左侧：订阅列表 -->
      <div class="subscription-list">
        <div class="list-header">
          <h3>图片订阅</h3>
          <button class="add-btn" title="添加订阅">
            <i class="icon-plus"></i>
          </button>
        </div>
        
        <div class="subscription-items">
          <div
            v-for="subscription in subscriptions"
            :key="subscription.id"
            :class="['subscription-item', { active: selectedSubscription?.id === subscription.id }]"
            @click="handleSubscriptionSelect(subscription)"
          >
            <div class="subscription-avatar">
              <img :src="subscription.avatar" :alt="subscription.name" />
            </div>
            <div class="subscription-info">
              <h4 class="subscription-name">{{ subscription.name }}</h4>
              <p class="subscription-desc">{{ subscription.description }}</p>
              <div class="subscription-meta">
                <span class="last-update">{{ formatTime(subscription.lastUpdate) }}</span>
                <span v-if="subscription.unreadCount" class="unread-count">
                  {{ subscription.unreadCount }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：图片展示网格 -->
      <div class="image-gallery">
        <div class="gallery-header">
          <div class="header-info">
            <h3 v-if="selectedSubscription">{{ selectedSubscription.name }}</h3>
            <h3 v-else>选择订阅查看图片</h3>
            <p v-if="selectedSubscription && currentImages.length" class="image-count">
              共 {{ currentImages.length }} 张图片
            </p>
          </div>
          
          <div v-if="selectedSubscription" class="view-controls">
            <button 
              :class="['view-btn', { active: viewMode === 'grid' }]"
              @click="viewMode = 'grid'"
              title="网格视图"
            >
              <i class="icon-grid"></i>
            </button>
            <button 
              :class="['view-btn', { active: viewMode === 'masonry' }]"
              @click="viewMode = 'masonry'"
              title="瀑布流视图"
            >
              <i class="icon-masonry"></i>
            </button>
          </div>
        </div>

        <div v-if="selectedSubscription" class="gallery-content">
          <div :class="['image-grid', viewMode]">
            <div
              v-for="image in currentImages"
              :key="image.id"
              class="image-item"
              @click="openImageModal(image)"
            >
              <div class="image-wrapper">
                <img 
                  :src="image.thumbnail" 
                  :alt="image.title"
                  @load="onImageLoad"
                  @error="onImageError"
                />
                <div class="image-overlay">
                  <div class="image-info">
                    <h4 class="image-title">{{ image.title }}</h4>
                    <p class="image-photographer">{{ image.photographer }}</p>
                  </div>
                  <div class="image-actions">
                    <button class="action-btn like-btn">
                      <i class="icon-heart"></i>
                      {{ image.likes }}
                    </button>
                    <button class="action-btn download-btn">
                      <i class="icon-download"></i>
                      {{ image.downloads }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <div class="empty-icon">🖼️</div>
          <p>请从左侧选择一个订阅来查看图片</p>
        </div>
      </div>
    </div>

    <!-- 图片模态框 -->
    <Transition name="modal">
      <div v-if="selectedImage" class="image-modal" @click="closeImageModal">
        <div class="modal-content" @click.stop>
          <button class="close-btn" @click="closeImageModal">
            <i class="icon-close"></i>
          </button>
          
          <div class="modal-image">
            <img :src="selectedImage.url" :alt="selectedImage.title" />
          </div>
          
          <div class="modal-info">
            <h3 class="modal-title">{{ selectedImage.title }}</h3>
            <p class="modal-description">{{ selectedImage.description }}</p>
            
            <div class="modal-meta">
              <div class="meta-item">
                <span class="meta-label">摄影师：</span>
                <span class="meta-value">{{ selectedImage.photographer }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">上传时间：</span>
                <span class="meta-value">{{ formatTime(selectedImage.uploadTime) }}</span>
              </div>
            </div>
            
            <div class="modal-actions">
              <button class="action-btn primary like-btn">
                <i class="icon-heart"></i>
                点赞 ({{ selectedImage.likes }})
              </button>
              <button class="action-btn primary download-btn">
                <i class="icon-download"></i>
                下载
              </button>
              <button class="action-btn share-btn">
                <i class="icon-share"></i>
                分享
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'

export default {
  name: 'ImageSubscription',
  setup() {
    const subscriptionStore = useSubscriptionStore()
    
    // 响应式数据
    const viewMode = ref('grid') // 'grid' | 'masonry'
    const selectedImage = ref(null)

    // 计算属性
    const subscriptions = computed(() => subscriptionStore.currentSubscriptions)
    const selectedSubscription = computed(() => subscriptionStore.selectedSubscription)
    
    const currentImages = computed(() => {
      if (!selectedSubscription.value) return []
      const content = subscriptionStore.getContentBySubscription(selectedSubscription.value.id)
      return content.images || []
    })

    // 方法
    const handleSubscriptionSelect = (subscription) => {
      subscriptionStore.selectSubscription(subscription)
    }

    const openImageModal = (image) => {
      selectedImage.value = image
      document.body.style.overflow = 'hidden'
    }

    const closeImageModal = () => {
      selectedImage.value = null
      document.body.style.overflow = 'auto'
    }

    const onImageLoad = (event) => {
      event.target.classList.add('loaded')
    }

    const onImageError = (event) => {
      event.target.src = '/img/placeholder.jpg'
    }

    const formatTime = (timeString) => {
      const date = new Date(timeString)
      const now = new Date()
      const diff = now - date
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      return date.toLocaleDateString()
    }

    return {
      viewMode,
      selectedImage,
      subscriptions,
      selectedSubscription,
      currentImages,
      handleSubscriptionSelect,
      openImageModal,
      closeImageModal,
      onImageLoad,
      onImageError,
      formatTime
    }
  }
}
</script>

<style lang="scss" scoped>
.image-subscription {
  height: 100%;
}

.subscription-layout {
  display: grid;
  grid-template-columns: 260px 1fr;
  height: calc(100vh - 180px);
  gap: 1px;
  background: #e5e7eb;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    height: auto;
  }
}

// 左侧订阅列表（复用文章订阅的样式）
.subscription-list {
  background: white;
  display: flex;
  flex-direction: column;

  .list-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }

    .add-btn {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 8px;
      background: #4f46e5;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: #4338ca;
        transform: scale(1.05);
      }
    }
  }

  .subscription-items {
    flex: 1;
    overflow-y: auto;
  }

  .subscription-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    gap: 0.75rem;

    &:hover {
      background: #f9fafb;
    }

    &.active {
      background: #eff6ff;
      border-left: 4px solid #4f46e5;
    }

    .subscription-avatar {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .subscription-info {
      flex: 1;
      min-width: 0;

      .subscription-name {
        margin: 0 0 0.25rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .subscription-desc {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .subscription-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .last-update {
          font-size: 0.75rem;
          color: #9ca3af;
        }

        .unread-count {
          background: #ef4444;
          color: white;
          font-size: 0.75rem;
          padding: 0.125rem 0.5rem;
          border-radius: 10px;
          min-width: 1.25rem;
          text-align: center;
        }
      }
    }
  }
}

// 右侧图片画廊
.image-gallery {
  background: white;
  display: flex;
  flex-direction: column;

  .gallery-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
    }

    .header-info {
      h3 {
        margin: 0 0 0.25rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
      }

      .image-count {
        margin: 0;
        font-size: 0.875rem;
        color: #6b7280;
      }
    }

    .view-controls {
      display: flex;
      gap: 0.5rem;

      .view-btn {
        width: 40px;
        height: 40px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        background: white;
        color: #6b7280;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          border-color: #4f46e5;
          color: #4f46e5;
        }

        &.active {
          border-color: #4f46e5;
          background: #4f46e5;
          color: white;
        }
      }
    }
  }

  .gallery-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
  }

  .image-grid {
    display: grid;
    gap: 1rem;

    &.grid {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    &.masonry {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      grid-auto-rows: 10px;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 0.75rem;
    }
  }

  .image-item {
    cursor: pointer;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      .image-overlay {
        opacity: 1;
      }
    }

    .image-wrapper {
      position: relative;
      width: 100%;
      height: 200px;
      overflow: hidden;

      .masonry & {
        height: auto;
        aspect-ratio: auto;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.3s ease;
        opacity: 0;

        &.loaded {
          opacity: 1;
        }

        .masonry & {
          height: auto;
        }
      }

      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
        opacity: 0;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 1rem;

        .image-info {
          .image-title {
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            line-height: 1.3;
          }

          .image-photographer {
            margin: 0;
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.8);
          }
        }

        .image-actions {
          display: flex;
          gap: 0.5rem;
          align-self: flex-end;

          .action-btn {
            padding: 0.375rem 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            backdrop-filter: blur(10px);

            &:hover {
              background: rgba(255, 255, 255, 0.2);
            }
          }
        }
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  padding: 2rem;

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 1rem;
  }
}

// 图片模态框
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;

  .modal-content {
    background: white;
    border-radius: 16px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    position: relative;

    @media (max-width: 768px) {
      flex-direction: column;
      max-width: 95vw;
      max-height: 95vh;
    }

    .close-btn {
      position: absolute;
      top: 1rem;
      right: 1rem;
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.5);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1001;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.7);
      }
    }

    .modal-image {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }

    .modal-info {
      width: 300px;
      padding: 2rem;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      @media (max-width: 768px) {
        width: 100%;
        padding: 1.5rem;
      }

      .modal-title {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
      }

      .modal-description {
        margin: 0;
        font-size: 1rem;
        color: #4b5563;
        line-height: 1.6;
      }

      .modal-meta {
        .meta-item {
          display: flex;
          margin-bottom: 0.5rem;

          .meta-label {
            font-weight: 500;
            color: #6b7280;
            min-width: 80px;
          }

          .meta-value {
            color: #1f2937;
          }
        }
      }

      .modal-actions {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        .action-btn {
          padding: 0.75rem 1rem;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          background: white;
          color: #6b7280;
          font-size: 0.875rem;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;

          &:hover {
            border-color: #4f46e5;
            color: #4f46e5;
          }

          &.primary {
            background: #4f46e5;
            border-color: #4f46e5;
            color: white;

            &:hover {
              background: #4338ca;
            }
          }

          &.like-btn:hover {
            border-color: #ef4444;
            color: #ef4444;
          }

          &.like-btn.primary:hover {
            background: #dc2626;
          }
        }
      }
    }
  }
}

// 模态框过渡动画
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: scale(0.9);
}

// 图标
.icon-plus::before { content: "+"; }
.icon-grid::before { content: "⊞"; }
.icon-masonry::before { content: "⊟"; }
.icon-heart::before { content: "❤️"; }
.icon-download::before { content: "⬇️"; }
.icon-share::before { content: "🔗"; }
.icon-close::before { content: "✕"; }
</style>
