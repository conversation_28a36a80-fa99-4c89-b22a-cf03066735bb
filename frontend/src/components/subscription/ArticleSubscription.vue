<template>
  <div class="article-subscription">
    <!-- 三列布局容器 -->
    <div class="subscription-layout">
      <!-- 左侧：订阅列表 (18%) -->
      <div class="subscription-sidebar">
        <div class="sidebar-header">
          <h3>文章订阅</h3>
          <div class="header-actions">
            <button class="refresh-btn" @click="refreshSubscriptions" :disabled="loading">
              <i class="icon-refresh" :class="{ spinning: loading }"></i>
            </button>
          </div>
        </div>

        <div class="subscription-list">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载中...</p>
          </div>
          
          <div v-else-if="subscriptions.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无订阅源</p>
          </div>
          
          <div
            v-for="(subscription, index) in subscriptions"
            :key="index"
            :class="['subscription-item', { active: selectedSubscription === subscription }]"
            @click="selectSubscription(subscription)"
          >
            <div class="subscription-info">
              <div class="subscription-icon">
                <i class="icon-article"></i>
              </div>
              <div class="subscription-details">
                <h4 class="subscription-name">{{ subscription }}</h4>
              </div>
            </div>
            <div class="subscription-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：文章列表 (32%) -->
      <div class="article-list">
        <div class="list-header">
          <h3>{{ selectedSubscription ? selectedSubscription : '全部文章' }}</h3>
        </div>

        <div class="article-items">
          <div v-if="loadingArticles" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载文章中...</p>
          </div>
          
          <div v-else-if="filteredArticles.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无文章</p>
          </div>
          
          <div
            v-for="article in filteredArticles"
            :key="article.id"
            :class="['article-item', { active: selectedArticle?.id === article.id }]"
            @click="selectArticle(article)"
          >
            <div class="article-content">
              <h4 class="article-title">{{ article.title }}</h4>
              <p class="article-description">{{ article.description || '暂无描述' }}</p>
              <div class="article-meta">
                <span class="article-author">{{ article.author || '未知作者' }}</span>
                <span class="article-date">{{ formatDate(article.pubDate) }}</span>
              </div>
            </div>
            <div class="article-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="page-btn"
          >
            <i class="icon-chevron-left"></i>
          </button>
          <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="page-btn"
          >
            <i class="icon-chevron-right"></i>
          </button>
        </div>
      </div>

      <!-- 右侧：文章详情 (50%) -->
      <div class="article-detail">
        <div v-if="!selectedArticle" class="detail-placeholder">
          <i class="icon-article-large"></i>
          <h3>选择文章查看详情</h3>
          <p>点击左侧文章列表中的任意文章查看详细内容</p>
        </div>
        
        <div v-else class="detail-content">
          <div class="detail-header">
            <h2 class="detail-title">{{ selectedArticle.title }}</h2>
            <div class="detail-meta">
              <span class="detail-author">作者：{{ selectedArticle.author || '未知作者' }}</span>
              <span class="detail-date">发布：{{ formatDate(selectedArticle.pubDate) }}</span>
              <span class="detail-source">来源：{{ selectedArticle.taskName }}</span>
            </div>
          </div>
          
          <div class="detail-body">
            <div v-if="loadingDetail" class="loading-state">
              <div class="loading-spinner"></div>
              <p>加载详情中...</p>
            </div>
            <div v-else-if="articleContent" class="article-content-html" v-html="articleContent"></div>
            <div v-else class="no-content">
              <p>暂无详细内容</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import useArticleSubscription from './ArticleSubscription.js'

export default {
  name: 'ArticleSubscription',
  emits: ['stats-updated'],
  setup(props, { emit }) {
    return useArticleSubscription(emit)
  }
}
</script>

<style lang="scss" scoped>
@import './ArticleSubscription.scss';
</style>
