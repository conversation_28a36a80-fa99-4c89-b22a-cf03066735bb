<template>
  <div class="article-subscription">
    <!-- 三列布局 -->
    <div class="subscription-layout">
      <!-- 左侧：订阅列表 -->
      <div class="subscription-list">
        <div class="list-header">
          <h3>文章订阅</h3>
          <button class="add-btn" title="添加订阅">
            <i class="icon-plus"></i>
          </button>
        </div>
        
        <div class="subscription-items">
          <div
            v-for="subscription in subscriptions"
            :key="subscription.id"
            :class="['subscription-item', { active: selectedSubscription?.id === subscription.id }]"
            @click="handleSubscriptionSelect(subscription)"
          >
            <div class="subscription-avatar">
              <img :src="subscription.avatar" :alt="subscription.name" />
            </div>
            <div class="subscription-info">
              <h4 class="subscription-name">{{ subscription.name }}</h4>
              <p class="subscription-desc">{{ subscription.description }}</p>
              <div class="subscription-meta">
                <span class="last-update">{{ formatTime(subscription.lastUpdate) }}</span>
                <span v-if="subscription.unreadCount" class="unread-count">
                  {{ subscription.unreadCount }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：文章简介列表 -->
      <div class="article-preview">
        <div class="preview-header">
          <h3 v-if="selectedSubscription">{{ selectedSubscription.name }}</h3>
          <h3 v-else>选择订阅查看文章</h3>
        </div>

        <div v-if="selectedSubscription" class="article-list">
          <div
            v-for="article in currentArticles"
            :key="article.id"
            :class="['article-item', { active: selectedContent?.id === article.id }]"
            @click="handleArticleSelect(article)"
          >
            <div class="article-header">
              <h4 class="article-title">{{ article.title }}</h4>
              <div class="article-meta">
                <span class="author">{{ article.author }}</span>
                <span class="publish-time">{{ formatTime(article.publishTime) }}</span>
                <span class="read-time">{{ article.readTime }}</span>
              </div>
            </div>
            
            <p class="article-summary">{{ article.summary }}</p>
            
            <div class="article-tags">
              <span v-for="tag in article.tags" :key="tag" class="tag">
                {{ tag }}
              </span>
            </div>
            
            <div class="article-stats">
              <span class="likes">
                <i class="icon-heart"></i>
                {{ article.likes }}
              </span>
              <span class="comments">
                <i class="icon-comment"></i>
                {{ article.comments }}
              </span>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <div class="empty-icon">📄</div>
          <p>请从左侧选择一个订阅来查看文章</p>
        </div>
      </div>

      <!-- 右侧：文章详细内容 -->
      <div class="article-detail">
        <div v-if="selectedContent" class="detail-content">
          <div class="detail-header">
            <h1 class="detail-title">{{ selectedContent.title }}</h1>
            <div class="detail-meta">
              <div class="author-info">
                <span class="author">作者：{{ selectedContent.author }}</span>
                <span class="publish-time">{{ formatTime(selectedContent.publishTime) }}</span>
              </div>
              <div class="article-actions">
                <button class="action-btn like-btn">
                  <i class="icon-heart"></i>
                  {{ selectedContent.likes }}
                </button>
                <button class="action-btn share-btn">
                  <i class="icon-share"></i>
                  分享
                </button>
                <button class="action-btn bookmark-btn">
                  <i class="icon-bookmark"></i>
                  收藏
                </button>
              </div>
            </div>
          </div>

          <div class="detail-body">
            <div class="article-content" v-html="selectedContent.content"></div>
          </div>

          <div class="detail-footer">
            <div class="article-tags">
              <span v-for="tag in selectedContent.tags" :key="tag" class="tag">
                {{ tag }}
              </span>
            </div>
            
            <div class="article-stats">
              <span class="stat-item">
                <i class="icon-eye"></i>
                阅读时间：{{ selectedContent.readTime }}
              </span>
              <span class="stat-item">
                <i class="icon-heart"></i>
                {{ selectedContent.likes }} 点赞
              </span>
              <span class="stat-item">
                <i class="icon-comment"></i>
                {{ selectedContent.comments }} 评论
              </span>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <div class="empty-icon">📖</div>
          <p>选择一篇文章来查看详细内容</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'

export default {
  name: 'ArticleSubscription',
  setup() {
    const subscriptionStore = useSubscriptionStore()

    // 计算属性
    const subscriptions = computed(() => subscriptionStore.currentSubscriptions)
    const selectedSubscription = computed(() => subscriptionStore.selectedSubscription)
    const selectedContent = computed(() => subscriptionStore.selectedContent)
    
    const currentArticles = computed(() => {
      if (!selectedSubscription.value) return []
      const content = subscriptionStore.getContentBySubscription(selectedSubscription.value.id)
      return content.articles || []
    })

    // 方法
    const handleSubscriptionSelect = (subscription) => {
      subscriptionStore.selectSubscription(subscription)
    }

    const handleArticleSelect = (article) => {
      subscriptionStore.selectContent(article)
    }

    const formatTime = (timeString) => {
      const date = new Date(timeString)
      const now = new Date()
      const diff = now - date
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      return date.toLocaleDateString()
    }

    return {
      subscriptions,
      selectedSubscription,
      selectedContent,
      currentArticles,
      handleSubscriptionSelect,
      handleArticleSelect,
      formatTime
    }
  }
}
</script>

<style lang="scss" scoped>
.article-subscription {
  height: 100%;
}

.subscription-layout {
  display: grid;
  grid-template-columns: 260px 320px 1fr;
  height: calc(100vh - 180px);
  gap: 1px;
  background: #e5e7eb;

  @media (max-width: 1200px) {
    grid-template-columns: 240px 300px 1fr;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
    height: auto;
  }
}

// 左侧订阅列表
.subscription-list {
  background: white;
  display: flex;
  flex-direction: column;

  .list-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }

    .add-btn {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 8px;
      background: #4f46e5;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: #4338ca;
        transform: scale(1.05);
      }
    }
  }

  .subscription-items {
    flex: 1;
    overflow-y: auto;
  }

  .subscription-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    gap: 0.75rem;

    &:hover {
      background: #f9fafb;
    }

    &.active {
      background: #eff6ff;
      border-left: 4px solid #4f46e5;
    }

    .subscription-avatar {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .subscription-info {
      flex: 1;
      min-width: 0;

      .subscription-name {
        margin: 0 0 0.25rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .subscription-desc {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .subscription-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .last-update {
          font-size: 0.75rem;
          color: #9ca3af;
        }

        .unread-count {
          background: #ef4444;
          color: white;
          font-size: 0.75rem;
          padding: 0.125rem 0.5rem;
          border-radius: 10px;
          min-width: 1.25rem;
          text-align: center;
        }
      }
    }
  }
}

// 中间文章预览
.article-preview {
  background: white;
  display: flex;
  flex-direction: column;

  .preview-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .article-list {
    flex: 1;
    overflow-y: auto;
  }

  .article-item {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #f9fafb;
    }

    &.active {
      background: #eff6ff;
      border-left: 4px solid #4f46e5;
    }

    .article-header {
      margin-bottom: 0.75rem;

      .article-title {
        margin: 0 0 0.5rem 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .article-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.875rem;
        color: #6b7280;

        @media (max-width: 1200px) {
          flex-wrap: wrap;
          gap: 0.5rem;
        }
      }
    }

    .article-summary {
      margin: 0 0 1rem 0;
      font-size: 0.875rem;
      color: #4b5563;
      line-height: 1.5;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .article-tags {
      margin-bottom: 1rem;
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;

      .tag {
        background: #f3f4f6;
        color: #6b7280;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
      }
    }

    .article-stats {
      display: flex;
      gap: 1rem;
      font-size: 0.875rem;
      color: #6b7280;

      .likes, .comments {
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }
    }
  }
}

// 右侧文章详情
.article-detail {
  background: white;
  display: flex;
  flex-direction: column;

  .detail-content {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;

    .detail-header {
      margin-bottom: 2rem;
      padding-bottom: 1.5rem;
      border-bottom: 1px solid #e5e7eb;

      .detail-title {
        margin: 0 0 1rem 0;
        font-size: 1.875rem;
        font-weight: 700;
        color: #1f2937;
        line-height: 1.3;
      }

      .detail-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;

        @media (max-width: 768px) {
          flex-direction: column;
          align-items: stretch;
        }

        .author-info {
          display: flex;
          gap: 1rem;
          font-size: 0.875rem;
          color: #6b7280;

          @media (max-width: 768px) {
            justify-content: center;
          }
        }

        .article-actions {
          display: flex;
          gap: 0.5rem;

          .action-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            color: #6b7280;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.25rem;

            &:hover {
              border-color: #4f46e5;
              color: #4f46e5;
            }

            &.like-btn:hover {
              border-color: #ef4444;
              color: #ef4444;
            }
          }
        }
      }
    }

    .detail-body {
      margin-bottom: 2rem;

      .article-content {
        font-size: 1rem;
        line-height: 1.7;
        color: #374151;

        h2, h3, h4 {
          margin: 1.5rem 0 1rem 0;
          color: #1f2937;
        }

        h2 {
          font-size: 1.5rem;
          font-weight: 600;
        }

        h3 {
          font-size: 1.25rem;
          font-weight: 600;
        }

        p {
          margin: 1rem 0;
        }

        ul, ol {
          margin: 1rem 0;
          padding-left: 1.5rem;

          li {
            margin: 0.5rem 0;
          }
        }

        strong {
          font-weight: 600;
          color: #1f2937;
        }
      }
    }

    .detail-footer {
      padding-top: 1.5rem;
      border-top: 1px solid #e5e7eb;

      .article-tags {
        margin-bottom: 1rem;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .tag {
          background: #eff6ff;
          color: #1d4ed8;
          font-size: 0.875rem;
          padding: 0.375rem 0.75rem;
          border-radius: 8px;
          font-weight: 500;
        }
      }

      .article-stats {
        display: flex;
        gap: 1.5rem;
        font-size: 0.875rem;
        color: #6b7280;

        @media (max-width: 768px) {
          flex-wrap: wrap;
          gap: 1rem;
        }

        .stat-item {
          display: flex;
          align-items: center;
          gap: 0.375rem;
        }
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  padding: 2rem;

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 1rem;
  }
}

// 图标
.icon-plus::before { content: "+"; }
.icon-heart::before { content: "❤️"; }
.icon-comment::before { content: "💬"; }
.icon-share::before { content: "🔗"; }
.icon-bookmark::before { content: "🔖"; }
.icon-eye::before { content: "👁️"; }
</style>
