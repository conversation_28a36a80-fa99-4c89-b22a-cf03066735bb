<template>
  <div class="article-subscription">
    <!-- 三列布局容器 -->
    <div class="subscription-layout">
      <!-- 左侧：订阅列表 (18%) -->
      <div class="subscription-sidebar">
        <div class="sidebar-header">
          <h3>文章订阅</h3>
          <div class="header-actions">
            <button class="refresh-btn" @click="refreshSubscriptions" :disabled="loading">
              <i class="icon-refresh" :class="{ spinning: loading }"></i>
            </button>
          </div>
        </div>

        <div class="subscription-list">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载中...</p>
          </div>
          
          <div v-else-if="subscriptions.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无订阅源</p>
          </div>
          
          <div
            v-for="subscription in subscriptions"
            :key="subscription"
            :class="['subscription-item', { active: selectedSubscription === subscription }]"
            @click="selectSubscription(subscription)"
          >
            <div class="subscription-info">
              <div class="subscription-icon">
                <i class="icon-article"></i>
              </div>
              <div class="subscription-details">
                <h4 class="subscription-name">{{ subscription }}</h4>
                <p class="subscription-count">{{ getSubscriptionCount(subscription) }} 篇文章</p>
              </div>
            </div>
            <div class="subscription-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：文章列表 (32%) -->
      <div class="article-list">
        <div class="list-header">
          <h3>{{ selectedSubscription || '全部文章' }}</h3>
          <div class="header-actions">
            <div class="search-box">
              <input
                v-model="searchKeyword"
                type="text"
                placeholder="搜索文章..."
                @input="handleSearch"
                class="search-input"
              >
              <i class="icon-search"></i>
            </div>
          </div>
        </div>

        <div class="article-items">
          <div v-if="loadingArticles" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载文章中...</p>
          </div>
          
          <div v-else-if="filteredArticles.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无文章</p>
          </div>
          
          <div
            v-for="article in filteredArticles"
            :key="article.id"
            :class="['article-item', { active: selectedArticle?.id === article.id }]"
            @click="selectArticle(article)"
          >
            <div class="article-content">
              <h4 class="article-title">{{ article.title }}</h4>
              <p class="article-description">{{ article.description || '暂无描述' }}</p>
              <div class="article-meta">
                <span class="article-author">{{ article.author || '未知作者' }}</span>
                <span class="article-date">{{ formatDate(article.pubDate) }}</span>
              </div>
            </div>
            <div class="article-indicator">
              <i class="icon-chevron-right"></i>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="page-btn"
          >
            <i class="icon-chevron-left"></i>
          </button>
          <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="page-btn"
          >
            <i class="icon-chevron-right"></i>
          </button>
        </div>
      </div>

      <!-- 右侧：文章详情 (50%) -->
      <div class="article-detail">
        <div v-if="!selectedArticle" class="detail-placeholder">
          <i class="icon-article-large"></i>
          <h3>选择文章查看详情</h3>
          <p>点击左侧文章列表中的任意文章查看详细内容</p>
        </div>
        
        <div v-else class="detail-content">
          <div class="detail-header">
            <h2 class="detail-title">{{ selectedArticle.title }}</h2>
            <div class="detail-meta">
              <span class="detail-author">作者：{{ selectedArticle.author || '未知作者' }}</span>
              <span class="detail-date">发布：{{ formatDate(selectedArticle.pubDate) }}</span>
              <span class="detail-source">来源：{{ selectedArticle.taskName }}</span>
            </div>
            <div class="detail-actions">
              <button class="action-btn primary" @click="openOriginal" title="查看原文">
                <i class="icon-external"></i>
                原文链接
              </button>
              <button class="action-btn" @click="shareArticle" title="分享">
                <i class="icon-share"></i>
              </button>
              <button class="action-btn" @click="favoriteArticle" title="收藏">
                <i class="icon-heart"></i>
              </button>
            </div>
          </div>
          
          <div class="detail-body">
            <div v-if="loadingDetail" class="loading-state">
              <div class="loading-spinner"></div>
              <p>加载详情中...</p>
            </div>
            <div v-else-if="articleContent" class="article-content-html" v-html="articleContent"></div>
            <div v-else class="no-content">
              <p>暂无详细内容</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ApiClient } from '@/utils/api'

export default {
  name: 'ArticleSubscription',
  emits: ['stats-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const loadingArticles = ref(false)
    const loadingDetail = ref(false)
    const subscriptions = ref([])
    const articles = ref([])
    const selectedSubscription = ref(null)
    const selectedArticle = ref(null)
    const articleContent = ref('')
    const searchKeyword = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const totalCount = ref(0)

    // 计算属性
    const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
    
    const filteredArticles = computed(() => {
      if (!searchKeyword.value) return articles.value
      
      const keyword = searchKeyword.value.toLowerCase()
      return articles.value.filter(article =>
        article.title?.toLowerCase().includes(keyword) ||
        article.description?.toLowerCase().includes(keyword) ||
        article.author?.toLowerCase().includes(keyword)
      )
    })

    // 方法
    const refreshSubscriptions = async () => {
      loading.value = true
      try {
        const response = await ApiClient.get('http://localhost:8001/api/crawler/content/list?type=article')
        if (response.code === 200) {
          subscriptions.value = response.data.subscriptions || []
          if (subscriptions.value.length > 0 && !selectedSubscription.value) {
            selectedSubscription.value = subscriptions.value[0]
          }
          
          // 发送统计更新事件
          emit('stats-updated', {
            articleCount: response.data.total || 0,
            articleSubscriptionCount: subscriptions.value.length
          })
        }
      } catch (error) {
        console.error('获取订阅列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    const selectSubscription = async (subscription) => {
      if (selectedSubscription.value === subscription) return
      
      selectedSubscription.value = subscription
      selectedArticle.value = null
      articleContent.value = ''
      currentPage.value = 1
      await loadArticles()
    }

    const loadArticles = async () => {
      if (!selectedSubscription.value) return
      
      loadingArticles.value = true
      try {
        const response = await ApiClient.get(
          `http://localhost:8001/api/crawler/content/by-subscription?type=article&taskName=${encodeURIComponent(selectedSubscription.value)}&page=${currentPage.value}&size=${pageSize.value}`
        )
        if (response.code === 200) {
          articles.value = response.data.contents || []
          totalCount.value = response.data.total || 0
        }
      } catch (error) {
        console.error('获取文章列表失败:', error)
      } finally {
        loadingArticles.value = false
      }
    }

    const selectArticle = async (article) => {
      if (selectedArticle.value?.id === article.id) return
      
      selectedArticle.value = article
      await loadArticleDetail(article.id)
    }

    const loadArticleDetail = async (articleId) => {
      loadingDetail.value = true
      try {
        const response = await ApiClient.get(`http://localhost:8001/api/crawler/content/${articleId}`)
        if (response.code === 200) {
          articleContent.value = response.data.content || ''
        }
      } catch (error) {
        console.error('获取文章详情失败:', error)
      } finally {
        loadingDetail.value = false
      }
    }

    const getSubscriptionCount = (subscription) => {
      // 这里可以实现真实的统计逻辑
      return Math.floor(Math.random() * 100) + 10
    }

    const handleSearch = () => {
      // 搜索逻辑已在计算属性中处理
    }

    const changePage = (page) => {
      if (page < 1 || page > totalPages.value) return
      currentPage.value = page
      loadArticles()
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const openOriginal = () => {
      if (selectedArticle.value?.url) {
        window.open(selectedArticle.value.url, '_blank')
      }
    }

    const shareArticle = () => {
      console.log('分享文章:', selectedArticle.value)
    }

    const favoriteArticle = () => {
      console.log('收藏文章:', selectedArticle.value)
    }

    // 监听选中订阅的变化
    watch(selectedSubscription, () => {
      if (selectedSubscription.value) {
        loadArticles()
      }
    })

    // 组件挂载
    onMounted(() => {
      refreshSubscriptions()
    })

    return {
      loading,
      loadingArticles,
      loadingDetail,
      subscriptions,
      articles,
      selectedSubscription,
      selectedArticle,
      articleContent,
      searchKeyword,
      currentPage,
      totalPages,
      filteredArticles,
      refreshSubscriptions,
      selectSubscription,
      selectArticle,
      getSubscriptionCount,
      handleSearch,
      changePage,
      formatDate,
      openOriginal,
      shareArticle,
      favoriteArticle
    }
  }
}
</script>

<style lang="scss" scoped>
.article-subscription {
  height: 100vh;
  background: #f8fafc;
}

.subscription-layout {
  display: flex;
  height: 100%;
  gap: 1px;
  background: #e5e7eb;
}

// 左侧订阅列表 (18%)
.subscription-sidebar {
  width: 18%;
  background: white;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .header-actions {
      .refresh-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .subscription-list {
    flex: 1;
    overflow-y: auto;

    .subscription-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      .subscription-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;

        .subscription-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          background: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #6b7280;
        }

        .subscription-details {
          flex: 1;
          min-width: 0;

          .subscription-name {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 500;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .subscription-count {
            margin: 0;
            font-size: 0.75rem;
            color: #6b7280;
          }
        }
      }

      .subscription-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
      }
    }
  }
}

// 中间文章列表 (32%)
.article-list {
  width: 32%;
  background: white;
  display: flex;
  flex-direction: column;

  .list-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .search-box {
      position: relative;

      .search-input {
        width: 100%;
        padding: 0.5rem 2.5rem 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;

        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }

      .icon-search {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
      }
    }
  }

  .article-items {
    flex: 1;
    overflow-y: auto;

    .article-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: flex-start;
      gap: 1rem;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      .article-content {
        flex: 1;
        min-width: 0;

        .article-title {
          margin: 0 0 0.5rem 0;
          font-size: 0.875rem;
          font-weight: 500;
          color: #1f2937;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .article-description {
          margin: 0 0 0.5rem 0;
          font-size: 0.75rem;
          color: #6b7280;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .article-meta {
          display: flex;
          gap: 0.5rem;
          font-size: 0.75rem;
          color: #9ca3af;

          span {
            white-space: nowrap;
          }
        }
      }

      .article-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
        margin-top: 0.25rem;
      }
    }
  }

  .pagination {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    .page-btn {
      width: 32px;
      height: 32px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      background: white;
      color: #6b7280;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        border-color: #3b82f6;
        color: #3b82f6;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .page-info {
      font-size: 0.875rem;
      color: #6b7280;
    }
  }
}

// 右侧文章详情 (50%)
.article-detail {
  width: 50%;
  background: white;
  display: flex;
  flex-direction: column;

  .detail-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #9ca3af;

    .icon-article-large {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.25rem;
      font-weight: 500;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
    }
  }

  .detail-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .detail-header {
      padding: 1.5rem;
      border-bottom: 1px solid #e5e7eb;

      .detail-title {
        margin: 0 0 1rem 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.4;
      }

      .detail-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        color: #6b7280;
      }

      .detail-actions {
        display: flex;
        gap: 0.5rem;

        .action-btn {
          padding: 0.5rem 1rem;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          background: white;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;

          &:hover {
            border-color: #3b82f6;
            color: #3b82f6;
          }

          &.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;

            &:hover {
              background: #2563eb;
            }
          }
        }
      }
    }

    .detail-body {
      flex: 1;
      padding: 1.5rem;
      overflow-y: auto;

      .article-content-html {
        line-height: 1.6;
        color: #374151;

        :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
          margin: 1.5rem 0 1rem 0;
          font-weight: 600;
          color: #1f2937;
        }

        :deep(p) {
          margin: 1rem 0;
        }

        :deep(img) {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          margin: 1rem 0;
        }

        :deep(a) {
          color: #3b82f6;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        :deep(blockquote) {
          border-left: 4px solid #e5e7eb;
          padding-left: 1rem;
          margin: 1rem 0;
          color: #6b7280;
          font-style: italic;
        }

        :deep(code) {
          background: #f3f4f6;
          padding: 0.125rem 0.25rem;
          border-radius: 4px;
          font-family: 'Monaco', 'Consolas', monospace;
          font-size: 0.875rem;
        }

        :deep(pre) {
          background: #f3f4f6;
          padding: 1rem;
          border-radius: 8px;
          overflow-x: auto;
          margin: 1rem 0;

          code {
            background: none;
            padding: 0;
          }
        }
      }

      .no-content {
        text-align: center;
        color: #9ca3af;
        padding: 2rem;
      }
    }
  }
}

// 公共状态样式
.loading-state,
.empty-state {
  padding: 2rem;
  text-align: center;
  color: #9ca3af;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 图标
.icon-refresh::before { content: "🔄"; }
.icon-article::before { content: "📄"; }
.icon-chevron-right::before { content: "›"; }
.icon-chevron-left::before { content: "‹"; }
.icon-empty::before { content: "📄"; }
.icon-search::before { content: "🔍"; }
.icon-article-large::before { content: "📄"; }
.icon-external::before { content: "🔗"; }
.icon-share::before { content: "📤"; }
.icon-heart::before { content: "♡"; }
</style>
