import { ref, computed, onMounted, watch } from 'vue'
import { ApiClient } from '@/utils/api'
import { useToastStore } from '@/stores/toast'

export default function useArticleSubscription(emit) {
  const toast = useToastStore()

  // 响应式数据
  const loading = ref(false)
  const loadingArticles = ref(false)
  const loadingDetail = ref(false)
  const subscriptions = ref([])
  const articles = ref([])
  const selectedSubscription = ref(null)
  const selectedArticle = ref(null)
  const articleContent = ref('')
  const searchKeyword = ref('')
  const currentPage = ref(1)
  const pageSize = ref(20)
  const totalCount = ref(0)
  const articleCount = ref(0) // 新增变量，用于记录当前选中订阅的文章数量

  // 计算属性
  const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
  
  const filteredArticles = computed(() => {
    if (!searchKeyword.value) return articles.value
    
    const keyword = searchKeyword.value.toLowerCase()
    return articles.value.filter(article =>
      article.title?.toLowerCase().includes(keyword) ||
      article.description?.toLowerCase().includes(keyword) ||
      article.author?.toLowerCase().includes(keyword)
    )
  })

  // 方法
  const refreshSubscriptions = async () => {
    loading.value = true
    try {
      const response = await ApiClient.get('http://localhost:8001/api/crawler/content/list?type=article')
      console.log('API响应:', response)
      if (response.code === 200) {
        // 确保 subscriptions 是一个数组
        const subscriptionList = response.data.subscriptions || []
        console.log('原始订阅列表:', subscriptionList)
        
        // 更新 subscriptions
        subscriptions.value = subscriptionList
        console.log('更新后的订阅列表:', subscriptions.value)
        
        // 如果有订阅且当前没有选中的订阅，选择第一个
        if (subscriptions.value.length > 0) {
          console.log('订阅列表长度:', subscriptions.value.length)
          console.log('当前选中的订阅:', selectedSubscription.value)
          
          if (!selectedSubscription.value) {
            // 直接设置选中的订阅
            const firstSubscription = subscriptions.value[0]
            console.log('准备选中第一个订阅:', firstSubscription)
            selectedSubscription.value = firstSubscription
            console.log('设置后的选中订阅:', selectedSubscription.value)
          } else {
            console.log('保持当前选中的订阅:', selectedSubscription.value)
          }
        } else {
          console.log('订阅列表为空')
        }
        
        // 发送统计更新事件
        emit('stats-updated', {
          articleCount: response.data.total || 0,
          articleSubscriptionCount: subscriptions.value.length
        })
      } else {
        console.error('API返回错误码:', response.code)
      }
    } catch (error) {
      console.error('获取订阅列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 监听 subscriptions 的变化
  watch(subscriptions, (newSubscriptions) => {
    console.log('订阅列表已更新:', newSubscriptions)
    if (newSubscriptions.length > 0 && !selectedSubscription.value) {
      const firstSubscription = newSubscriptions[0]
      console.log('在 watch 中准备选中第一个订阅:', firstSubscription)
      selectedSubscription.value = firstSubscription
      console.log('在 watch 中设置后的选中订阅:', selectedSubscription.value)
    }
  })

  const selectSubscription = async (subscription) => {
    console.log('选择订阅被调用，参数:', subscription)
    console.log('当前选中的订阅:', selectedSubscription.value)
    
    // if (selectedSubscription.value === subscription) {
    //   console.log('相同的订阅被选中，不进行操作')
    //   return
    // }
    
    console.log('设置新的选中订阅:', subscription)
    selectedSubscription.value = subscription
    console.log('设置后的选中订阅:', selectedSubscription.value)
    
    selectedArticle.value = null
    articleContent.value = ''
    currentPage.value = 1
    await loadArticles()
  }

  const loadArticles = async () => {
    console.log('loadArticles 被调用')
    console.log('当前选中的订阅:', selectedSubscription.value)
    
    if (!selectedSubscription.value) {
      console.log('没有选中的订阅，不加载文章')
      return
    }
    
    loadingArticles.value = true
    try {
      const taskName = encodeURIComponent(selectedSubscription.value)
      console.log('编码后的 taskName:', taskName)
      
      const url = `http://localhost:8001/api/crawler/content/by-subscription?type=article&taskName=${taskName}&page=${currentPage.value}&size=${pageSize.value}`
      console.log('请求 URL:', url)
      
      const response = await ApiClient.get(url)
      console.log('文章列表 API 响应:', response)
      
      if (response.code === 200) {
        articles.value = response.data.contents || []
        totalCount.value = response.data.total || 0
        articleCount.value = response.data.total || 0 // 更新文章数量
        console.log('加载的文章数量:', articles.value.length)
      } else {
        console.error('API 返回错误码:', response.code)
      }
    } catch (error) {
      console.error('获取文章列表失败:', error)
    } finally {
      loadingArticles.value = false
    }
  }

  const selectArticle = async (article) => {
    console.log("selectArticle 被调用，文章:", article)
    if (selectedArticle.value?.id === article.id) {
      console.log("相同的文章被选中，不进行操作")
      return
    }
    
    selectedArticle.value = article
    console.log("准备加载文章详情")
    try {
      await loadArticleDetail(article.id)
      console.log("文章详情加载完成")
    } catch (error) {
      console.error("加载文章详情失败:", error)
      toast.error("加载文章详情失败，请稍后重试")
    }
  }

  const loadArticleDetail = async (articleId) => {
    console.log("loadArticleDetail 被调用，文章ID:", articleId)
    loadingDetail.value = true
    try {
      const response = await ApiClient.get(`http://localhost:8001/api/crawler/content/${articleId}`)
      console.log("API响应:", response)
      if (response.code === 200) {
        articleContent.value = response.data.content || ''
        console.log("文章内容已更新")
      } else {
        console.error("API返回错误码:", response.code)
        throw new Error(`API返回错误码: ${response.code}`)
      }
    } catch (error) {
      console.error('获取文章详情失败:', error)
      articleContent.value = ''
      toast.error('加载文章详情失败，请稍后重试')
    } finally {
      loadingDetail.value = false
      console.log("文章详情加载状态更新完成")
    }
  }

  const getSubscriptionCount = (subscription) => {
    // 这里可以实现真实的统计逻辑
    return Math.floor(Math.random() * 100) + 10
  }

  const handleSearch = () => {
    // 搜索逻辑已在计算属性中处理
  }

  const changePage = (page) => {
    if (page < 1 || page > totalPages.value) return
    currentPage.value = page
    loadArticles()
  }

  const formatDate = (dateString) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const openOriginal = () => {
    if (selectedArticle.value?.url) {
      window.open(selectedArticle.value.url, '_blank')
    }
  }

  const shareArticle = () => {
    console.log('分享文章:', selectedArticle.value)
  }

  const favoriteArticle = () => {
    console.log('收藏文章:', selectedArticle.value)
  }

  // 监听选中订阅的变化
  watch(selectedSubscription, (newValue, oldValue) => {
    console.log('selectedSubscription 变化:', oldValue, '->', newValue)
    if (newValue) {
      console.log('选中了新的订阅，加载文章')
      loadArticles()
    } else {
      console.log('没有选中的订阅')
    }
  })

  // 组件挂载
  onMounted(() => {
    console.log('组件挂载')
    console.log('初始 selectedSubscription:', selectedSubscription.value)
    refreshSubscriptions()
  })

  return {
    loading,
    loadingArticles,
    loadingDetail,
    subscriptions,
    articles,
    selectedSubscription,
    selectedArticle,
    articleContent,
    searchKeyword,
    currentPage,
    totalPages,
    articleCount,
    filteredArticles,
    refreshSubscriptions,
    selectSubscription,
    selectArticle,
    getSubscriptionCount,
    handleSearch,
    changePage,
    formatDate,
    openOriginal,
    shareArticle,
    favoriteArticle
  }
}
