.video-subscription {
  height: 100vh;
  background: #f8fafc;
}

.subscription-layout {
  display: flex;
  height: 100%;
  gap: 1px;
  background: #e5e7eb;
}

// 左侧订阅列表 (25%)
.subscription-sidebar {
  width: 25%;
  background: white;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
    }

    .header-actions {
      .refresh-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .subscription-list {
    flex: 1;
    overflow-y: auto;

    .subscription-item {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #eff6ff;
        border-right: 3px solid #3b82f6;
      }

      .subscription-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;

        .subscription-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          background: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #6b7280;
        }

        .subscription-details {
          flex: 1;
          min-width: 0;

          .subscription-name {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 500;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .subscription-count {
            margin: 0;
            font-size: 0.75rem;
            color: #6b7280;
          }
        }
      }

      .subscription-indicator {
        color: #9ca3af;
        font-size: 0.75rem;
      }
    }
  }
}

// 右侧视频内容区域 (75%)
.video-content {
  width: 75%;
  background: white;
  display: flex;
  flex-direction: column;

  .content-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;

    .header-left {
      flex: 1;

      h3 {
        margin: 0 0 1rem 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        
        .video-count {
          font-weight: 400;
          font-size: 0.875rem;
          color: #6b7280;
          margin-left: 0.5rem;
        }
      }

      .search-box {
        position: relative;
        max-width: 300px;

        .search-input {
          width: 100%;
          padding: 0.5rem 2.5rem 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 0.875rem;

          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }

        .icon-search {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          color: #9ca3af;
        }
      }
    }

    .header-right {
      .view-toggle {
        display: flex;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        overflow: hidden;

        .toggle-btn {
          padding: 0.5rem 0.75rem;
          border: none;
          background: white;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #f9fafb;
          }

          &.active {
            background: #3b82f6;
            color: white;
          }

          &:not(:last-child) {
            border-right: 1px solid #d1d5db;
          }
        }
      }
    }
  }

  .video-container {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
  }
}

// 视频网格视图
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;

  .video-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .video-thumbnail {
      position: relative;
      width: 100%;
      height: 160px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .play-overlay {
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .play-btn {
          width: 60px;
          height: 60px;
          border: none;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.9);
          color: #1f2937;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          transition: all 0.2s ease;

          &:hover {
            background: white;
            transform: scale(1.1);
          }
        }
      }

      .video-duration {
        position: absolute;
        bottom: 0.5rem;
        right: 0.5rem;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
      }

      &:hover {
        .play-overlay {
          opacity: 1;
        }

        img {
          transform: scale(1.05);
        }
      }
    }

    .video-info {
      padding: 1rem;

      .video-title {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        font-weight: 500;
        color: #1f2937;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .video-author {
        margin: 0 0 0.5rem 0;
        font-size: 0.75rem;
        color: #6b7280;
      }

      .video-meta {
        display: flex;
        gap: 0.5rem;
        font-size: 0.75rem;
        color: #9ca3af;

        span {
          white-space: nowrap;
        }
      }
    }
  }
}

// 视频列表视图
.video-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .video-item {
    display: flex;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .video-thumbnail {
      position: relative;
      width: 200px;
      height: 120px;
      flex-shrink: 0;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .play-overlay {
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .play-btn {
          width: 48px;
          height: 48px;
          border: none;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.9);
          color: #1f2937;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.25rem;

          &:hover {
            background: white;
            transform: scale(1.1);
          }
        }
      }

      .video-duration {
        position: absolute;
        bottom: 0.5rem;
        right: 0.5rem;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
      }

      &:hover {
        .play-overlay {
          opacity: 1;
        }

        img {
          transform: scale(1.05);
        }
      }
    }

    .video-content {
      flex: 1;
      padding: 1rem;
      display: flex;
      flex-direction: column;

      .video-title {
        margin: 0 0 0.5rem 0;
        font-size: 1rem;
        font-weight: 500;
        color: #1f2937;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .video-description {
        margin: 0 0 0.75rem 0;
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        flex: 1;
      }

      .video-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.75rem;
        color: #9ca3af;
        margin-top: auto;

        span {
          white-space: nowrap;
        }
      }
    }

    .video-actions {
      padding: 1rem;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      align-items: center;

      .action-btn {
        width: 32px;
        height: 32px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
        }
      }
    }
  }
}

// 分页
.pagination {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;

  .page-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      border-color: #3b82f6;
      color: #3b82f6;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .page-info {
    font-size: 0.875rem;
    color: #6b7280;
  }
}

// 视频播放模态框
.video-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;

  .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .modal-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        flex: 1;
        margin-right: 1rem;
      }

      .close-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }
      }
    }

    .modal-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;

      .video-player {
        position: relative;
        background: #000;

        video {
          width: 100%;
          height: auto;
          max-height: 60vh;
        }

        .video-loading, .video-error {
          position: absolute;
          inset: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: white;

          .loading-spinner {
            width: 48px;
            height: 48px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
          }

          p {
            margin: 0;
            font-size: 0.875rem;
          }
        }
        
        .video-error {
          background: rgba(220, 38, 38, 0.8);
        }
      }

      .video-details {
        padding: 1.5rem;

        .video-meta {
          display: flex;
          gap: 1rem;
          margin-bottom: 1rem;
          font-size: 0.875rem;
          color: #6b7280;
        }

        .video-description {
          p {
            margin: 0;
            line-height: 1.6;
            color: #374151;
          }
        }
      }
    }
  }
}

// 公共状态样式
.loading-state,
.empty-state {
  padding: 2rem;
  text-align: center;
  color: #9ca3af;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 图标
.icon-refresh::before { content: "🔄"; }
.icon-video::before { content: "🎥"; }
.icon-chevron-right::before { content: "›"; }
.icon-chevron-left::before { content: "‹"; }
.icon-empty::before { content: "🎥"; }
.icon-search::before { content: "🔍"; }
.icon-grid::before { content: "⊞"; }
.icon-list::before { content: "☰"; }
.icon-play::before { content: "▶"; }
.icon-share::before { content: "📤"; }
.icon-heart::before { content: "♡"; }
.icon-close::before { content: "✕"; }