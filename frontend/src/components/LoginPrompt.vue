<template>
  <div class="login-prompt">
    <div class="login-prompt-content">
      <div class="login-prompt-icon">
        <i class="fas fa-lock"></i>
      </div>
      <h3>需要登录</h3>
      <p>{{ message || '请登录后继续使用此功能' }}</p>
      <div class="login-prompt-actions">
        <button @click="goToLogin" class="btn btn-primary">立即登录</button>
        <button @click="goToRegister" class="btn btn-outline">注册账号</button>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'LoginPrompt',
  props: {
    message: {
      type: String,
      default: ''
    }
  },
  setup() {
    const router = useRouter()
    
    const goToLogin = () => {
      router.push('/login')
    }
    
    const goToRegister = () => {
      router.push('/login?tab=register')
    }
    
    return {
      goToLogin,
      goToRegister
    }
  }
}
</script>

<style scoped>
.login-prompt {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
}

.login-prompt-content {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.login-prompt-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.login-prompt-icon i {
  font-size: 36px;
  color: white;
}

.login-prompt-content h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
}

.login-prompt-content p {
  color: #666;
  font-size: 16px;
  margin-bottom: 30px;
  line-height: 1.5;
}

.login-prompt-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-outline {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

@media (max-width: 480px) {
  .login-prompt-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}
</style>