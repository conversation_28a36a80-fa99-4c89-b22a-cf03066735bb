<template>
  <div class="followers-following">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-users"></i>
        社交关系
      </h3>
    </div>
    
    <!-- 标签切换 -->
    <div class="tab-nav">
      <button 
        class="tab-btn"
        :class="{ active: activeTab === 'following' }"
        @click="setActiveTab('following')"
      >
        <i class="fas fa-user-plus"></i>
        关注 ({{ followingList.length }})
      </button>
      <button 
        class="tab-btn"
        :class="{ active: activeTab === 'followers' }"
        @click="setActiveTab('followers')"
      >
        <i class="fas fa-heart"></i>
        粉丝 ({{ followersList.length }})
      </button>
    </div>
    
    <!-- 搜索框 -->
    <div class="search-section">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input 
          type="text" 
          :placeholder="activeTab === 'following' ? '搜索关注的人...' : '搜索粉丝...'"
          v-model="searchQuery"
          @input="filterUsers"
        >
      </div>
    </div>
    
    <!-- 用户列表 -->
    <div class="users-list">
      <div v-for="user in filteredUsers" :key="user.id" class="user-item">
        <div class="user-info" @click="viewUserProfile(user)">
          <div class="user-avatar">
            <img v-if="user.avatar" :src="user.avatar" :alt="user.name">
            <div v-else class="avatar-placeholder">
              <i class="fas fa-user"></i>
            </div>
            <div v-if="user.isOnline" class="online-indicator"></div>
          </div>
          
          <div class="user-details">
            <div class="user-name">{{ user.name }}</div>
            <div class="user-title">{{ user.title }}</div>
            <div class="user-stats">
              <span class="stat">
                <i class="fas fa-file-alt"></i>
                {{ user.articlesCount }}篇文章
              </span>
              <span class="stat">
                <i class="fas fa-users"></i>
                {{ user.followersCount }}粉丝
              </span>
            </div>
            <div v-if="user.mutualFollows && user.mutualFollows.length > 0" class="mutual-follows">
              <i class="fas fa-user-friends"></i>
              与{{ user.mutualFollows.slice(0, 2).join('、') }}{{ user.mutualFollows.length > 2 ? '等' : '' }}共同关注
            </div>
          </div>
        </div>
        
        <div class="user-actions">
          <button 
            v-if="activeTab === 'following'"
            class="btn btn-outline"
            @click="unfollowUser(user)"
          >
            <i class="fas fa-user-minus"></i>
            取消关注
          </button>
          <button 
            v-else-if="!user.isFollowing"
            class="btn btn-primary"
            @click="followUser(user)"
          >
            <i class="fas fa-user-plus"></i>
            关注
          </button>
          <button 
            v-else
            class="btn btn-outline"
            @click="unfollowUser(user)"
          >
            <i class="fas fa-user-check"></i>
            已关注
          </button>
          
          <button class="btn btn-ghost" @click="sendMessage(user)">
            <i class="fas fa-comment"></i>
            私信
          </button>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="filteredUsers.length === 0" class="empty-state">
      <div class="empty-icon">
        <i :class="activeTab === 'following' ? 'fas fa-user-plus' : 'fas fa-heart'"></i>
      </div>
      <h4>{{ getEmptyTitle() }}</h4>
      <p>{{ getEmptyDescription() }}</p>
      <button v-if="activeTab === 'following'" class="btn btn-primary" @click="discoverUsers">
        <i class="fas fa-compass"></i>
        发现更多用户
      </button>
    </div>
    
    <!-- 推荐关注 -->
    <div v-if="activeTab === 'followers' && recommendedUsers.length > 0" class="recommended-section">
      <h4 class="recommended-title">
        <i class="fas fa-star"></i>
        推荐关注
      </h4>
      <div class="recommended-users">
        <div v-for="user in recommendedUsers" :key="user.id" class="recommended-user">
          <div class="user-avatar">
            <img v-if="user.avatar" :src="user.avatar" :alt="user.name">
            <div v-else class="avatar-placeholder">
              <i class="fas fa-user"></i>
            </div>
          </div>
          <div class="user-name">{{ user.name }}</div>
          <div class="user-title">{{ user.title }}</div>
          <button class="btn btn-primary btn-sm" @click="followUser(user)">
            <i class="fas fa-plus"></i>
            关注
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'

export default {
  name: 'FollowersFollowing',
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    
    const activeTab = ref('following')
    const searchQuery = ref('')
    
    const followingList = ref([
      {
        id: 1,
        name: '张小明',
        title: '前端开发工程师',
        avatar: null,
        isOnline: true,
        articlesCount: 24,
        followersCount: 156,
        mutualFollows: ['李四', '王五'],
        followTime: '2024-01-10T09:15:00Z'
      },
      {
        id: 2,
        name: '李小红',
        title: 'UI/UX设计师',
        avatar: null,
        isOnline: false,
        articlesCount: 18,
        followersCount: 89,
        mutualFollows: ['张三'],
        followTime: '2024-01-08T14:20:00Z'
      }
    ])
    
    const followersList = ref([
      {
        id: 3,
        name: '王小强',
        title: '产品经理',
        avatar: null,
        isOnline: true,
        articlesCount: 12,
        followersCount: 67,
        isFollowing: false,
        followTime: '2024-01-12T16:30:00Z'
      },
      {
        id: 4,
        name: '赵小美',
        title: '数据分析师',
        avatar: null,
        isOnline: false,
        articlesCount: 31,
        followersCount: 234,
        isFollowing: true,
        followTime: '2024-01-11T11:45:00Z'
      }
    ])
    
    const recommendedUsers = ref([
      {
        id: 5,
        name: '刘小华',
        title: '全栈开发工程师',
        avatar: null,
        articlesCount: 45,
        followersCount: 312
      },
      {
        id: 6,
        name: '陈小丽',
        title: '技术写作者',
        avatar: null,
        articlesCount: 67,
        followersCount: 189
      }
    ])
    
    const currentList = computed(() => {
      return activeTab.value === 'following' ? followingList.value : followersList.value
    })
    
    const filteredUsers = computed(() => {
      if (!searchQuery.value.trim()) {
        return currentList.value
      }
      
      const query = searchQuery.value.toLowerCase()
      return currentList.value.filter(user => 
        user.name.toLowerCase().includes(query) ||
        user.title.toLowerCase().includes(query)
      )
    })
    
    const setActiveTab = (tab) => {
      activeTab.value = tab
      searchQuery.value = ''
    }
    
    const filterUsers = () => {
      // 触发计算属性重新计算
    }
    
    const viewUserProfile = (user) => {
      router.push(`/profile/${user.id}`)
    }
    
    const followUser = (user) => {
      if (activeTab.value === 'followers') {
        user.isFollowing = true
      } else {
        // 添加到关注列表
        followingList.value.unshift({
          ...user,
          followTime: new Date().toISOString()
        })
      }
      toastStore.success(`已关注 ${user.name}`)
    }
    
    const unfollowUser = (user) => {
      if (activeTab.value === 'following') {
        const index = followingList.value.findIndex(u => u.id === user.id)
        if (index > -1) {
          followingList.value.splice(index, 1)
        }
      } else {
        user.isFollowing = false
      }
      toastStore.success(`已取消关注 ${user.name}`)
    }
    
    const sendMessage = (user) => {
      toastStore.info('私信功能开发中...')
    }
    
    const discoverUsers = () => {
      router.push('/discover/users')
    }
    
    const getEmptyTitle = () => {
      return activeTab.value === 'following' ? '还没有关注任何人' : '还没有粉丝'
    }
    
    const getEmptyDescription = () => {
      return activeTab.value === 'following' 
        ? '关注感兴趣的用户，获取他们的最新动态'
        : '分享优质内容，吸引更多粉丝关注'
    }
    
    return {
      activeTab,
      searchQuery,
      followingList,
      followersList,
      recommendedUsers,
      filteredUsers,
      setActiveTab,
      filterUsers,
      viewUserProfile,
      followUser,
      unfollowUser,
      sendMessage,
      discoverUsers,
      getEmptyTitle,
      getEmptyDescription
    }
  }
}
</script>

<style scoped>
.followers-following {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.section-title i {
  color: #ef4444;
  font-size: 18px;
}

.tab-nav {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
  background: #f8fafc;
  border-radius: 10px;
  padding: 4px;
}

.tab-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  background: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.tab-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.15);
}

.search-section {
  margin-bottom: 20px;
}

.search-box {
  position: relative;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-box input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.users-list {
  margin-bottom: 20px;
}

.user-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid #f3f4f6;
  border-radius: 12px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.user-item:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  cursor: pointer;
}

.user-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img,
.avatar-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 20px;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.user-title {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 8px;
}

.user-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 6px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.mutual-follows {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.user-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.user-actions .btn {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-ghost {
  background: none;
  border: 1px solid transparent;
  color: #6b7280;
}

.btn-ghost:hover {
  background: #f9fafb;
  color: #374151;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.3;
}

.empty-state h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #374151;
}

.empty-state p {
  font-size: 14px;
  margin: 0 0 20px 0;
}

.recommended-section {
  border-top: 1px solid #f3f4f6;
  padding-top: 20px;
}

.recommended-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
}

.recommended-title i {
  color: #f59e0b;
}

.recommended-users {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.recommended-user {
  text-align: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.recommended-user:hover {
  background: #f1f5f9;
  transform: translateY(-2px);
}

.recommended-user .user-avatar {
  width: 40px;
  height: 40px;
  margin: 0 auto 8px;
}

.recommended-user .user-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.recommended-user .user-title {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 10px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

@media (max-width: 768px) {
  .followers-following {
    padding: 20px;
  }

  .user-item {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .user-info {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .user-details {
    text-align: center;
  }

  .user-stats {
    justify-content: center;
  }

  .user-actions {
    justify-content: center;
  }

  .recommended-users {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
  }
}
</style>
