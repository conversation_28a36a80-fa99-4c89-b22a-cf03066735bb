<template>
  <div class="layout">
    <Header />
    <main class="main-content">
      <slot />
    </main>
    <Footer />
    <BackToTop />
    <Toast />
  </div>
</template>

<script>
import Header from './Header.vue'
import Footer from './Footer.vue'
import BackToTop from './BackToTop.vue'
import Toast from './Toast.vue'

export default {
  name: 'Layout',
  components: {
    Header,
    Footer,
    BackToTop,
    Toast
  }
}
</script>

<style scoped>
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}
</style>