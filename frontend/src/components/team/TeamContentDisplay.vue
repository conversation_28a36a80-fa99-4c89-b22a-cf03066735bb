<template>
  <div class="team-content-display">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-folder-open"></i>
        团队内容
      </h3>
      <div class="view-controls">
        <div class="view-toggle">
          <button
            class="view-btn"
            :class="{ active: viewMode === 'grid' }"
            @click="setViewMode('grid')"
          >
            <i class="fas fa-th"></i>
          </button>
          <button
            class="view-btn"
            :class="{ active: viewMode === 'list' }"
            @click="setViewMode('list')"
          >
            <i class="fas fa-list"></i>
          </button>
        </div>
        <button v-if="canManage" class="btn btn-outline btn-sm" @click="showRecycleBin = !showRecycleBin">
          <i class="fas fa-trash"></i>
          回收站 ({{ recycleBinContent.length }})
        </button>
      </div>
    </div>

    <!-- 标签导航 -->
    <div class="tab-nav">
      <button
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-btn"
        :class="{ active: activeTab === tab.key }"
        @click="setActiveTab(tab.key)"
      >
        <i :class="tab.icon"></i>
        {{ tab.label }} ({{ getTabCount(tab.key) }})
      </button>
    </div>

    <!-- 资源类型过滤 -->
    <div class="filter-section">
      <div class="resource-filters">
        <button
          v-for="type in resourceTypes"
          :key="type.key"
          class="filter-btn"
          :class="{ active: activeResourceType === type.key }"
          @click="setResourceType(type.key)"
        >
          <i :class="type.icon"></i>
          {{ type.label }}
        </button>
      </div>

      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="text"
          placeholder="搜索团队内容..."
          v-model="searchQuery"
          @input="filterContent"
        >
      </div>
    </div>
    
    <!-- 回收站 -->
    <div v-if="showRecycleBin" class="recycle-bin-section">
      <div class="recycle-header">
        <h4>
          <i class="fas fa-trash"></i>
          回收站
        </h4>
        <button class="btn btn-outline btn-sm" @click="showRecycleBin = false">
          <i class="fas fa-times"></i>
          关闭
        </button>
      </div>
      
      <div class="recycle-content">
        <div v-if="recycleBinContent.length === 0" class="empty-recycle">
          <i class="fas fa-trash"></i>
          <p>回收站为空</p>
        </div>
        <div v-else class="recycle-list">
          <div v-for="item in recycleBinContent" :key="item.id" class="recycle-item">
            <div class="item-info">
              <div class="item-type">
                <i :class="getResourceIcon(item.resourceType)"></i>
                <span>{{ getResourceLabel(item.resourceType) }}</span>
              </div>
              <h5 class="item-title">{{ item.title }}</h5>
              <p class="item-meta">
                删除者：{{ item.deletedBy }} | 删除时间：{{ formatDate(item.deletedAt) }}
              </p>
            </div>
            <div class="item-actions">
              <button class="btn btn-primary btn-sm" @click="restoreContent(item)">
                <i class="fas fa-undo"></i>
                恢复
              </button>
              <button class="btn btn-danger btn-sm" @click="permanentDelete(item)">
                <i class="fas fa-trash"></i>
                永久删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 内容列表 -->
    <div class="content-area">
      <div class="content-grid" :class="{ 'content-list': viewMode === 'list' }">
        <div
          v-for="item in paginatedContent"
          :key="item.id"
          class="content-item"
          :class="item.resourceType"
        >
          <div class="item-header">
            <div class="item-type">
              <i :class="getResourceIcon(item.resourceType)"></i>
              <span>{{ getResourceLabel(item.resourceType) }}</span>
            </div>
            <div class="item-actions">
              <button class="action-btn" @click="shareItem(item)" title="分享">
                <i class="fas fa-share"></i>
              </button>
              <button class="action-btn" @click="bookmarkItem(item)" title="收藏">
                <i class="fas fa-bookmark"></i>
              </button>
              <button v-if="canManage" class="action-btn danger" @click="deleteContent(item)" title="删除">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <div class="item-content">
            <h4 class="item-title">{{ item.title }}</h4>
            <p class="item-description">{{ item.description }}</p>

            <div class="item-meta">
              <div class="meta-left">
                <span class="author">
                  {{ getAuthorLabel() }}：
                  <span
                    class="author-name"
                    @click="navigateToUserProfile(item.authorId || item.recommender?.userId)"
                    style="cursor: pointer; color: #007bff;"
                  >
                    {{ item.author || item.recommendedBy }}
                  </span>
                </span>
                <span class="date">
                  {{ getDateLabel() }}：{{ formatDate(item.date || item.recommendedAt) }}
                </span>
              </div>

              <div class="meta-right">
                <div class="item-stats">
                  <span class="stat">
                    <i class="fas fa-eye"></i>
                    {{ item.views }}
                  </span>
                  <span class="stat">
                    <i class="fas fa-heart"></i>
                    {{ item.likes }}
                  </span>
                  <span class="stat">
                    <i class="fas fa-bookmark"></i>
                    {{ item.bookmarks }}
                  </span>
                </div>
              </div>
            </div>

            <div class="item-tags">
              <span v-for="tag in item.tags" :key="tag" class="tag">{{ tag }}</span>
            </div>

            <div v-if="item.recommendationNote" class="recommendation-note">
              <i class="fas fa-quote-left"></i>
              <span>{{ item.recommendationNote }}</span>
            </div>
          </div>

          <div class="item-footer">
            <button class="btn btn-primary" @click="viewItem(item)">查看详情</button>
            <button v-if="canManage && activeTab === 'published'" class="btn btn-outline" @click="editItem(item)">编辑</button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredContent.length === 0 && !showRecycleBin" class="empty-state">
        <div class="empty-icon">
          <i :class="getEmptyIcon()"></i>
        </div>
        <h4>{{ getEmptyTitle() }}</h4>
        <p>{{ getEmptyDescription() }}</p>
      </div>

      <!-- 分页 -->
      <div v-if="filteredContent.length > 0 && !showRecycleBin" class="pagination-container">
        <div class="pagination-info">
          显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredContent.length) }} 条，
          共 {{ filteredContent.length }} 条记录
        </div>
        <div class="pagination">
          <button
            class="pagination-btn"
            :disabled="currentPage === 1"
            @click="goToPage(currentPage - 1)"
          >
            <i class="fas fa-chevron-left"></i>
            上一页
          </button>

          <div class="pagination-pages">
            <button
              v-for="page in visiblePages"
              :key="page"
              class="pagination-page"
              :class="{ active: page === currentPage }"
              @click="goToPage(page)"
            >
              {{ page }}
            </button>
          </div>

          <button
            class="pagination-btn"
            :disabled="currentPage === totalPages"
            @click="goToPage(currentPage + 1)"
          >
            下一页
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import { useUserStore } from '../../stores/user'

export default {
  name: 'TeamContentDisplay',
  props: {
    content: {
      type: Array,
      default: () => []
    },
    canManage: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: Object,
      default: () => ({
        currentPage: 1,
        pageSize: 10,
        total: 0
      })
    }
  },
  emits: ['delete-content', 'restore-content', 'page-change'],
  setup(props, { emit }) {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    const activeTab = ref('all')
    const activeResourceType = ref('all')
    const viewMode = ref('grid')
    const searchQuery = ref('')
    const showRecycleBin = ref(false)

    // 分页相关状态
    const currentPage = ref(1)
    const pageSize = ref(6) // 每页显示6个项目

    const tabs = [
      { key: 'all', label: '全部内容', icon: 'fas fa-th-large' },
      { key: 'recommended', label: '团队推荐', icon: 'fas fa-star' },
      { key: 'popular', label: '热门内容', icon: 'fas fa-fire' },
      { key: 'recent', label: '最新发布', icon: 'fas fa-clock' }
    ]

    const resourceTypes = [
      { key: 'all', label: '全部', icon: 'fas fa-th-large' },
      { key: 'article', label: '文章', icon: 'fas fa-file-alt' },
      { key: 'prompt', label: 'Prompt', icon: 'fas fa-code' },
      { key: 'tool', label: '工具', icon: 'fas fa-wrench' },
      { key: 'course', label: '课程', icon: 'fas fa-graduation-cap' }
    ]
    
    // 回收站数据
    const recycleBinContent = ref([])

    const filteredContent = computed(() => {
      let content = props.content || []

      // 按标签过滤
      if (activeTab.value !== 'all') {
        switch (activeTab.value) {
          case 'recommended':
            content = content.filter(item => item.category === 'recommended')
            break
          case 'popular':
            content = content.filter(item => item.isPopular)
            break
          case 'recent':
            content = content.sort((a, b) => new Date(b.date || b.recommendedAt) - new Date(a.date || a.recommendedAt))
            break
        }
      }

      // 按资源类型过滤
      if (activeResourceType.value !== 'all') {
        content = content.filter(item => item.resourceType === activeResourceType.value)
      }

      // 按搜索关键词过滤
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase()
        content = content.filter(item =>
          (item.title || '').toLowerCase().includes(query) ||
          (item.description || '').toLowerCase().includes(query) ||
          (item.tags || []).some(tag => tag.toLowerCase().includes(query))
        )
      }

      return content
    })

    // 分页相关计算属性
    const totalPages = computed(() => {
      return Math.ceil(filteredContent.value.length / pageSize.value)
    })

    const paginatedContent = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredContent.value.slice(start, end)
    })

    const visiblePages = computed(() => {
      const total = totalPages.value
      const current = currentPage.value
      const delta = 2
      const range = []
      const rangeWithDots = []

      for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
        range.push(i)
      }

      if (current - delta > 2) {
        rangeWithDots.push(1, '...')
      } else {
        rangeWithDots.push(1)
      }

      rangeWithDots.push(...range)

      if (current + delta < total - 1) {
        rangeWithDots.push('...', total)
      } else if (total > 1) {
        rangeWithDots.push(total)
      }

      return rangeWithDots.filter((item, index, arr) => arr.indexOf(item) === index)
    })

    const getTabCount = (tabKey) => {
      const content = props.content || []
      switch (tabKey) {
        case 'all':
          return content.length
        case 'recommended':
          return content.filter(item => item.category === 'recommended').length
        case 'popular':
          return content.filter(item => item.isPopular).length
        case 'recent':
          return content.length
        default:
          return 0
      }
    }
    
    const setActiveTab = (tab) => {
      activeTab.value = tab
      currentPage.value = 1 // 重置到第一页
    }

    const setResourceType = (type) => {
      activeResourceType.value = type
      currentPage.value = 1 // 重置到第一页
    }

    const setViewMode = (mode) => {
      viewMode.value = mode
    }

    const filterContent = () => {
      currentPage.value = 1 // 搜索时重置到第一页
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value && page !== '...') {
        currentPage.value = page
      }
    }

    // 导航相关方法
    const navigateToUserProfile = (userId) => {
      if (!userId) {
        toastStore.error('用户ID无效')
        return
      }

      // 检查是否是当前用户，如果是则跳转到个人资料页面
      const currentUser = userStore.currentUser
      const currentUserId = currentUser?.id || currentUser?.userId

      if (userId === currentUserId) {
        router.push('/profile')
      } else {
        router.push(`/user/${userId}`)
      }
    }

    const getAuthorLabel = () => {
      return activeTab.value === 'recommended' ? '推荐者' : '作者'
    }

    const getDateLabel = () => {
      return activeTab.value === 'recommended' ? '推荐时间' : '发布时间'
    }

    const getEmptyIcon = () => {
      switch (activeTab.value) {
        case 'recommended':
          return 'fas fa-star'
        case 'popular':
          return 'fas fa-fire'
        case 'recent':
          return 'fas fa-clock'
        default:
          return 'fas fa-folder-open'
      }
    }

    const getEmptyTitle = () => {
      switch (activeTab.value) {
        case 'recommended':
          return '还没有推荐内容'
        case 'popular':
          return '还没有热门内容'
        case 'recent':
          return '还没有最新内容'
        default:
          return '还没有团队内容'
      }
    }

    const getEmptyDescription = () => {
      switch (activeTab.value) {
        case 'recommended':
          return '团队成员推荐的优质内容将在这里显示'
        case 'popular':
          return '受欢迎的热门内容将在这里显示'
        case 'recent':
          return '最新发布的内容将在这里显示'
        default:
          return '团队成员分享的内容将在这里显示'
      }
    }
    
    const getResourceIcon = (type) => {
      const iconMap = {
        article: 'fas fa-file-alt',
        prompt: 'fas fa-code',
        tool: 'fas fa-wrench',
        course: 'fas fa-graduation-cap'
      }
      return iconMap[type] || 'fas fa-file'
    }
    
    const getResourceLabel = (type) => {
      const labelMap = {
        article: '文章',
        prompt: 'Prompt',
        tool: '工具',
        course: '课程'
      }
      return labelMap[type] || '未知'
    }
    
    const viewItem = (item) => {
      // 根据资源类型跳转到对应详情页
      const routeMap = {
        article: '/article',
        prompt: '/prompt',
        tool: '/tool',
        course: '/course'
      }
      const route = routeMap[item.resourceType] || '/article'
      router.push(`${route}/${item.id}`)
      toastStore.info(`正在跳转到${getResourceLabel(item.resourceType)}详情页...`)
    }

    const editItem = (item) => {
      const routeMap = {
        article: '/article',
        prompt: '/prompt',
        tool: '/tool',
        course: '/course'
      }
      const route = routeMap[item.resourceType] || '/article'
      router.push(`${route}/${item.id}/edit`)
      toastStore.info(`正在跳转到编辑页面...`)
    }

    const shareItem = (item) => {
      const url = `${window.location.origin}/${item.resourceType}/${item.id}`
      navigator.clipboard.writeText(url).then(() => {
        toastStore.success('链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('复制失败，请手动复制链接')
      })
    }

    const bookmarkItem = (item) => {
      // 模拟收藏操作
      item.bookmarks += 1
      toastStore.success('已收藏到个人空间')
    }

    const deleteContent = (item) => {
      // 移动到回收站
      recycleBinContent.value.push({
        ...item,
        deletedBy: '当前用户',
        deletedAt: new Date().toISOString()
      })

      // 触发父组件删除事件
      emit('delete-content', item)
      toastStore.success(`已删除"${item.title}"`)
    }

    const restoreContent = (item) => {
      // 从回收站移除
      const index = recycleBinContent.value.findIndex(r => r.id === item.id)
      if (index > -1) {
        recycleBinContent.value.splice(index, 1)
        // 触发父组件恢复事件
        emit('restore-content', item)
        toastStore.success(`已恢复"${item.title}"`)
      }
    }

    const permanentDelete = (item) => {
      const index = recycleBinContent.value.findIndex(r => r.id === item.id)
      if (index > -1) {
        recycleBinContent.value.splice(index, 1)
        toastStore.success('内容已永久删除')
      }
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 监听props变化，用于调试
    watch(() => props.content, (newContent) => {
      console.log('TeamContentDisplay 接收到新的content数据:', newContent)
    }, { immediate: true })

    return {
      activeTab,
      activeResourceType,
      viewMode,
      searchQuery,
      showRecycleBin,
      currentPage,
      pageSize,
      tabs,
      resourceTypes,
      recycleBinContent,
      filteredContent,
      paginatedContent,
      totalPages,
      visiblePages,
      getTabCount,
      setActiveTab,
      setResourceType,
      setViewMode,
      filterContent,
      goToPage,
      navigateToUserProfile,
      getAuthorLabel,
      getDateLabel,
      getEmptyIcon,
      getEmptyTitle,
      getEmptyDescription,
      getResourceIcon,
      getResourceLabel,
      viewItem,
      editItem,
      shareItem,
      bookmarkItem,
      deleteContent,
      restoreContent,
      permanentDelete,
      formatDate
    }
  }
}
</script>

<style scoped>
.team-content-display {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 头部样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f3f4f6;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.section-title i {
  color: #8b5cf6;
  font-size: 18px;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

.view-btn {
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: white;
  color: #8b5cf6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 标签导航样式 */
.tab-nav {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  padding: 0;
  border-bottom: 1px solid #f3f4f6;
}

.tab-btn {
  background: none;
  border: none;
  padding: 12px 16px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

.tab-btn:hover {
  color: #374151;
  background: #f9fafb;
}

.tab-btn.active {
  color: #8b5cf6;
  border-bottom-color: #8b5cf6;
  background: #faf5ff;
}

.tab-btn i {
  font-size: 12px;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  gap: 20px;
}

.resource-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.filter-btn.active {
  background: #8b5cf6;
  border-color: #8b5cf6;
  color: white;
}

.search-box {
  position: relative;
  min-width: 250px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-box input:focus {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.recycle-bin-section {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
}

.recycle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.recycle-header h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
  margin: 0;
}

.empty-recycle {
  text-align: center;
  padding: 30px;
  color: #9ca3af;
}

.empty-recycle i {
  font-size: 36px;
  margin-bottom: 10px;
  opacity: 0.5;
}

.recycle-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recycle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #f3f4f6;
}

.recycle-item .item-info {
  flex: 1;
}

.recycle-item .item-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.recycle-item .item-meta {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.recycle-item .item-actions {
  display: flex;
  gap: 8px;
}

.content-area {
  min-height: 400px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.content-list {
  grid-template-columns: 1fr;
}

.content-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.content-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.item-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 6px;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #ef4444;
}

.item-content {
  margin-bottom: 15px;
}

.item-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.item-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 15px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 12px;
  color: #9ca3af;
}

.meta-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-stats {
  display: flex;
  gap: 12px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.item-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.recommendation-note {
  background: #f0f9ff;
  border-left: 4px solid #0ea5e9;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #0c4a6e;
  font-style: italic;
}

.recommendation-note i {
  margin-right: 8px;
  color: #0ea5e9;
}

.item-footer {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.3;
}

.empty-state h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #374151;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

/* 分页样式 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 30px;
  padding: 20px 0;
  border-top: 1px solid #f3f4f6;
}

.page-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #374151;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  padding: 0 8px;
}

/* 新分页样式 */
.pagination-container {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f3f4f6;
}

.pagination-info {
  text-align: center;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 15px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #374151;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
}

.pagination-page {
  width: 36px;
  height: 36px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.pagination-page:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #374151;
}

.pagination-page.active {
  background: #8b5cf6;
  border-color: #8b5cf6;
  color: white;
}

@media (max-width: 1024px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .search-box {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .team-content-display {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .view-controls {
    justify-content: space-between;
  }

  .tab-nav {
    overflow-x: auto;
    padding-bottom: 8px;
  }

  .tab-btn {
    padding: 10px 14px;
    font-size: 13px;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .item-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .item-actions {
    justify-content: flex-end;
  }

  .item-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .recycle-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .recycle-item .item-actions {
    justify-content: center;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
  }

  .pagination-btn {
    padding: 10px 16px;
  }

  .pagination-pages {
    justify-content: center;
  }

  .pagination-page {
    width: 32px;
    height: 32px;
    font-size: 13px;
  }
}

/* 作者名称点击样式 */
.author-name {
  transition: all 0.2s ease;
}

.author-name:hover {
  color: #0056b3 !important;
  text-decoration: underline;
}
</style>
