<template>
  <div :class="['loading-spinner', size]">
    <div class="spinner">
      <div class="bounce1"></div>
      <div class="bounce2"></div>
      <div class="bounce3"></div>
    </div>
    <p v-if="text" class="loading-text">{{ text }}</p>
  </div>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  props: {
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    text: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;

  &.small {
    padding: 1rem;
    
    .spinner {
      width: 40px;
      height: 40px;
    }
    
    .loading-text {
      font-size: 0.875rem;
    }
  }

  &.medium {
    .spinner {
      width: 60px;
      height: 60px;
    }
  }

  &.large {
    padding: 3rem;
    
    .spinner {
      width: 80px;
      height: 80px;
    }
    
    .loading-text {
      font-size: 1.125rem;
    }
  }
}

.spinner {
  width: 60px;
  height: 60px;
  position: relative;
  margin-bottom: 1rem;
}

.spinner > div {
  width: 18px;
  height: 18px;
  background-color: #4f46e5;
  border-radius: 100%;
  display: inline-block;
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinner .bounce1 {
  animation-delay: -0.32s;
}

.spinner .bounce2 {
  animation-delay: -0.16s;
}

@keyframes sk-bouncedelay {
  0%, 80%, 100% {
    transform: scale(0);
  } 40% {
    transform: scale(1.0);
  }
}

.loading-text {
  margin: 0;
  color: #6b7280;
  font-weight: 500;
  text-align: center;
}
</style>
