<template>
  <div :class="['error-message', type]">
    <div class="error-icon">
      <i v-if="type === 'error'" class="icon-error"></i>
      <i v-else-if="type === 'warning'" class="icon-warning"></i>
      <i v-else class="icon-info"></i>
    </div>
    <div class="error-content">
      <h4 v-if="title" class="error-title">{{ title }}</h4>
      <p class="error-text">{{ message }}</p>
      <button v-if="showRetry" class="retry-btn" @click="$emit('retry')">
        <i class="icon-refresh"></i>
        重试
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorMessage',
  props: {
    type: {
      type: String,
      default: 'error',
      validator: (value) => ['error', 'warning', 'info'].includes(value)
    },
    title: {
      type: String,
      default: ''
    },
    message: {
      type: String,
      required: true
    },
    showRetry: {
      type: Boolean,
      default: false
    }
  },
  emits: ['retry']
}
</script>

<style lang="scss" scoped>
.error-message {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid;
  background: white;

  &.error {
    border-color: #fecaca;
    background: #fef2f2;

    .error-icon {
      color: #ef4444;
    }
  }

  &.warning {
    border-color: #fed7aa;
    background: #fffbeb;

    .error-icon {
      color: #f59e0b;
    }
  }

  &.info {
    border-color: #bfdbfe;
    background: #eff6ff;

    .error-icon {
      color: #3b82f6;
    }
  }
}

.error-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.error-content {
  flex: 1;
  min-width: 0;

  .error-title {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
  }

  .error-text {
    margin: 0 0 1rem 0;
    font-size: 0.875rem;
    color: #4b5563;
    line-height: 1.5;
  }

  .retry-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: white;
    color: #374151;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:hover {
      border-color: #4f46e5;
      color: #4f46e5;
    }
  }
}

// 图标
.icon-error::before { content: "❌"; }
.icon-warning::before { content: "⚠️"; }
.icon-info::before { content: "ℹ️"; }
.icon-refresh::before { content: "🔄"; }
</style>
