<template>
  <header class="header">
    <div class="container">
      <div class="nav-left">
        <router-link to="/" class="logo">
          <i class="fas fa-brain"></i>
          <span>AI知识库</span>
        </router-link>
        <nav class="nav-menu">
          <router-link to="/" class="nav-item">Prompt市场</router-link>
          <router-link to="/recommendation" class="nav-item">
            <i class="fas fa-star"></i>
            推荐广场
          </router-link>
          <router-link to="/profile" class="nav-item">个人空间</router-link>
          <router-link to="/team-space" class="nav-item">团队空间</router-link>
          <router-link to="/tools" class="nav-item">工具箱</router-link>
        </nav>
      </div>
      <div class="nav-right">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            type="text" 
            placeholder="搜索Prompt、作者或关键词..."
            v-model="searchQuery"
            @keypress.enter="performSearch"
          >
        </div>
        <div class="user-info">
          <i class="fas fa-bell" @click="toggleNotifications"></i>
          <span v-if="notificationCount > 0" class="notification-badge">{{ notificationCount }}</span>
          <div class="user-profile" @click="toggleUserMenu" v-if="user">
            <span class="user-name">{{ user.nickname || user.username }}</span>
            <div class="user-avatar">
              <img v-if="user.avatar" :src="user.avatar" :alt="user.nickname">
              <i v-else class="fas fa-user"></i>
            </div>
          </div>
          <router-link v-else to="/login" class="btn btn-outline login-btn">登录</router-link>
          <router-link v-if="!user" to="/login?tab=register" class="btn btn-primary register-btn">注册</router-link>
        </div>
      </div>
    </div>
    
    <!-- 通知下拉菜单 -->
    <div v-if="showNotifications" class="notifications-dropdown">
      <div class="notifications-header">
        <h3>通知</h3>
        <button @click="markAllAsRead" class="mark-all-read">全部已读</button>
      </div>
      <div class="notifications-list">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.read }"
        >
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ formatTime(notification.time) }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 用户菜单 -->
    <div v-if="showUserMenu" class="user-menu-dropdown">
      <div class="user-menu-header">
        <div class="user-avatar-large">
          <img v-if="user && user.avatar" :src="user.avatar" :alt="user.nickname">
          <i v-else class="fas fa-user"></i>
        </div>
        <div class="user-info-text">
          <div class="user-name">{{ user ? user.nickname : '' }}</div>
          <div class="user-email">{{ user ? user.email : '' }}</div>
        </div>
      </div>
      <div class="user-menu-items">
        <router-link to="/profile" class="menu-item">
          <i class="fas fa-user"></i>
          个人空间
        </router-link>
        <router-link to="/recommendation" class="menu-item">
          <i class="fas fa-star"></i>
          推荐广场
        </router-link>
        <router-link to="/team-space" class="menu-item">
          <i class="fas fa-users"></i>
          团队空间
        </router-link>
        <router-link to="/tools" class="menu-item">
          <i class="fas fa-toolbox"></i>
          工具箱
        </router-link>
        <div class="menu-divider"></div>
        <div class="menu-item" @click="logout">
          <i class="fas fa-sign-out-alt"></i>
          退出登录
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { useNotificationStore } from '../stores/notification'

export default {
  name: 'Header',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const notificationStore = useNotificationStore()
    
    const searchQuery = ref('')
    const showNotifications = ref(false)
    const showUserMenu = ref(false)
    
    const user = computed(() => userStore.currentUser)
    const notifications = computed(() => notificationStore.notifications)
    const notificationCount = computed(() => notificationStore.unreadCount)
    
    const performSearch = () => {
      if (searchQuery.value.trim()) {
        router.push(`/search?q=${encodeURIComponent(searchQuery.value)}`)
      }
    }
    
    const toggleNotifications = () => {
      showNotifications.value = !showNotifications.value
      showUserMenu.value = false
    }
    
    const toggleUserMenu = () => {
      showUserMenu.value = !showUserMenu.value
      showNotifications.value = false
    }
    
    const markAllAsRead = () => {
      notificationStore.markAllAsRead()
    }
    
    const formatTime = (time) => {
      return new Date(time).toLocaleString('zh-CN')
    }
    
    const logout = () => {
      userStore.logout()
      showUserMenu.value = false
      router.push('/login')
    }
    
    const handleClickOutside = (event) => {
      if (!event.target.closest('.user-info') && !event.target.closest('.notifications-dropdown')) {
        showNotifications.value = false
        showUserMenu.value = false
      }
    }
    
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })
    
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
    
    return {
      searchQuery,
      showNotifications,
      showUserMenu,
      user,
      notifications,
      notificationCount,
      performSearch,
      toggleNotifications,
      toggleUserMenu,
      markAllAsRead,
      formatTime,
      logout
    }
  }
}
</script>

<style scoped>
.header {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 40px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 20px;
  font-weight: bold;
  color: #4f46e5;
  text-decoration: none;
}

.logo i {
  font-size: 24px;
}

.nav-menu {
  display: flex;
  gap: 30px;
}

.nav-item {
  text-decoration: none;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-item:hover,
.nav-item.router-link-active {
  color: #4f46e5;
}

.nav-item.router-link-active::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: #4f46e5;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 0 15px;
  width: 300px;
}

.search-box i {
  color: #9ca3af;
  margin-right: 10px;
}

.search-box input {
  border: none;
  background: none;
  outline: none;
  padding: 12px 0;
  width: 100%;
  font-size: 14px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
}

.user-info i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.notification-badge {
  background: #ef4444;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  position: absolute;
  top: -5px;
  right: 95px;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.user-profile:hover {
  background: #f3f4f6;
}

.user-name {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.login-btn {
  padding: 8px 16px;
  font-size: 14px;
  margin-right: 10px;
}

.register-btn {
  padding: 8px 16px;
  font-size: 14px;
}

/* 通知下拉菜单 */
.notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 80px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  width: 320px;
  z-index: 1000;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.notifications-header h3 {
  margin: 0;
  font-size: 16px;
  color: #111827;
}

.mark-all-read {
  background: none;
  border: none;
  color: #4f46e5;
  font-size: 14px;
  cursor: pointer;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background: #f9fafb;
}

.notification-item.unread {
  background: #eff6ff;
}

.notification-title {
  font-weight: 600;
  color: #111827;
  margin-bottom: 5px;
  font-size: 14px;
}

.notification-message {
  color: #6b7280;
  font-size: 13px;
  margin-bottom: 5px;
}

.notification-time {
  color: #9ca3af;
  font-size: 12px;
}

/* 用户菜单 */
.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  width: 250px;
  z-index: 1000;
}

.user-menu-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.user-avatar-large {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.user-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info-text {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
}

.user-email {
  color: #6b7280;
  font-size: 14px;
}

.user-menu-items {
  padding: 10px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #374151;
  text-decoration: none;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.menu-item:hover {
  background: #f9fafb;
}

.menu-item i {
  width: 16px;
  font-size: 16px;
}

.menu-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 8px 0;
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .search-box {
    width: 200px;
  }
  
  .user-name {
    display: none;
  }
  
  .user-profile {
    padding: 5px;
  }
  
  .notifications-dropdown,
  .user-menu-dropdown {
    right: 10px;
    width: 280px;
  }
}
</style>