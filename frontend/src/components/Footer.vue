<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>关于我们</h3>
          <ul>
            <li><a href="#">公司简介</a></li>
            <li><a href="#">团队介绍</a></li>
            <li><a href="#">联系我们</a></li>
            <li><a href="#">加入我们</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3>产品</h3>
          <ul>
            <li><a href="#">Prompt市场</a></li>
            <li><a href="#">AI工具箱</a></li>
            <li><a href="#">团队协作</a></li>
            <li><a href="#">企业版</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3>支持</h3>
          <ul>
            <li><a href="#">帮助中心</a></li>
            <li><a href="#">使用指南</a></li>
            <li><a href="#">API文档</a></li>
            <li><a href="#">反馈建议</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3>法律</h3>
          <ul>
            <li><a href="#">服务条款</a></li>
            <li><a href="#">隐私政策</a></li>
            <li><a href="#">版权声明</a></li>
            <li><a href="#">免责声明</a></li>
          </ul>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 AI知识库. 保留所有权利.</p>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'Footer'
}
</script>

<style scoped>
.footer {
  background: #111827;
  color: white;
  padding: 40px 0 20px;
  margin-top: 60px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section h3 {
  margin-bottom: 15px;
  font-size: 16px;
  color: white;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-section a:hover {
  color: white;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 20px;
  text-align: center;
  color: #9ca3af;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}
</style>