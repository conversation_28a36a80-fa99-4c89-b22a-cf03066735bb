<template>
  <Layout>
    <div class="modern-user-profile-page">
      <!-- 导航栏 -->
      <div class="profile-nav">
        <div class="container">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            <span>返回</span>
          </button>
          <div class="nav-actions">
            <button class="action-btn" @click="shareProfile">
              <i class="fas fa-share"></i>
            </button>
            <button class="action-btn" @click="reportUser">
              <i class="fas fa-flag"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 个人资料头部区域 -->
      <div class="profile-header">
        <div class="header-background">
          <div class="gradient-bg"></div>
          <div class="pattern-overlay"></div>
        </div>
        
        <div class="container">
          <div class="profile-hero">
            <!-- 个人基础信息 -->
            <div class="profile-main">
              <div class="avatar-section">
                <div class="avatar-container">
                  <img v-if="userInfo.avatarUrl" :src="userInfo.avatarUrl" :alt="userInfo.displayName" class="avatar">
                  <div v-else class="avatar-placeholder">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="online-indicator" v-if="userInfo.isOnline"></div>
                </div>
              </div>
              
              <div class="profile-info">
                <div class="name-section">
                  <h1 class="display-name">{{ userInfo.displayName || '未知用户' }}</h1>
                  <span class="username">@{{ userInfo.username || 'unknown' }}</span>
                  <div class="verification-badge" v-if="userInfo.verified">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="relationship-badge" v-if="userInfo.relationship">
                    <span class="badge" :class="userInfo.relationship">
                      {{ getRelationshipText(userInfo.relationship) }}
                    </span>
                  </div>
                </div>
                
                <p class="bio" v-if="userInfo.bio">{{ userInfo.bio }}</p>
                <p class="bio placeholder" v-else>这个人很懒，什么都没有留下...</p>
                
                <div class="profile-meta">
                  <div class="meta-item" v-if="userInfo.department">
                    <i class="fas fa-building"></i>
                    <span>{{ userInfo.department }}</span>
                  </div>
                  <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>{{ formatJoinDate(userInfo.createdAt) }}</span>
                  </div>
                  <div class="meta-item" v-if="userInfo.location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>{{ userInfo.location }}</span>
                  </div>
                  <div class="meta-item" v-if="userInfo.lastActiveAt">
                    <i class="fas fa-clock"></i>
                    <span>{{ formatLastActive(userInfo.lastActiveAt) }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 统计数据和操作 -->
            <div class="stats-section">
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ userStats.publishedCount || 0 }}</div>
                    <div class="stat-label">发布内容</div>
                  </div>
                </div>
                
                <div class="stat-card" @click="viewFollowers">
                  <div class="stat-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ formatNumber(userStats.followersCount || 0) }}</div>
                    <div class="stat-label">粉丝</div>
                  </div>
                </div>
                
                <div class="stat-card" @click="viewFollowing">
                  <div class="stat-icon">
                    <i class="fas fa-user-plus"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ formatNumber(userStats.followingCount || 0) }}</div>
                    <div class="stat-label">关注</div>
                  </div>
                </div>
                
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-eye"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ formatNumber(userStats.totalViews || 0) }}</div>
                    <div class="stat-label">总浏览</div>
                  </div>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="action-buttons">
                <button 
                  class="btn btn-primary" 
                  @click="toggleFollow"
                  :disabled="followLoading"
                >
                  <i class="fas fa-spinner fa-spin" v-if="followLoading"></i>
                  <i :class="userInfo.isFollowing ? 'fas fa-user-check' : 'fas fa-user-plus'" v-else></i>
                  {{ userInfo.isFollowing ? '已关注' : '关注' }}
                </button>
                <button class="btn btn-outline" @click="sendMessage">
                  <i class="fas fa-envelope"></i>
                  私信
                </button>
                <button class="btn btn-outline" @click="blockUser" v-if="!userInfo.isBlocked">
                  <i class="fas fa-ban"></i>
                  屏蔽
                </button>
                <button class="btn btn-outline danger" @click="unblockUser" v-else>
                  <i class="fas fa-unlock"></i>
                  取消屏蔽
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="profile-content">
        <div class="container">
          <div class="content-layout">
            <!-- 左侧边栏 -->
            <div class="sidebar">
              <!-- 用户技能 -->
              <div class="widget skills-widget" v-if="userSkills.length > 0">
                <h3 class="widget-title">
                  <i class="fas fa-tags"></i>
                  技能标签
                </h3>
                <div class="skills-container">
                  <div v-for="skill in userSkills" :key="skill.name" class="skill-tag" :class="skill.level">
                    <span class="skill-name">{{ skill.name }}</span>
                    <span class="skill-level">{{ skill.level }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 用户成就 -->
              <div class="widget achievements-widget" v-if="userAchievements.length > 0">
                <h3 class="widget-title">
                  <i class="fas fa-trophy"></i>
                  成就徽章
                </h3>
                <div class="achievements-grid">
                  <div v-for="achievement in userAchievements" :key="achievement.id" 
                       class="achievement-badge" :class="{ unlocked: achievement.unlocked }">
                    <div class="badge-icon">
                      <i :class="achievement.icon"></i>
                    </div>
                    <div class="badge-info">
                      <div class="badge-name">{{ achievement.name }}</div>
                      <div class="badge-desc">{{ achievement.description }}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 共同关注 -->
              <div class="widget mutual-widget" v-if="mutualFollows.length > 0">
                <h3 class="widget-title">
                  <i class="fas fa-users"></i>
                  共同关注
                </h3>
                <div class="mutual-list">
                  <div v-for="user in mutualFollows" :key="user.id" class="mutual-item" @click="viewUser(user.id)">
                    <img :src="user.avatar" :alt="user.name" class="mutual-avatar">
                    <span class="mutual-name">{{ user.name }}</span>
                  </div>
                  <div class="mutual-more" v-if="mutualFollows.length > 5">
                    +{{ mutualFollows.length - 5 }} 更多
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 主要内容 -->
            <div class="main-content">
              <!-- 导航标签 -->
              <div class="content-nav">
                <div class="nav-tabs">
                  <button v-for="tab in tabs" :key="tab.key" 
                          class="nav-tab" :class="{ active: activeTab === tab.key }"
                          @click="setActiveTab(tab.key)">
                    <i :class="tab.icon"></i>
                    <span>{{ tab.title }}</span>
                    <div class="tab-indicator"></div>
                  </button>
                </div>
              </div>
              
              <!-- 标签内容 -->
              <div class="tab-content">
                <!-- 公开内容 -->
                <div v-if="activeTab === 'content'" class="tab-panel">
                  <div class="content-filters">
                    <button v-for="filter in contentFilters" :key="filter.key"
                            class="filter-btn" :class="{ active: activeContentFilter === filter.key }"
                            @click="setContentFilter(filter.key)">
                      {{ filter.label }}
                    </button>
                  </div>
                  
                  <div class="content-grid" v-if="filteredContent.length > 0">
                    <div v-for="item in filteredContent" :key="item.id" class="content-card">
                      <div class="content-header">
                        <div class="content-type">
                          <i :class="getContentIcon(item.type)"></i>
                          <span>{{ getContentTypeLabel(item.type) }}</span>
                        </div>
                        <div class="content-date">{{ formatDate(item.createdAt) }}</div>
                      </div>
                      <div class="content-body">
                        <h4>{{ item.title }}</h4>
                        <p>{{ item.description }}</p>
                      </div>
                      <div class="content-stats">
                        <span class="stat">
                          <i class="fas fa-eye"></i>
                          {{ formatNumber(item.views) }}
                        </span>
                        <span class="stat">
                          <i class="fas fa-heart"></i>
                          {{ formatNumber(item.likes) }}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="empty-state" v-else>
                    <i class="fas fa-file-alt"></i>
                    <h3>暂无公开内容</h3>
                    <p>该用户还没有发布任何公开内容</p>
                  </div>
                </div>
                
                <!-- 活动历史 -->
                <div v-else-if="activeTab === 'activity'" class="tab-panel">
                  <div class="activity-list" v-if="userActivities.length > 0">
                    <div v-for="activity in userActivities" :key="activity.id" class="activity-card">
                      <div class="activity-header">
                        <div class="activity-type">
                          <i :class="activity.icon"></i>
                          <span>{{ activity.type }}</span>
                        </div>
                        <div class="activity-date">{{ formatDate(activity.date) }}</div>
                      </div>
                      <div class="activity-body">
                        <h4>{{ activity.title }}</h4>
                        <p>{{ activity.description }}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="empty-state" v-else>
                    <i class="fas fa-clock"></i>
                    <h3>暂无活动记录</h3>
                    <p>该用户的活动记录为空或不公开</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Layout from '../../../components/Layout.vue'
import { useToastStore } from '../../../stores/toast'
import { useUserStore } from '../../../stores/user'

export default {
  name: 'ModernUserProfile',
  components: {
    Layout
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // 响应式数据
    const activeTab = ref('content')
    const activeContentFilter = ref('all')
    const followLoading = ref(false)

    // 用户信息
    const userInfo = reactive({
      id: route.params.userId,
      displayName: '张三',
      username: 'zhangsan',
      bio: '资深产品设计师，专注于用户体验设计和产品创新。热爱分享设计思考和实践经验。',
      department: '产品设计部',
      location: '上海',
      avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhangsan',
      verified: true,
      isOnline: true,
      isFollowing: false,
      isBlocked: false,
      relationship: 'colleague', // colleague, friend, stranger
      createdAt: '2023-03-20T00:00:00Z',
      lastActiveAt: '2024-01-20T14:30:00Z'
    })

    // 用户统计
    const userStats = reactive({
      publishedCount: 18,
      followersCount: 856,
      followingCount: 234,
      totalViews: 28450,
      totalLikes: 1680,
      totalFavorites: 420
    })

    // 用户技能
    const userSkills = ref([
      { name: 'UI设计', level: 'expert' },
      { name: 'UX研究', level: 'expert' },
      { name: 'Figma', level: 'advanced' },
      { name: '原型设计', level: 'advanced' },
      { name: '用户调研', level: 'intermediate' }
    ])

    // 用户成就
    const userAchievements = ref([
      {
        id: 1,
        name: '设计大师',
        description: '发布了50+设计作品',
        icon: 'fas fa-palette',
        unlocked: true
      },
      {
        id: 2,
        name: '人气设计师',
        description: '获得5000+浏览量',
        icon: 'fas fa-fire',
        unlocked: true
      },
      {
        id: 3,
        name: '社区贡献者',
        description: '帮助了100+用户',
        icon: 'fas fa-hands-helping',
        unlocked: true
      }
    ])

    // 共同关注
    const mutualFollows = ref([
      {
        id: 1,
        name: '李四',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=lisi'
      },
      {
        id: 2,
        name: '王五',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wangwu'
      },
      {
        id: 3,
        name: '赵六',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhaoliu'
      }
    ])

    // 标签配置
    const tabs = ref([
      {
        key: 'content',
        title: '公开内容',
        icon: 'fas fa-file-alt'
      },
      {
        key: 'activity',
        title: '活动历史',
        icon: 'fas fa-history'
      }
    ])

    // 内容过滤器
    const contentFilters = ref([
      { key: 'all', label: '全部' },
      { key: 'article', label: '文章' },
      { key: 'design', label: '设计' },
      { key: 'tool', label: '工具' }
    ])

    // 用户内容
    const userContent = ref([
      {
        id: 1,
        type: 'article',
        title: '移动端设计规范指南',
        description: '详细介绍移动端UI设计的基本规范和最佳实践',
        createdAt: '2024-01-18T10:00:00Z',
        views: 2340,
        likes: 156,
        category: 'article'
      },
      {
        id: 2,
        type: 'design',
        title: '电商APP界面设计',
        description: '现代化电商应用的界面设计案例分享',
        createdAt: '2024-01-15T14:30:00Z',
        views: 1890,
        likes: 234,
        category: 'design'
      },
      {
        id: 3,
        type: 'tool',
        title: 'Figma插件推荐',
        description: '提高设计效率的Figma插件合集',
        createdAt: '2024-01-12T09:15:00Z',
        views: 1560,
        likes: 189,
        category: 'tool'
      }
    ])

    // 用户活动
    const userActivities = ref([
      {
        id: 1,
        type: '发布内容',
        icon: 'fas fa-file-alt',
        title: '发布了新文章',
        description: '《移动端设计规范指南》',
        date: '2024-01-18T10:00:00Z'
      },
      {
        id: 2,
        type: '社交互动',
        icon: 'fas fa-heart',
        title: '点赞了设计作品',
        description: '为《现代化仪表盘设计》点赞',
        date: '2024-01-17T15:20:00Z'
      },
      {
        id: 3,
        type: '加入团队',
        icon: 'fas fa-users',
        title: '加入了新团队',
        description: '成为产品设计团队的成员',
        date: '2024-01-15T11:30:00Z'
      }
    ])

    // 计算属性
    const filteredContent = computed(() => {
      if (activeContentFilter.value === 'all') {
        return userContent.value
      }
      return userContent.value.filter(item => item.category === activeContentFilter.value)
    })

    // 方法
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatJoinDate = (dateString) => {
      const date = new Date(dateString)
      return `${date.getFullYear()}年${date.getMonth() + 1}月加入`
    }

    const formatLastActive = (dateString) => {
      const now = new Date()
      const time = new Date(dateString)
      const diff = now - time

      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (days > 0) return `${days}天前活跃`
      if (hours > 0) return `${hours}小时前活跃`
      if (minutes > 0) return `${minutes}分钟前活跃`
      return '刚刚活跃'
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const getRelationshipText = (relationship) => {
      const texts = {
        colleague: '同事',
        friend: '朋友',
        stranger: '陌生人'
      }
      return texts[relationship] || ''
    }

    const getContentIcon = (type) => {
      const icons = {
        article: 'fas fa-file-alt',
        design: 'fas fa-palette',
        tool: 'fas fa-wrench',
        course: 'fas fa-graduation-cap'
      }
      return icons[type] || 'fas fa-file'
    }

    const getContentTypeLabel = (type) => {
      const labels = {
        article: '文章',
        design: '设计',
        tool: '工具',
        course: '课程'
      }
      return labels[type] || '内容'
    }

    // 事件处理
    const goBack = () => {
      router.go(-1)
    }

    const setActiveTab = (tabKey) => {
      activeTab.value = tabKey
    }

    const setContentFilter = (filterKey) => {
      activeContentFilter.value = filterKey
    }

    const toggleFollow = async () => {
      followLoading.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        userInfo.isFollowing = !userInfo.isFollowing

        if (userInfo.isFollowing) {
          userStats.followersCount++
          toastStore.success('关注成功')
        } else {
          userStats.followersCount--
          toastStore.success('取消关注成功')
        }
      } catch (error) {
        toastStore.error('操作失败，请重试')
      } finally {
        followLoading.value = false
      }
    }

    const sendMessage = () => {
      toastStore.info('私信功能开发中...')
    }

    const blockUser = () => {
      userInfo.isBlocked = true
      toastStore.success('已屏蔽该用户')
    }

    const unblockUser = () => {
      userInfo.isBlocked = false
      toastStore.success('已取消屏蔽')
    }

    const shareProfile = () => {
      if (navigator.share) {
        navigator.share({
          title: `${userInfo.displayName}的个人主页`,
          url: window.location.href
        })
      } else {
        navigator.clipboard.writeText(window.location.href)
        toastStore.success('链接已复制到剪贴板')
      }
    }

    const reportUser = () => {
      toastStore.info('举报功能开发中...')
    }

    const viewFollowers = () => {
      toastStore.info('粉丝列表功能开发中...')
    }

    const viewFollowing = () => {
      toastStore.info('关注列表功能开发中...')
    }

    const viewUser = (userId) => {
      router.push(`/user/${userId}`)
    }

    // 生命周期
    onMounted(() => {
      // 根据路由参数加载用户数据
      const userId = route.params.userId
      console.log('加载用户数据:', userId)
      // 这里应该调用API加载真实的用户数据
    })

    return {
      // 响应式数据
      activeTab,
      activeContentFilter,
      followLoading,
      userInfo,
      userStats,
      userSkills,
      userAchievements,
      mutualFollows,
      tabs,
      contentFilters,
      filteredContent,
      userActivities,

      // 方法
      formatNumber,
      formatJoinDate,
      formatLastActive,
      formatDate,
      getRelationshipText,
      getContentIcon,
      getContentTypeLabel,
      goBack,
      setActiveTab,
      setContentFilter,
      toggleFollow,
      sendMessage,
      blockUser,
      unblockUser,
      shareProfile,
      reportUser,
      viewFollowers,
      viewFollowing,
      viewUser
    }
  }
}
</script>

<style scoped>
/* 现代化用户个人主页样式 */
.modern-user-profile-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 导航栏 */
.profile-nav {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.profile-nav .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.nav-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* 个人资料头部区域 */
.profile-header {
  position: relative;
  padding: 40px 0;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.9) 0%,
    rgba(118, 75, 162, 0.9) 100%);
}

.pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
}

.profile-hero {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 40px;
  align-items: start;
}

/* 个人基础信息 */
.profile-main {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.avatar-section {
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
  width: 120px;
  height: 120px;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.online-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #4CAF50;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.profile-info {
  flex: 1;
  color: white;
}

.name-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.display-name {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.username {
  font-size: 18px;
  opacity: 0.8;
  font-weight: 500;
}

.verification-badge {
  color: #4CAF50;
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.relationship-badge {
  margin-left: auto;
}

.badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge.colleague {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.badge.friend {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.badge.stranger {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.bio {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 20px 0;
  opacity: 0.9;
  max-width: 500px;
}

.bio.placeholder {
  opacity: 0.6;
  font-style: italic;
}

.profile-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.8;
}

.meta-item i {
  width: 16px;
  text-align: center;
}

/* 统计数据和操作按钮 */
.stats-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  min-width: 280px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 12px 20px;
  border-radius: 12px;
  border: none;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: white;
  color: #667eea;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.btn-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline.danger {
  border-color: rgba(244, 67, 54, 0.5);
  color: #f44336;
}

.btn-outline.danger:hover:not(:disabled) {
  background: rgba(244, 67, 54, 0.1);
  border-color: #f44336;
}

/* 主要内容区域 */
.profile-content {
  background: #f8f9fa;
  min-height: 60vh;
  padding: 40px 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 40px;
}

/* 左侧边栏 */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.widget {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: #2c3e50;
}

.widget-title i {
  color: #667eea;
}

/* 技能标签 */
.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  transition: transform 0.2s ease;
}

.skill-tag:hover {
  transform: scale(1.05);
}

.skill-tag.expert {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.skill-tag.advanced {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.skill-tag.intermediate {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
}

.skill-tag.beginner {
  background: linear-gradient(135deg, #9E9E9E, #757575);
  color: white;
}

.skill-level {
  font-size: 10px;
  opacity: 0.8;
}

/* 成就徽章 */
.achievements-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.achievement-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.achievement-badge.unlocked {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-color: rgba(102, 126, 234, 0.2);
}

.achievement-badge:not(.unlocked) {
  background: #f8f9fa;
  opacity: 0.6;
}

.achievement-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.badge-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.achievement-badge.unlocked .badge-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.achievement-badge:not(.unlocked) .badge-icon {
  background: #e9ecef;
  color: #6c757d;
}

.badge-info {
  flex: 1;
}

.badge-name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  color: #2c3e50;
}

.badge-desc {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

/* 共同关注 */
.mutual-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mutual-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.mutual-item:hover {
  background: #f8f9fa;
}

.mutual-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.mutual-name {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.mutual-more {
  text-align: center;
  color: #6c757d;
  font-size: 12px;
  padding: 8px;
  font-style: italic;
}

/* 主要内容 */
.main-content {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

/* 导航标签 */
.content-nav {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 0 24px;
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.nav-tab {
  position: relative;
  padding: 20px 24px;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.nav-tab.active {
  color: #667eea;
  background: white;
  border-bottom-color: #667eea;
}

.nav-tab i {
  font-size: 16px;
}

.tab-indicator {
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 3px;
  background: #667eea;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-tab.active .tab-indicator {
  transform: scaleX(1);
}

/* 标签内容 */
.tab-content {
  padding: 32px;
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 内容过滤器 */
.content-filters {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  background: white;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.content-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.content-card:hover {
  background: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.content-type {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #667eea;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.content-date {
  color: #6c757d;
  font-size: 12px;
}

.content-body h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.content-body p {
  margin: 0 0 16px 0;
  color: #6c757d;
  line-height: 1.5;
  font-size: 14px;
}

.content-stats {
  display: flex;
  gap: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6c757d;
  font-size: 12px;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.activity-card:hover {
  background: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.activity-type {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #667eea;
  font-size: 14px;
  font-weight: 600;
}

.activity-date {
  color: #6c757d;
  font-size: 12px;
}

.activity-body h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.activity-body p {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  margin: 0 0 8px 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 280px 1fr;
    gap: 30px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    min-width: 240px;
  }
}

@media (max-width: 992px) {
  .profile-hero {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .profile-main {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 20px;
  }

  .stats-section {
    align-self: stretch;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    min-width: auto;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .sidebar {
    order: 2;
  }

  .main-content {
    order: 1;
  }
}

@media (max-width: 768px) {
  .profile-header {
    padding: 30px 0;
  }

  .profile-main {
    gap: 16px;
  }

  .avatar-container {
    width: 100px;
    height: 100px;
  }

  .display-name {
    font-size: 24px;
  }

  .username {
    font-size: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .profile-content {
    padding: 24px 0;
  }

  .tab-content {
    padding: 20px;
  }

  .nav-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .nav-tabs::-webkit-scrollbar {
    display: none;
  }

  .nav-tab {
    padding: 16px 20px;
    white-space: nowrap;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .profile-header {
    padding: 20px 0;
  }

  .avatar-container {
    width: 80px;
    height: 80px;
  }

  .display-name {
    font-size: 20px;
  }

  .profile-meta {
    flex-direction: column;
    gap: 8px;
  }

  .widget {
    padding: 16px;
  }

  .tab-content {
    padding: 16px;
  }

  .profile-nav .container {
    flex-direction: column;
    gap: 12px;
  }

  .nav-actions {
    align-self: flex-end;
  }
}
</style>
