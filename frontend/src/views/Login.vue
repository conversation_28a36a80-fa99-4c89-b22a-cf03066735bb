<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1>AI Community</h1>
        <p>欢迎回来！请登录您的账户。</p>
      </div>
      
      <div class="login-tabs">
        <button 
          :class="{ active: activeTab === 'login' }"
          @click="activeTab = 'login'"
        >
          登录
        </button>
        <button 
          :class="{ active: activeTab === 'register' }"
          @click="activeTab = 'register'"
        >
          注册
        </button>
      </div>

      <!-- 登录表单 -->
      <form v-if="activeTab === 'login'" @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="username">用户名或邮箱</label>
          <input 
            id="username"
            v-model="loginForm.username"
            type="text"
            placeholder="请输入您的用户名或邮箱"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="password">密码</label>
          <input 
            id="password"
            v-model="loginForm.password"
            type="password"
            placeholder="请输入您的密码"
            required
          />
        </div>
        
        <button type="submit" class="login-btn" :disabled="loading">
          {{ loading ? '登录中...' : '登录' }}
        </button>


      </form>

      <!-- 注册表单 -->
      <form v-if="activeTab === 'register'" @submit.prevent="handleRegister" class="login-form">
        <div class="form-group">
          <label for="reg-username">用户名</label>
          <input 
            id="reg-username"
            v-model="registerForm.username"
            type="text"
            placeholder="请选择一个用户名"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="reg-email">邮箱</label>
          <input 
            id="reg-email"
            v-model="registerForm.email"
            type="email"
            placeholder="请输入您的邮箱地址"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="reg-nickname">昵称</label>
          <input 
            id="reg-nickname"
            v-model="registerForm.nickname"
            type="text"
            placeholder="请输入您的昵称"
          />
        </div>
        
        <div class="form-group">
          <label for="reg-password">密码</label>
          <input 
            id="reg-password"
            v-model="registerForm.password"
            type="password"
            placeholder="请创建一个密码"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="confirm-password">确认密码</label>
          <input 
            id="confirm-password"
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            required
          />
        </div>
        
        <button type="submit" class="login-btn" :disabled="loading">
          {{ loading ? '注册中...' : '注册' }}
        </button>
      </form>

      <!-- 第三方登录 -->
      <div class="oauth-section">
        <div class="divider">
          <span>或使用第三方账号登录</span>
        </div>
        
        <div class="oauth-buttons">
          <button @click="handleGoogleLogin" class="oauth-btn google-btn" :disabled="googleLoading">
            <svg class="oauth-icon" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            {{ googleLoading ? '连接中...' : '使用 Google 账号登录' }}
          </button>
          
          <button @click="handleGithubLogin" class="oauth-btn github-btn" :disabled="githubLoading">
            <svg class="oauth-icon" viewBox="0 0 24 24">
              <path fill="currentColor" d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            {{ githubLoading ? '连接中...' : '使用 GitHub 账号登录' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'
import { API_CONFIG, ApiClient } from '@/utils/api'

export default {
  name: 'LoginView',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const toastStore = useToastStore()
    
    const activeTab = ref('login')
    const loading = ref(false)
    const googleLoading = ref(false)
    const githubLoading = ref(false)
    
    const loginForm = ref({
      username: '',
      password: ''
    })
    
    const registerForm = ref({
      username: '',
      email: '',
      nickname: '',
      password: '',
      confirmPassword: ''
    })
    
    const handleLogin = async () => {
      try {
        loading.value = true
        const data = await ApiClient.post(API_CONFIG.ENDPOINTS.AUTH.LOGIN, loginForm.value)
        
        if (data.code === 200) {
          userStore.setUser(data.data)
          toastStore.showToast('登录成功！', 'success')
          
          // 检查是否有保存的重定向地址
          const redirectPath = localStorage.getItem('redirectAfterLogin')
          if (redirectPath) {
            localStorage.removeItem('redirectAfterLogin')
            router.push(redirectPath)
          } else {
            router.push('/')
          }
        } else {
          toastStore.showToast(data.message, 'error')
        }
      } catch (error) {
        toastStore.showToast('登录失败，请重试', 'error')
      } finally {
        loading.value = false
      }
    }
    
    const handleRegister = async () => {
      if (registerForm.value.password !== registerForm.value.confirmPassword) {
        toastStore.showToast('两次输入的密码不一致', 'error')
        return
      }
      
      try {
        loading.value = true
        const data = await ApiClient.post(API_CONFIG.ENDPOINTS.AUTH.REGISTER, {
          username: registerForm.value.username,
          email: registerForm.value.email,
          nickname: registerForm.value.nickname,
          password: registerForm.value.password
        })
        
        if (data.code === 200) {
          userStore.setUser(data.data)
          toastStore.showToast('注册成功！', 'success')
          
          // 检查是否有保存的重定向地址
          const redirectPath = localStorage.getItem('redirectAfterLogin')
          if (redirectPath) {
            localStorage.removeItem('redirectAfterLogin')
            router.push(redirectPath)
          } else {
            router.push('/')
          }
        } else {
          toastStore.showToast(data.message, 'error')
        }
      } catch (error) {
        toastStore.showToast('注册失败，请重试', 'error')
      } finally {
        loading.value = false
      }
    }
    
    const handleGoogleLogin = async () => {
      try {
        const data = await ApiClient.get(API_CONFIG.ENDPOINTS.AUTH.OAUTH_GOOGLE)
        
        if (data.code === 200) {
          // 成功获取到授权URL，直接跳转，不显示loading
          window.location.href = data.data
        } else {
          // 只有在出错时才显示loading和错误信息
          googleLoading.value = true
          toastStore.showToast(data.message, 'error')
          googleLoading.value = false
        }
      } catch (error) {
        // 只有在出错时才显示loading和错误信息
        googleLoading.value = true
        console.error('Google OAuth 错误:', error)
        if (error.message === 'Network Error' || error.code === 'ECONNREFUSED') {
          toastStore.showToast('无法连接到服务器，请检查后端服务是否正常启动', 'error')
        } else {
          toastStore.showToast('Google 登录失败，请重试', 'error')
        }
        googleLoading.value = false
      }
    }
    
    const handleGithubLogin = async () => {
      try {
        const data = await ApiClient.get(API_CONFIG.ENDPOINTS.AUTH.OAUTH_GITHUB)
        
        if (data.code === 200) {
          // 成功获取到授权URL，直接跳转，不显示loading
          window.location.href = data.data
        } else {
          // 只有在出错时才显示loading和错误信息
          githubLoading.value = true
          toastStore.showToast(data.message, 'error')
          githubLoading.value = false
        }
      } catch (error) {
        // 只有在出错时才显示loading和错误信息
        githubLoading.value = true
        console.error('GitHub OAuth 错误:', error)
        if (error.message === 'Network Error' || error.code === 'ECONNREFUSED') {
          toastStore.showToast('无法连接到服务器，请检查后端服务是否正常启动', 'error')
        } else {
          toastStore.showToast('GitHub 登录失败，请重试', 'error')
        }
        githubLoading.value = false
      }
    }
    
    onMounted(() => {
      // 检查URL参数，如果有tab=register则切换到注册标签页
      const urlParams = new URLSearchParams(window.location.search)
      const tab = urlParams.get('tab')
      if (tab === 'register') {
        activeTab.value = 'register'
      }
      
      // 检查是否有保存的重定向地址，如果有则显示提示
      const redirectPath = localStorage.getItem('redirectAfterLogin')
      if (redirectPath) {
        toastStore.showToast('请登录后继续访问您的页面', 'info')
      }
      
      // 检查是否有OAuth回调
      const provider = urlParams.get('provider')
      const code = urlParams.get('code')
      
      if (provider && code) {
        handleOAuthCallback(provider, code)
      }
    })
    
    const handleOAuthCallback = async (provider, code) => {
      try {
        loading.value = true
        const data = await ApiClient.post(API_CONFIG.ENDPOINTS.AUTH.OAUTH_CALLBACK(provider), { code })
        
        if (data.code === 200) {
          userStore.setUser(data.data)
          toastStore.showToast('登录成功！', 'success')
          
          // 检查是否有保存的重定向地址
          const redirectPath = localStorage.getItem('redirectAfterLogin')
          if (redirectPath) {
            localStorage.removeItem('redirectAfterLogin')
            router.push(redirectPath)
          } else {
            router.push('/')
          }
        } else {
          toastStore.showToast(data.message, 'error')
        }
      } catch (error) {
        toastStore.showToast('OAuth登录失败，请重试', 'error')
      } finally {
        loading.value = false
      }
    }
    
    return {
      activeTab,
      loading,
      googleLoading,
      githubLoading,
      loginForm,
      registerForm,
      handleLogin,
      handleRegister,
      handleGoogleLogin,
      handleGithubLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-box {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #333;
  font-size: 2.5em;
  margin-bottom: 10px;
  font-weight: 700;
}

.login-header p {
  color: #666;
  font-size: 1.1em;
  margin-bottom: 0;
}

.login-tabs {
  display: flex;
  margin-bottom: 30px;
  border-bottom: 2px solid #f0f0f0;
}

.login-tabs button {
  flex: 1;
  padding: 15px;
  border: none;
  background: none;
  font-size: 1.1em;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.login-tabs button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

.login-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1em;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.login-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.oauth-section {
  margin-top: 30px;
}

.divider {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
  line-height: 1.5;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e0e0e0;
  z-index: 1;
}

.divider span {
  background: white;
  padding: 0 20px;
  color: #666;
  font-size: 0.9em;
  position: relative;
  z-index: 2;
  display: inline-block;
}

.oauth-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.oauth-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1em;
  font-weight: 600;
}

.oauth-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.oauth-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.google-btn {
  color: #4285F4;
}

.google-btn:hover {
  border-color: #4285F4;
}

.github-btn {
  color: #333;
}

.github-btn:hover {
  border-color: #333;
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-box {
    padding: 30px 20px;
  }
  
  .login-header h1 {
    font-size: 2em;
  }
}
</style>