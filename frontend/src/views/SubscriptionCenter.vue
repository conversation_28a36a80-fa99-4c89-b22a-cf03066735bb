<template>
  <div class="subscription-center">
    <!-- 顶部导航栏 -->
    <div class="subscription-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <i class="icon-rss"></i>
            订阅中心
          </h1>
          <p class="page-subtitle">智能内容聚合，一站式订阅管理</p>
        </div>
        <div class="stats-section">
          <div class="stat-item">
            <span class="stat-number">{{ stats.totalContent || 0 }}</span>
            <span class="stat-label">总内容</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.totalSubscriptions || 0 }}</span>
            <span class="stat-label">订阅源</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ activeSubscriptions }}</span>
            <span class="stat-label">活跃订阅</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 类型切换标签 -->
    <div class="type-tabs">
      <div class="tabs-wrapper">
        <button
          v-for="tab in contentTypes"
          :key="tab.type"
          :class="['tab-btn', { active: activeType === tab.type }]"
          @click="switchType(tab.type)"
        >
          <i :class="tab.icon"></i>
          <span>{{ tab.label }}</span>
          <span class="tab-count">({{ getTypeCount(tab.type) }})</span>
        </button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <Transition name="fade" mode="out-in">
        <component 
          :is="currentComponent" 
          :key="activeType"
          @stats-updated="handleStatsUpdate"
        />
      </Transition>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ApiClient } from '@/utils/api'
import ArticleSubscription from '@/components/subscription/ArticleSubscription.vue'
import VideoSubscription from '@/components/subscription/VideoSubscription.vue'
import AudioSubscription from '@/components/subscription/AudioSubscription.vue'

export default {
  name: 'SubscriptionCenter',
  components: {
    ArticleSubscription,
    VideoSubscription,
    AudioSubscription
  },
  setup() {
    // 响应式数据
    const activeType = ref('article')
    const stats = ref({})
    const loading = ref(false)

    // 内容类型配置
    const contentTypes = [
      { type: 'article', label: '文章', icon: 'icon-article' },
      { type: 'video', label: '视频', icon: 'icon-video' },
      { type: 'audio', label: '音频', icon: 'icon-audio' }
    ]

    // 计算属性
    const currentComponent = computed(() => {
      const componentMap = {
        article: 'ArticleSubscription',
        video: 'VideoSubscription',
        audio: 'AudioSubscription' // 修正为使用音频组件
      }
      return componentMap[activeType.value]
    })

    const activeSubscriptions = computed(() => {
      const typeStats = {
        article: stats.value.articleSubscriptionCount || 0,
        video: stats.value.videoSubscriptionCount || 0,
        audio: stats.value.audioSubscriptionCount || 0
      }
      return typeStats[activeType.value]
    })

    // 方法
    const switchType = (type) => {
      if (activeType.value !== type) {
        activeType.value = type
      }
    }

    const getTypeCount = (type) => {
      const countMap = {
        article: stats.value.articleCount || 0,
        video: stats.value.videoCount || 0,
        audio: stats.value.audioCount || 0
      }
      return countMap[type]
    }

    const loadStats = async () => {
      loading.value = true
      try {
        const response = await ApiClient.get('http://localhost:8001/api/crawler/content/stats')
        if (response.code === 200) {
          stats.value = response.data
        }
      } catch (error) {
        console.error('获取统计信息失败:', error)
      } finally {
        loading.value = false
      }
    }

    const handleStatsUpdate = (newStats) => {
      stats.value = { ...stats.value, ...newStats }
    }

    // 监听路由参数
    watch(() => activeType.value, (newType) => {
      // 可以在这里添加路由更新逻辑
      console.log('切换到类型:', newType)
    })

    // 组件挂载
    onMounted(() => {
      loadStats()
    })

    return {
      activeType,
      stats,
      loading,
      contentTypes,
      currentComponent,
      activeSubscriptions,
      switchType,
      getTypeCount,
      handleStatsUpdate
    }
  }
}
</script>

<style lang="scss" scoped>
.subscription-center {
  min-height: 100vh;
  background: #f8fafc;
  margin: 0;
  padding: 0;
}

.subscription-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  
  .header-content {
    max-width: 100%;
    margin: 0;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      gap: 1.5rem;
    }
  }

  .title-section {
    .page-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;

      .icon-rss {
        color: rgba(255, 255, 255, 0.9);
        font-size: 2rem;
      }

      @media (max-width: 768px) {
        font-size: 2rem;
        justify-content: center;
      }
    }

    .page-subtitle {
      font-size: 1.1rem;
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
    }
  }

  .stats-section {
    display: flex;
    gap: 2rem;

    .stat-item {
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      padding: 1rem 1.5rem;
      border-radius: 12px;
      backdrop-filter: blur(10px);
      
      .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: 700;
        color: white;
        line-height: 1;
      }

      .stat-label {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.8);
        margin-top: 0.25rem;
      }
    }
  }
}

.type-tabs {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .tabs-wrapper {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 2px;
    }
  }

  .tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;

    &:hover {
      border-color: #667eea;
      color: #667eea;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    &.active {
      border-color: #667eea;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    i {
      font-size: 1.125rem;
    }

    .tab-count {
      font-size: 0.75rem;
      opacity: 0.8;
    }
  }
}

.content-area {
  flex: 1;
  margin: 0;
  padding: 0;
  min-height: calc(100vh - 200px);
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 图标字体
.icon-rss::before { content: "📡"; }
.icon-article::before { content: "📄"; }
.icon-video::before { content: "🎥"; }
.icon-audio::before { content: "🎵"; }
</style>
