<template>
  <div class="subscription-center">
    <!-- 页面头部 -->
    <div class="subscription-header">
      <div class="container">
        <div class="header-content">
          <div class="title-section">
            <h1 class="page-title">
              <i class="icon-rss"></i>
              订阅中心
            </h1>
            <p class="page-subtitle">管理您的内容订阅，获取最新资讯</p>
          </div>
          <div class="stats-section">
            <div class="stat-item">
              <span class="stat-number">{{ totalUnreadCount }}</span>
              <span class="stat-label">未读内容</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ currentSubscriptions.length }}</span>
              <span class="stat-label">当前订阅</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 类型切换标签 -->
    <div class="type-tabs">
      <div class="container">
        <div class="tabs-wrapper">
          <button
            v-for="type in subscriptionTypes"
            :key="type.key"
            :class="['tab-btn', { active: currentType === type.key }]"
            @click="handleTypeChange(type.key)"
          >
            <i :class="type.icon"></i>
            <span>{{ type.label }}</span>
            <span v-if="getTypeUnreadCount(type.key)" class="unread-badge">
              {{ getTypeUnreadCount(type.key) }}
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <div class="container">
        <Transition name="fade" mode="out-in">
          <component 
            :is="currentComponent" 
            :key="currentType"
            class="subscription-content"
          />
        </Transition>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, defineAsyncComponent } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'

// 异步加载组件以优化性能
const ArticleSubscription = defineAsyncComponent(() => import('@/components/subscription/ArticleSubscription.vue'))
const ImageSubscription = defineAsyncComponent(() => import('@/components/subscription/ImageSubscription.vue'))
const VideoSubscription = defineAsyncComponent(() => import('@/components/subscription/VideoSubscription.vue'))
const AudioSubscription = defineAsyncComponent(() => import('@/components/subscription/AudioSubscription.vue'))

export default {
  name: 'SubscriptionCenter',
  components: {
    ArticleSubscription,
    ImageSubscription,
    VideoSubscription,
    AudioSubscription
  },
  setup() {
    const subscriptionStore = useSubscriptionStore()

    // 订阅类型配置
    const subscriptionTypes = [
      { key: 'article', label: '文章', icon: 'icon-article' },
      { key: 'image', label: '图片', icon: 'icon-image' },
      { key: 'video', label: '视频', icon: 'icon-video' },
      { key: 'audio', label: '音频', icon: 'icon-audio' }
    ]

    // 计算属性
    const currentType = computed(() => subscriptionStore.currentType)
    const currentSubscriptions = computed(() => subscriptionStore.currentSubscriptions)
    const totalUnreadCount = computed(() => subscriptionStore.totalUnreadCount)

    // 当前组件
    const currentComponent = computed(() => {
      const componentMap = {
        article: 'ArticleSubscription',
        image: 'ImageSubscription',
        video: 'VideoSubscription',
        audio: 'AudioSubscription'
      }
      return componentMap[currentType.value]
    })

    // 方法
    const handleTypeChange = (type) => {
      subscriptionStore.setCurrentType(type)
    }

    const getTypeUnreadCount = (type) => {
      const subs = subscriptionStore.subscriptions[type] || []
      return subs.reduce((total, sub) => total + sub.unreadCount, 0)
    }

    return {
      subscriptionTypes,
      currentType,
      currentSubscriptions,
      totalUnreadCount,
      currentComponent,
      handleTypeChange,
      getTypeUnreadCount
    }
  }
}
</script>

<style lang="scss" scoped>
.subscription-center {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.subscription-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 2rem 0;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      gap: 1.5rem;
    }
  }

  .title-section {
    .page-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;

      .icon-rss {
        color: #f59e0b;
        font-size: 2rem;
      }

      @media (max-width: 768px) {
        font-size: 2rem;
        justify-content: center;
      }
    }

    .page-subtitle {
      font-size: 1.1rem;
      color: #6b7280;
      margin: 0;
    }
  }

  .stats-section {
    display: flex;
    gap: 2rem;

    .stat-item {
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: 700;
        color: #4f46e5;
        line-height: 1;
      }

      .stat-label {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.25rem;
      }
    }
  }
}

.type-tabs {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 0;

  .tabs-wrapper {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 2px;
    }
  }

  .tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;

    &:hover {
      border-color: #4f46e5;
      color: #4f46e5;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
    }

    &.active {
      border-color: #4f46e5;
      background: #4f46e5;
      color: white;
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    }

    i {
      font-size: 1.25rem;
    }

    .unread-badge {
      background: #ef4444;
      color: white;
      font-size: 0.75rem;
      padding: 0.125rem 0.375rem;
      border-radius: 10px;
      min-width: 1.25rem;
      text-align: center;
      line-height: 1;
    }

    &.active .unread-badge {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.content-area {
  padding: 2rem 0;
  min-height: 60vh;
}

.subscription-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 图标字体（使用Font Awesome或自定义图标）
.icon-rss::before { content: "📡"; }
.icon-article::before { content: "📄"; }
.icon-image::before { content: "🖼️"; }
.icon-video::before { content: "🎥"; }
.icon-audio::before { content: "🎵"; }
</style>
