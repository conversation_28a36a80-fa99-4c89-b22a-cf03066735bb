<template>
  <div class="subscription-center">
    <!-- 页面头部 -->
    <div class="subscription-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <i class="icon-rss"></i>
            订阅中心
          </h1>
          <p class="page-subtitle">管理您的内容订阅，获取最新资讯</p>
        </div>
        <div class="stats-section">
          <div class="stat-item">
            <span class="stat-number">{{ stats.total?.contents || 0 }}</span>
            <span class="stat-label">总内容</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.total?.subscriptions || 0 }}</span>
            <span class="stat-label">订阅源</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 类型切换标签 -->
    <div class="type-tabs">
      <div class="tabs-wrapper">
        <button
          v-for="type in subscriptionTypes"
          :key="type.key"
          :class="['tab-btn', { active: currentType === type.key }]"
          @click="handleTypeChange(type.key)"
        >
          <i :class="type.icon"></i>
          <span>{{ type.label }}</span>
          <span v-if="getTypeStats(type.key)?.contents" class="content-badge">
            {{ getTypeStats(type.key).contents }}
          </span>
        </button>
      </div>
    </div>

    <!-- 主要内容区域 - 三列布局 -->
    <div class="main-content">
      <Transition name="fade" mode="out-in">
        <component
          :is="currentComponent"
          :key="currentType"
          class="subscription-content"
        />
      </Transition>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ApiClient } from '@/utils/api'

// 导入订阅组件
import ArticleSubscription from '@/components/subscription/ArticleSubscription.vue'
import VideoSubscription from '@/components/subscription/VideoSubscription.vue'
import AudioSubscription from '@/components/subscription/AudioSubscription.vue'

export default {
  name: 'SubscriptionCenter',
  components: {
    ArticleSubscription,
    VideoSubscription,
    AudioSubscription
  },
  setup() {
    // 当前选中的类型
    const currentType = ref('article')

    // 统计数据
    const stats = ref({
      article: { subscriptions: 0, contents: 0 },
      video: { subscriptions: 0, contents: 0 },
      audio: { subscriptions: 0, contents: 0 },
      total: { subscriptions: 0, contents: 0 }
    })

    // 订阅类型配置
    const subscriptionTypes = [
      { key: 'article', label: '文章', icon: 'icon-article' },
      { key: 'video', label: '视频', icon: 'icon-video' },
      { key: 'audio', label: '音频', icon: 'icon-audio' }
    ]

    // 计算当前组件
    const currentComponent = computed(() => {
      const componentMap = {
        article: 'ArticleSubscription',
        video: 'VideoSubscription',
        audio: 'AudioSubscription'
      }
      return componentMap[currentType.value] || 'ArticleSubscription'
    })

    // 获取类型统计
    const getTypeStats = (type) => {
      return stats.value[type] || { subscriptions: 0, contents: 0 }
    }

    // 处理类型切换
    const handleTypeChange = (type) => {
      currentType.value = type
    }

    // 获取统计数据
    const fetchStats = async () => {
      try {
        const response = await ApiClient.get('http://localhost:8001/api/crawler/content/stats')
        if (response.code === 200) {
          stats.value = response.data
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    }

    // 组件挂载时初始化数据
    onMounted(() => {
      fetchStats()
    })

    return {
      currentType,
      subscriptionTypes,
      currentComponent,
      stats,
      getTypeStats,
      handleTypeChange
    }
  }
}
</script>

<style lang="scss" scoped>
.subscription-center {
  min-height: 100vh;
  background: #f8fafc;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.subscription-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      gap: 1.5rem;
    }
  }

  .title-section {
    .page-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;

      .icon-rss {
        color: rgba(255, 255, 255, 0.9);
        font-size: 2rem;
      }

      @media (max-width: 768px) {
        font-size: 2rem;
        justify-content: center;
      }
    }

    .page-subtitle {
      font-size: 1.1rem;
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
    }
  }

  .stats-section {
    display: flex;
    gap: 2rem;

    .stat-item {
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      padding: 1rem 1.5rem;
      border-radius: 12px;
      backdrop-filter: blur(10px);

      .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: 700;
        color: white;
        line-height: 1;
      }

      .stat-label {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.8);
        margin-top: 0.25rem;
      }
    }
  }
}

.type-tabs {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .tabs-wrapper {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 2px;
    }
  }

  .tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;

    &:hover {
      border-color: #4f46e5;
      color: #4f46e5;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
    }

    &.active {
      border-color: #667eea;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    i {
      font-size: 1.25rem;
    }

    .content-badge {
      background: #10b981;
      color: white;
      font-size: 0.75rem;
      padding: 0.125rem 0.375rem;
      border-radius: 10px;
      min-width: 1.25rem;
      text-align: center;
      line-height: 1;
    }

    &.active .content-badge {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.main-content {
  flex: 1;
  padding: 0;
  margin: 0;
  background: #f8fafc;
  min-height: calc(100vh - 200px);
}

.subscription-content {
  height: 100%;
  width: 100%;
  background: transparent;
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 图标字体（使用Font Awesome或自定义图标）
.icon-rss::before { content: "📡"; }
.icon-article::before { content: "📄"; }
.icon-image::before { content: "🖼️"; }
.icon-video::before { content: "🎥"; }
.icon-audio::before { content: "🎵"; }
</style>
