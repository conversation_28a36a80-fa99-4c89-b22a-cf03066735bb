<template>
  <Layout>
    <div class="my-prompts-page">
      <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>个人空间</h1>
          <p>管理您的Prompt创作和收藏</p>
        </div>
        
        <!-- 统计概览 -->
        <div class="stats-overview">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ userStats.totalPrompts }}</div>
              <div class="stat-label">我的Prompt</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-eye"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ userStats.totalViews }}</div>
              <div class="stat-label">总浏览量</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-heart"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ userStats.totalLikes }}</div>
              <div class="stat-label">获得点赞</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-bookmark"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ userStats.totalBookmarks }}</div>
              <div class="stat-label">收藏数量</div>
            </div>
          </div>
        </div>
        
        <!-- 操作栏 -->
        <div class="action-bar">
          <div class="action-left">
            <button class="btn btn-primary" @click="createNewPrompt">
              <i class="fas fa-plus"></i>
              新建Prompt
            </button>
            <button class="btn btn-outline" @click="importPrompt">
              <i class="fas fa-upload"></i>
              导入Prompt
            </button>
          </div>
          <div class="action-right">
            <div class="search-filter">
              <input 
                type="text" 
                placeholder="搜索我的Prompt..."
                v-model="searchQuery"
                @input="filterPrompts"
              >
              <select v-model="selectedCategory" @change="filterPrompts">
                <option value="">全部分类</option>
                <option value="writing">文案写作</option>
                <option value="coding">代码编程</option>
                <option value="analysis">数据分析</option>
                <option value="design">创意设计</option>
              </select>
              <select v-model="statusFilter" @change="filterPrompts">
                <option value="">全部状态</option>
                <option value="draft">草稿</option>
                <option value="published">已发布</option>
                <option value="private">私有</option>
              </select>
            </div>
            <div class="view-toggle">
              <button 
                class="view-btn"
                :class="{ active: viewMode === 'grid' }"
                @click="setViewMode('grid')"
              >
                <i class="fas fa-th"></i>
              </button>
              <button 
                class="view-btn"
                :class="{ active: viewMode === 'list' }"
                @click="setViewMode('list')"
              >
                <i class="fas fa-list"></i>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 标签页 -->
        <div class="tab-nav">
          <button 
            class="tab-btn"
            :class="{ active: activeTab === 'my-prompts' }"
            @click="setActiveTab('my-prompts')"
          >
            我的Prompt ({{ myPrompts.length }})
          </button>
          <button 
            class="tab-btn"
            :class="{ active: activeTab === 'bookmarks' }"
            @click="setActiveTab('bookmarks')"
          >
            收藏夹 ({{ bookmarkedPrompts.length }})
          </button>
          <button 
            class="tab-btn"
            :class="{ active: activeTab === 'drafts' }"
            @click="setActiveTab('drafts')"
          >
            草稿箱 ({{ draftPrompts.length }})
          </button>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-area">
          <!-- 我的Prompt -->
          <div v-if="activeTab === 'my-prompts'" class="tab-content">
            <div class="prompts-grid" :class="{ 'prompts-list': viewMode === 'list' }">
              <div 
                v-for="prompt in filteredMyPrompts" 
                :key="prompt.id"
                class="prompt-card"
              >
                <div class="card-header">
                  <h3 class="card-title">{{ prompt.title }}</h3>
                  <div class="card-menu">
                    <button class="menu-btn" @click="toggleMenu(prompt.id)">
                      <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div v-if="openMenuId === prompt.id" class="menu-dropdown">
                      <button @click="editPrompt(prompt.id)">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                      <button @click="duplicatePrompt(prompt.id)">
                        <i class="fas fa-copy"></i>
                        复制
                      </button>
                      <button @click="sharePrompt(prompt.id)">
                        <i class="fas fa-share"></i>
                        分享
                      </button>
                      <button @click="deletePrompt(prompt.id)" class="danger">
                        <i class="fas fa-trash"></i>
                        删除
                      </button>
                    </div>
                  </div>
                </div>
                
                <p class="card-description">{{ prompt.description }}</p>
                
                <div class="card-tags">
                  <span v-for="tag in prompt.tags" :key="tag" class="tag">{{ tag }}</span>
                </div>
                
                <div class="card-meta">
                  <div class="meta-info">
                    <span class="status-badge" :class="prompt.status">{{ getStatusText(prompt.status) }}</span>
                    <span class="update-time">{{ formatDate(prompt.updatedAt) }}</span>
                  </div>
                  <div class="card-stats">
                    <span class="stat">
                      <i class="fas fa-eye"></i>
                      {{ prompt.views }}
                    </span>
                    <span class="stat">
                      <i class="fas fa-heart"></i>
                      {{ prompt.likes }}
                    </span>
                    <span class="stat">
                      <i class="fas fa-comments"></i>
                      {{ prompt.comments }}
                    </span>
                  </div>
                </div>
                
                <div class="card-actions">
                  <button class="btn btn-primary" @click="viewPrompt(prompt.id)">查看详情</button>
                  <button class="btn btn-outline" @click="editPrompt(prompt.id)">编辑</button>
                  <button 
                    v-if="prompt.status === 'draft'"
                    class="btn btn-secondary"
                    @click="publishPrompt(prompt.id)"
                  >
                    发布
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 收藏夹 -->
          <div v-if="activeTab === 'bookmarks'" class="tab-content">
            <div class="prompts-grid" :class="{ 'prompts-list': viewMode === 'list' }">
              <div 
                v-for="prompt in filteredBookmarks" 
                :key="prompt.id"
                class="prompt-card"
              >
                <div class="card-header">
                  <h3 class="card-title">{{ prompt.title }}</h3>
                  <button class="remove-bookmark" @click="removeBookmark(prompt.id)">
                    <i class="fas fa-bookmark"></i>
                  </button>
                </div>
                
                <p class="card-description">{{ prompt.description }}</p>
                
                <div class="card-tags">
                  <span v-for="tag in prompt.tags" :key="tag" class="tag">{{ tag }}</span>
                </div>
                
                <div class="card-meta">
                  <div class="author-info">
                    <span>作者：{{ prompt.author }}</span>
                  </div>
                  <div class="bookmark-time">
                    <span>收藏于：{{ formatDate(prompt.bookmarkedAt) }}</span>
                  </div>
                </div>
                
                <div class="card-actions">
                  <button class="btn btn-primary" @click="viewPrompt(prompt.id)">查看详情</button>
                  <button class="btn btn-outline" @click="copyToSpace(prompt.id)">复制到空间</button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 草稿箱 -->
          <div v-if="activeTab === 'drafts'" class="tab-content">
            <div class="prompts-grid" :class="{ 'prompts-list': viewMode === 'list' }">
              <div 
                v-for="prompt in filteredDrafts" 
                :key="prompt.id"
                class="prompt-card draft-card"
              >
                <div class="card-header">
                  <h3 class="card-title">{{ prompt.title || '未命名草稿' }}</h3>
                  <button class="delete-draft" @click="deleteDraft(prompt.id)">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
                
                <p class="card-description">{{ prompt.description || '暂无描述' }}</p>
                
                <div class="card-meta">
                  <div class="draft-info">
                    <span class="draft-badge">草稿</span>
                    <span class="save-time">保存于：{{ formatDate(prompt.savedAt) }}</span>
                  </div>
                </div>
                
                <div class="card-actions">
                  <button class="btn btn-primary" @click="editPrompt(prompt.id)">继续编辑</button>
                  <button class="btn btn-outline" @click="publishPrompt(prompt.id)">直接发布</button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="getCurrentTabData().length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-folder-open"></i>
            </div>
            <h3>{{ getEmptyStateTitle() }}</h3>
            <p>{{ getEmptyStateDescription() }}</p>
            <button v-if="activeTab === 'my-prompts'" class="btn btn-primary" @click="createNewPrompt">
              创建第一个Prompt
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 删除确认模态框 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="closeDeleteModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <button class="modal-close" @click="closeDeleteModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <p>确定要删除这个Prompt吗？此操作不可撤销。</p>
        </div>
        <div class="modal-actions">
          <button class="btn btn-outline" @click="closeDeleteModal">取消</button>
          <button class="btn btn-danger" @click="confirmDelete">确认删除</button>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'MyPrompts',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    
    const searchQuery = ref('')
    const selectedCategory = ref('')
    const statusFilter = ref('')
    const viewMode = ref('grid')
    const activeTab = ref('my-prompts')
    const openMenuId = ref(null)
    const showDeleteModal = ref(false)
    const deletePromptId = ref(null)
    
    const userStats = reactive({
      totalPrompts: 15,
      totalViews: '12.5k',
      totalLikes: '3.2k',
      totalBookmarks: 45
    })
    
    const myPrompts = ref([
      {
        id: 1,
        title: '营销文案生成器',
        description: '专业的营销文案生成工具，支持多种风格和场景',
        tags: ['文案写作', '营销', '商业'],
        status: 'published',
        category: 'writing',
        views: 1250,
        likes: 89,
        comments: 12,
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        title: '代码审查助手',
        description: '帮助开发者进行代码审查和优化建议',
        tags: ['代码编程', '审查', '优化'],
        status: 'draft',
        category: 'coding',
        views: 0,
        likes: 0,
        comments: 0,
        updatedAt: '2024-01-14T15:20:00Z'
      }
    ])
    
    const bookmarkedPrompts = ref([
      {
        id: 101,
        title: '完美文案生成器',
        description: '专为营销文案量身定制的Prompt',
        tags: ['文案写作', '营销'],
        author: '刘小明',
        bookmarkedAt: '2024-01-10T09:15:00Z'
      }
    ])
    
    const draftPrompts = ref([
      {
        id: 201,
        title: '数据分析报告生成器',
        description: '自动生成数据分析报告',
        savedAt: '2024-01-12T14:45:00Z'
      }
    ])
    
    const filteredMyPrompts = computed(() => {
      return filterPromptsByQuery(myPrompts.value)
    })
    
    const filteredBookmarks = computed(() => {
      return filterPromptsByQuery(bookmarkedPrompts.value)
    })
    
    const filteredDrafts = computed(() => {
      return filterPromptsByQuery(draftPrompts.value)
    })
    
    const filterPromptsByQuery = (prompts) => {
      return prompts.filter(prompt => {
        const matchesSearch = !searchQuery.value || 
          prompt.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          prompt.description.toLowerCase().includes(searchQuery.value.toLowerCase())
        
        const matchesCategory = !selectedCategory.value || prompt.category === selectedCategory.value
        const matchesStatus = !statusFilter.value || prompt.status === statusFilter.value
        
        return matchesSearch && matchesCategory && matchesStatus
      })
    }
    
    const filterPrompts = () => {
      // 触发计算属性重新计算
    }
    
    const setViewMode = (mode) => {
      viewMode.value = mode
    }
    
    const setActiveTab = (tab) => {
      activeTab.value = tab
    }
    
    const getCurrentTabData = () => {
      switch (activeTab.value) {
        case 'my-prompts':
          return filteredMyPrompts.value
        case 'bookmarks':
          return filteredBookmarks.value
        case 'drafts':
          return filteredDrafts.value
        default:
          return []
      }
    }
    
    const getEmptyStateTitle = () => {
      switch (activeTab.value) {
        case 'my-prompts':
          return '还没有创建任何Prompt'
        case 'bookmarks':
          return '还没有收藏任何Prompt'
        case 'drafts':
          return '没有草稿'
        default:
          return '暂无数据'
      }
    }
    
    const getEmptyStateDescription = () => {
      switch (activeTab.value) {
        case 'my-prompts':
          return '点击"新建Prompt"按钮开始创建您的第一个Prompt'
        case 'bookmarks':
          return '浏览市场并收藏您喜欢的Prompt'
        case 'drafts':
          return '编辑Prompt时系统会自动保存草稿'
        default:
          return ''
      }
    }
    
    const createNewPrompt = () => {
      router.push('/prompt-edit')
    }
    
    const importPrompt = () => {
      toastStore.info('导入功能开发中...')
    }
    
    const toggleMenu = (id) => {
      openMenuId.value = openMenuId.value === id ? null : id
    }
    
    const editPrompt = (id) => {
      router.push(`/prompt-edit/${id}`)
    }
    
    const viewPrompt = (id) => {
      router.push(`/prompt/${id}`)
    }
    
    const duplicatePrompt = (id) => {
      toastStore.success('Prompt已复制')
    }
    
    const sharePrompt = (id) => {
      toastStore.success('分享链接已复制到剪贴板')
    }
    
    const deletePrompt = (id) => {
      deletePromptId.value = id
      showDeleteModal.value = true
    }
    
    const confirmDelete = () => {
      if (deletePromptId.value) {
        const index = myPrompts.value.findIndex(p => p.id === deletePromptId.value)
        if (index > -1) {
          myPrompts.value.splice(index, 1)
          toastStore.success('Prompt已删除')
        }
      }
      closeDeleteModal()
    }
    
    const closeDeleteModal = () => {
      showDeleteModal.value = false
      deletePromptId.value = null
    }
    
    const publishPrompt = (id) => {
      const prompt = myPrompts.value.find(p => p.id === id)
      if (prompt) {
        prompt.status = 'published'
        toastStore.success('Prompt已发布')
      }
    }
    
    const removeBookmark = (id) => {
      const index = bookmarkedPrompts.value.findIndex(p => p.id === id)
      if (index > -1) {
        bookmarkedPrompts.value.splice(index, 1)
        toastStore.success('已取消收藏')
      }
    }
    
    const copyToSpace = (id) => {
      toastStore.success('已复制到个人空间')
    }
    
    const deleteDraft = (id) => {
      const index = draftPrompts.value.findIndex(p => p.id === id)
      if (index > -1) {
        draftPrompts.value.splice(index, 1)
        toastStore.success('草稿已删除')
      }
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        published: '已发布',
        draft: '草稿',
        private: '私有'
      }
      return statusMap[status] || status
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    onMounted(() => {
      // 初始化数据
    })
    
    return {
      searchQuery,
      selectedCategory,
      statusFilter,
      viewMode,
      activeTab,
      openMenuId,
      showDeleteModal,
      userStats,
      myPrompts,
      bookmarkedPrompts,
      draftPrompts,
      filteredMyPrompts,
      filteredBookmarks,
      filteredDrafts,
      filterPrompts,
      setViewMode,
      setActiveTab,
      getCurrentTabData,
      getEmptyStateTitle,
      getEmptyStateDescription,
      createNewPrompt,
      importPrompt,
      toggleMenu,
      editPrompt,
      viewPrompt,
      duplicatePrompt,
      sharePrompt,
      deletePrompt,
      confirmDelete,
      closeDeleteModal,
      publishPrompt,
      removeBookmark,
      copyToSpace,
      deleteDraft,
      getStatusText,
      formatDate
    }
  }
}
</script>

<style scoped>
.my-prompts-page {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8px;
}

.page-header p {
  color: #6b7280;
  font-size: 16px;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4f46e5;
  font-size: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.action-left {
  display: flex;
  gap: 12px;
}

.action-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-filter {
  display: flex;
  gap: 10px;
}

.search-filter input,
.search-filter select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.search-filter input {
  width: 200px;
}

.tab-nav {
  display: flex;
  gap: 2px;
  margin-bottom: 20px;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

.tab-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-area {
  min-height: 400px;
}

.prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.prompts-list {
  grid-template-columns: 1fr;
}

.prompt-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.prompt-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.card-menu {
  position: relative;
}

.menu-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
}

.menu-btn:hover {
  background: #f3f4f6;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  z-index: 100;
  min-width: 120px;
}

.menu-dropdown button {
  width: 100%;
  padding: 10px 15px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-dropdown button:hover {
  background: #f9fafb;
}

.menu-dropdown button.danger {
  color: #ef4444;
}

.card-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
}

.card-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.published {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.draft {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.private {
  background: #e5e7eb;
  color: #374151;
}

.update-time {
  color: #9ca3af;
  font-size: 12px;
}

.card-stats {
  display: flex;
  gap: 15px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 12px;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.3;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #374151;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 20px;
}

.remove-bookmark,
.delete-draft {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
}

.remove-bookmark:hover,
.delete-draft:hover {
  background: #f3f4f6;
  color: #ef4444;
}

.draft-card {
  border-left: 4px solid #f59e0b;
}

.draft-badge {
  background: #fef3c7;
  color: #92400e;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.author-info,
.bookmark-time,
.save-time {
  color: #6b7280;
  font-size: 12px;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-filter {
    flex-direction: column;
    width: 100%;
  }
  
  .search-filter input {
    width: 100%;
  }
  
  .prompts-grid {
    grid-template-columns: 1fr;
  }
}
</style>