<template>
  <Layout>
    <div class="team-space-page">
      <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>团队空间</h1>
          <p>与团队成员协作，共同创建和管理Prompt</p>
        </div>
        
        <!-- 团队切换器 -->
        <div class="team-selector">
          <div class="current-team">
            <div class="team-info">
              <div class="team-avatar">
                <i class="fas fa-users"></i>
              </div>
              <div class="team-details">
                <h3>{{ currentTeam.name }}</h3>
                <p>{{ currentTeam.members.length }} 名成员</p>
              </div>
            </div>
            <button class="switch-team-btn" @click="showTeamModal = true">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          
          <div class="team-actions">
            <button class="btn btn-outline" @click="showCreateTeamModal = true">
              <i class="fas fa-plus"></i>
              创建团队
            </button>
            <button class="btn btn-primary" @click="showInviteModal = true">
              <i class="fas fa-user-plus"></i>
              邀请成员
            </button>
          </div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ teamStats.prompts }}</div>
              <div class="stat-label">团队Prompt</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ teamStats.members }}</div>
              <div class="stat-label">团队成员</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ teamStats.activities }}</div>
              <div class="stat-label">本周活动</div>
            </div>
          </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-content">
          <!-- 左侧内容 -->
          <div class="content-left">
            <!-- 标签导航 -->
            <div class="tab-nav">
              <button 
                class="tab-btn"
                :class="{ active: activeTab === 'prompts' }"
                @click="setActiveTab('prompts')"
              >
                团队Prompt
              </button>
              <button 
                class="tab-btn"
                :class="{ active: activeTab === 'members' }"
                @click="setActiveTab('members')"
              >
                成员管理
              </button>
              <button 
                class="tab-btn"
                :class="{ active: activeTab === 'settings' }"
                @click="setActiveTab('settings')"
              >
                团队设置
              </button>
            </div>
            
            <!-- 团队Prompt -->
            <div v-if="activeTab === 'prompts'" class="tab-content">
              <div class="prompts-header">
                <div class="search-filter">
                  <input 
                    type="text" 
                    placeholder="搜索团队Prompt..."
                    v-model="searchQuery"
                  >
                  <select v-model="filterCategory">
                    <option value="">全部分类</option>
                    <option value="writing">文案写作</option>
                    <option value="coding">代码编程</option>
                    <option value="analysis">数据分析</option>
                  </select>
                </div>
                <button class="btn btn-primary" @click="createPrompt">
                  <i class="fas fa-plus"></i>
                  新建Prompt
                </button>
              </div>
              
              <div class="prompts-grid">
                <div v-for="prompt in filteredPrompts" :key="prompt.id" class="prompt-card">
                  <div class="card-header">
                    <h3>{{ prompt.title }}</h3>
                    <div class="card-menu">
                      <button class="menu-btn" @click="toggleMenu(prompt.id)">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                    </div>
                  </div>
                  
                  <p class="card-description">{{ prompt.description }}</p>
                  
                  <div class="card-meta">
                    <div class="author-info">
                      <img v-if="prompt.author.avatar" :src="prompt.author.avatar" :alt="prompt.author.name">
                      <i v-else class="fas fa-user"></i>
                      <span>{{ prompt.author.name }}</span>
                    </div>
                    <div class="update-time">{{ formatDate(prompt.updatedAt) }}</div>
                  </div>
                  
                  <div class="card-tags">
                    <span v-for="tag in prompt.tags" :key="tag" class="tag">{{ tag }}</span>
                  </div>
                  
                  <div class="card-stats">
                    <div class="stat">
                      <i class="fas fa-eye"></i>
                      <span>{{ prompt.views }}</span>
                    </div>
                    <div class="stat">
                      <i class="fas fa-heart"></i>
                      <span>{{ prompt.likes }}</span>
                    </div>
                    <div class="stat">
                      <i class="fas fa-comments"></i>
                      <span>{{ prompt.comments }}</span>
                    </div>
                  </div>
                  
                  <div class="card-actions">
                    <button class="btn btn-primary" @click="editPrompt(prompt.id)">编辑</button>
                    <button class="btn btn-outline" @click="viewPrompt(prompt.id)">查看</button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 成员管理 -->
            <div v-if="activeTab === 'members'" class="tab-content">
              <div class="members-header">
                <h3>团队成员 ({{ currentTeam.members.length }})</h3>
                <button class="btn btn-primary" @click="showInviteModal = true">
                  <i class="fas fa-user-plus"></i>
                  邀请成员
                </button>
              </div>
              
              <div class="members-list">
                <div v-for="member in currentTeam.members" :key="member.id" class="member-item">
                  <div class="member-info">
                    <div class="member-avatar">
                      <img v-if="member.avatar" :src="member.avatar" :alt="member.name">
                      <i v-else class="fas fa-user"></i>
                    </div>
                    <div class="member-details">
                      <div class="member-name">{{ member.name }}</div>
                      <div class="member-email">{{ member.email }}</div>
                    </div>
                  </div>
                  
                  <div class="member-role">
                    <span class="role-badge" :class="member.role">{{ getRoleLabel(member.role) }}</span>
                  </div>
                  
                  <div class="member-actions">
                    <button class="btn btn-outline" @click="changeRole(member.id)">
                      <i class="fas fa-edit"></i>
                      编辑
                    </button>
                    <button class="btn btn-danger" @click="removeMember(member.id)">
                      <i class="fas fa-trash"></i>
                      移除
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 团队设置 -->
            <div v-if="activeTab === 'settings'" class="tab-content">
              <div class="settings-section">
                <h3>基本设置</h3>
                <div class="form-group">
                  <label>团队名称</label>
                  <input type="text" v-model="teamSettings.name" class="form-input">
                </div>
                <div class="form-group">
                  <label>团队描述</label>
                  <textarea v-model="teamSettings.description" class="form-textarea" rows="3"></textarea>
                </div>
                <div class="form-group">
                  <label>团队可见性</label>
                  <select v-model="teamSettings.visibility" class="form-select">
                    <option value="private">私有</option>
                    <option value="public">公开</option>
                  </select>
                </div>
                <button class="btn btn-primary" @click="saveSettings">保存设置</button>
              </div>
              
              <div class="settings-section">
                <h3>权限设置</h3>
                <div class="permission-item">
                  <label>
                    <input type="checkbox" v-model="teamSettings.allowMemberInvite">
                    允许成员邀请其他人
                  </label>
                </div>
                <div class="permission-item">
                  <label>
                    <input type="checkbox" v-model="teamSettings.allowMemberCreate">
                    允许成员创建Prompt
                  </label>
                </div>
                <div class="permission-item">
                  <label>
                    <input type="checkbox" v-model="teamSettings.allowMemberEdit">
                    允许成员编辑他人的Prompt
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧活动流 -->
          <div class="content-right">
            <div class="activity-section">
              <h3>最近活动</h3>
              <div class="activity-list">
                <div v-for="activity in activities" :key="activity.id" class="activity-item">
                  <div class="activity-icon">
                    <i :class="getActivityIcon(activity.type)"></i>
                  </div>
                  <div class="activity-content">
                    <div class="activity-text">
                      <strong>{{ activity.user }}</strong>
                      {{ activity.action }}
                      <strong>{{ activity.target }}</strong>
                    </div>
                    <div class="activity-time">{{ formatDate(activity.time) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 邀请成员模态框 -->
    <div v-if="showInviteModal" class="modal-overlay" @click="closeInviteModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>邀请成员</h3>
          <button class="modal-close" @click="closeInviteModal">×</button>
        </div>
        <div class="modal-content">
          <div class="form-group">
            <label>邮箱地址</label>
            <input type="email" v-model="inviteEmail" placeholder="请输入邮箱地址" class="form-input">
          </div>
          <div class="form-group">
            <label>角色</label>
            <select v-model="inviteRole" class="form-select">
              <option value="member">成员</option>
              <option value="admin">管理员</option>
            </select>
          </div>
          <div class="form-group">
            <label>邀请消息</label>
            <textarea v-model="inviteMessage" placeholder="可选的邀请消息" class="form-textarea" rows="3"></textarea>
          </div>
        </div>
        <div class="modal-actions">
          <button class="btn btn-outline" @click="closeInviteModal">取消</button>
          <button class="btn btn-primary" @click="sendInvite">发送邀请</button>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'TeamSpace',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    
    const activeTab = ref('prompts')
    const searchQuery = ref('')
    const filterCategory = ref('')
    const showInviteModal = ref(false)
    const showTeamModal = ref(false)
    const showCreateTeamModal = ref(false)
    const inviteEmail = ref('')
    const inviteRole = ref('member')
    const inviteMessage = ref('')
    
    const currentTeam = reactive({
      id: 1,
      name: '我的团队',
      members: [
        {
          id: 1,
          name: '张三',
          email: '<EMAIL>',
          avatar: null,
          role: 'owner'
        },
        {
          id: 2,
          name: '李四',
          email: '<EMAIL>',
          avatar: null,
          role: 'admin'
        },
        {
          id: 3,
          name: '王五',
          email: '<EMAIL>',
          avatar: null,
          role: 'member'
        }
      ]
    })
    
    const teamStats = reactive({
      prompts: 24,
      members: 3,
      activities: 15
    })
    
    const teamSettings = reactive({
      name: '我的团队',
      description: '这是我们的团队描述',
      visibility: 'private',
      allowMemberInvite: true,
      allowMemberCreate: true,
      allowMemberEdit: false
    })
    
    const prompts = ref([
      {
        id: 1,
        title: '团队文案模板',
        description: '用于团队内部文案创作的标准模板',
        author: {
          name: '张三',
          avatar: null
        },
        tags: ['文案', '模板', '团队'],
        views: 156,
        likes: 23,
        comments: 8,
        updatedAt: '2024-01-15T10:30:00Z',
        category: 'writing'
      },
      {
        id: 2,
        title: '代码审查清单',
        description: '团队代码审查的标准流程和检查点',
        author: {
          name: '李四',
          avatar: null
        },
        tags: ['代码', '审查', '清单'],
        views: 89,
        likes: 15,
        comments: 4,
        updatedAt: '2024-01-14T15:20:00Z',
        category: 'coding'
      }
    ])
    
    const activities = ref([
      {
        id: 1,
        user: '张三',
        action: '创建了',
        target: '团队文案模板',
        type: 'create',
        time: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        user: '李四',
        action: '编辑了',
        target: '代码审查清单',
        type: 'edit',
        time: '2024-01-14T15:20:00Z'
      },
      {
        id: 3,
        user: '王五',
        action: '加入了团队',
        target: '',
        type: 'join',
        time: '2024-01-13T09:15:00Z'
      }
    ])
    
    const filteredPrompts = computed(() => {
      return prompts.value.filter(prompt => {
        const matchesSearch = !searchQuery.value || 
          prompt.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          prompt.description.toLowerCase().includes(searchQuery.value.toLowerCase())
        
        const matchesCategory = !filterCategory.value || prompt.category === filterCategory.value
        
        return matchesSearch && matchesCategory
      })
    })
    
    const setActiveTab = (tab) => {
      activeTab.value = tab
    }
    
    const createPrompt = () => {
      router.push('/prompt-edit')
    }
    
    const editPrompt = (id) => {
      router.push(`/prompt-edit/${id}`)
    }
    
    const viewPrompt = (id) => {
      router.push(`/prompt/${id}`)
    }
    
    const toggleMenu = (id) => {
      // 切换菜单显示状态
    }
    
    const closeInviteModal = () => {
      showInviteModal.value = false
      inviteEmail.value = ''
      inviteRole.value = 'member'
      inviteMessage.value = ''
    }
    
    const sendInvite = () => {
      if (!inviteEmail.value.trim()) {
        toastStore.error('请输入邮箱地址')
        return
      }
      
      // 发送邀请逻辑
      toastStore.success('邀请已发送')
      closeInviteModal()
    }
    
    const changeRole = (memberId) => {
      toastStore.info('角色编辑功能开发中...')
    }
    
    const removeMember = (memberId) => {
      const index = currentTeam.members.findIndex(m => m.id === memberId)
      if (index > -1) {
        currentTeam.members.splice(index, 1)
        toastStore.success('成员已移除')
      }
    }
    
    const saveSettings = () => {
      toastStore.success('设置已保存')
    }
    
    const getRoleLabel = (role) => {
      const labels = {
        owner: '所有者',
        admin: '管理员',
        member: '成员'
      }
      return labels[role] || role
    }
    
    const getActivityIcon = (type) => {
      const icons = {
        create: 'fas fa-plus',
        edit: 'fas fa-edit',
        join: 'fas fa-user-plus',
        leave: 'fas fa-user-minus'
      }
      return icons[type] || 'fas fa-info'
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    return {
      activeTab,
      searchQuery,
      filterCategory,
      showInviteModal,
      showTeamModal,
      showCreateTeamModal,
      inviteEmail,
      inviteRole,
      inviteMessage,
      currentTeam,
      teamStats,
      teamSettings,
      prompts,
      activities,
      filteredPrompts,
      setActiveTab,
      createPrompt,
      editPrompt,
      viewPrompt,
      toggleMenu,
      closeInviteModal,
      sendInvite,
      changeRole,
      removeMember,
      saveSettings,
      getRoleLabel,
      getActivityIcon,
      formatDate
    }
  }
}
</script>

<style scoped>
.team-space-page {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8px;
}

.page-header p {
  color: #6b7280;
  font-size: 16px;
}

.team-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.current-team {
  display: flex;
  align-items: center;
  gap: 15px;
}

.team-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.team-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.team-details h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.team-details p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.switch-team-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
}

.switch-team-btn:hover {
  background: #f3f4f6;
}

.team-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4f46e5;
  font-size: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
}

.content-left {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.tab-nav {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
  flex: 1;
  padding: 15px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
  background: white;
}

.tab-content {
  padding: 20px;
}

.prompts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-filter {
  display: flex;
  gap: 10px;
}

.search-filter input,
.search-filter select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.prompt-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.menu-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.menu-btn:hover {
  background: #f3f4f6;
}

.card-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.author-info img,
.author-info i {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.update-time {
  font-size: 12px;
  color: #9ca3af;
}

.card-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.card-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 12px;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.members-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.member-email {
  font-size: 12px;
  color: #6b7280;
}

.role-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.role-badge.owner {
  background: #fef3c7;
  color: #92400e;
}

.role-badge.admin {
  background: #dbeafe;
  color: #1e40af;
}

.role-badge.member {
  background: #f3f4f6;
  color: #374151;
}

.member-actions {
  display: flex;
  gap: 8px;
}

.member-actions .btn {
  padding: 6px 12px;
  font-size: 12px;
}

.settings-section {
  margin-bottom: 30px;
}

.settings-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 15px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.permission-item {
  margin-bottom: 15px;
}

.permission-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.content-right {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  padding: 20px;
  height: fit-content;
}

.activity-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 15px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  gap: 12px;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4f46e5;
  font-size: 14px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
}

.activity-time {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
}

.modal-content {
  margin-bottom: 20px;
}

.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.btn-danger {
  background: #ef4444;
  color: white;
  border: 1px solid #ef4444;
}

.btn-danger:hover {
  background: #dc2626;
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .content-right {
    order: -1;
  }
}

@media (max-width: 768px) {
  .team-selector {
    flex-direction: column;
    gap: 15px;
  }
  
  .prompts-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-filter {
    flex-direction: column;
    width: 100%;
  }
  
  .prompts-grid {
    grid-template-columns: 1fr;
  }
  
  .member-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .member-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>