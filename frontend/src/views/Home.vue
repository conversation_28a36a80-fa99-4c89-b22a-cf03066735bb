<template>
  <Layout>
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <p class="hero-subtitle">🚀专业AI Prompt精选发布工具</p>
          <h1 class="hero-title">AI知识库<br>让每个Prompt都发挥价值</h1>
          <p class="hero-description">
            发现、分享和优化最佳AI提示词，与全球开发者一起构建更智能的AI应用体验。
          </p>
          
          <!-- 搜索框 -->
          <div class="hero-search">
            <input 
              type="text" 
              placeholder="搜索Prompt、作者或关键词 (如：文案编写、GPT-4、数据分析...)"
              v-model="heroSearchQuery"
              @keypress.enter="performHeroSearch"
            >
            <button class="search-btn" @click="performHeroSearch">搜索</button>
          </div>
          
          <!-- 统计数据 -->
          <div class="stats-container">
            <div class="stat-item">
              <div class="stat-number">{{ stats.prompts }}</div>
              <div class="stat-label">精选Prompt</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.users }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.categories }}</div>
              <div class="stat-label">应用场景</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.satisfaction }}</div>
              <div class="stat-label">满意度</div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="hero-actions">
            <button class="btn btn-primary" @click="scrollToPopular">开始探索</button>
            <button class="btn btn-secondary" @click="goToPublish">发布Prompt</button>
          </div>
        </div>
      </div>
    </section>

    <!-- 分类标签栏 -->
    <section class="category-section">
      <div class="container">
        <div class="category-tabs">
          <button 
            v-for="category in categories" 
            :key="category.id"
            class="tab-btn"
            :class="{ active: selectedCategory === category.id }"
            @click="selectCategory(category.id)"
          >
            {{ category.name }}
            <span v-if="category.count" class="count">{{ category.count }}</span>
          </button>
        </div>
        
        <div class="category-filters">
          <div class="filter-group">
            <label>最新发布</label>
            <select v-model="filters.timeFilter">
              <option value="all">全部更新</option>
              <option value="latest">最新发布</option>
              <option value="recent">最近更新</option>
            </select>
          </div>
          <div class="filter-group">
            <label>排序</label>
            <select v-model="filters.sortBy">
              <option value="recommended">推荐优先</option>
              <option value="latest">最新发布</option>
              <option value="popular">使用最多</option>
            </select>
          </div>
          <div class="view-toggle">
            <button 
              class="view-btn"
              :class="{ active: viewMode === 'grid' }"
              @click="setViewMode('grid')"
            >
              <i class="fas fa-th"></i>
            </button>
            <button 
              class="view-btn"
              :class="{ active: viewMode === 'list' }"
              @click="setViewMode('list')"
            >
              <i class="fas fa-list"></i>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- 精选推荐 -->
    <section class="featured-section">
      <div class="container">
        <h2 class="section-title">
          <i class="fas fa-star"></i>精选推荐
        </h2>
        <p class="section-subtitle">为您精心挑选的优质Prompt</p>
        
        <div class="featured-cards">
          <div 
            v-for="prompt in featuredPrompts" 
            :key="prompt.id"
            class="prompt-card featured-card"
            @click="viewPrompt(prompt.id)"
          >
            <div class="card-header">
              <h3 class="card-title">{{ prompt.title }}</h3>
              <span class="card-badge">精选</span>
            </div>
            <p class="card-description">{{ prompt.description }}</p>
            <div class="card-tags">
              <span v-for="tag in prompt.tags" :key="tag" class="tag">{{ tag }}</span>
            </div>
            <div class="card-info">
              <div class="model-info">
                <span v-for="model in prompt.models" :key="model" class="model-tag">{{ model }}</span>
              </div>
              <div class="author-info">
                <div class="author-avatar">{{ prompt.author.charAt(0) }}</div>
                <span>{{ prompt.author }}</span>
              </div>
            </div>
            <div class="card-stats">
              <div class="stat">
                <i class="fas fa-thumbs-up"></i>
                <span>{{ prompt.likes }} 赞同</span>
              </div>
              <div class="stat">
                <i class="fas fa-download"></i>
                <span>{{ prompt.uses }} 使用</span>
              </div>
              <div class="stat">
                <i class="fas fa-comments"></i>
                <span>{{ prompt.comments }} 评论</span>
              </div>
            </div>
            <div class="card-actions" @click.stop>
              <button class="btn btn-primary" @click="usePrompt(prompt.id)">立即使用</button>
              <button 
                class="btn btn-secondary like-btn" 
                :class="{ liked: prompt.isLiked }"
                @click="toggleLike(prompt.id)"
                :title="prompt.isLiked ? '取消点赞' : '点赞'"
              >
                <i class="fas fa-heart"></i>
                <span class="like-count">{{ prompt.likes }}</span>
              </button>
              <button 
                class="btn btn-secondary collect-btn" 
                :class="{ bookmarked: prompt.isBookmarked }"
                @click="toggleBookmark(prompt.id)"
                :title="prompt.isBookmarked ? '取消收藏' : '收藏'"
              >
                <i class="fas fa-bookmark"></i>
              </button>
              <button class="btn btn-secondary copy-to-space-btn" @click="copyToSpace(prompt.id)" title="复制到个人空间">
                <i class="fas fa-copy"></i>
              </button>
              <button class="btn btn-secondary share-btn" @click="sharePrompt(prompt.id)" title="分享">
                <i class="fas fa-share"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门Prompt -->
    <section class="popular-section" id="popular-section">
      <div class="container">
        <h2 class="section-title">
          <i class="fas fa-fire"></i>热门Prompt
        </h2>
        <p class="section-subtitle">社区最受欢迎的Prompt模板</p>
        
        <div class="prompts-grid" :class="{ 'prompts-list': viewMode === 'list' }">
          <div 
            v-for="prompt in filteredPrompts" 
            :key="prompt.id"
            class="prompt-card"
            @click="viewPrompt(prompt.id)"
          >
            <div class="card-header">
              <h3 class="card-title">{{ prompt.title }}</h3>
              <span class="card-badge">{{ prompt.badge }}</span>
            </div>
            <p class="card-description">{{ prompt.description }}</p>
            <div class="card-tags">
              <span v-for="tag in prompt.tags" :key="tag" class="tag">{{ tag }}</span>
            </div>
            <div class="card-info">
              <div class="model-info">
                <span v-for="model in prompt.models" :key="model" class="model-tag">{{ model }}</span>
              </div>
              <div class="author-info">
                <div class="author-avatar">{{ prompt.author.charAt(0) }}</div>
                <span>{{ prompt.author }}</span>
              </div>
            </div>
            <div class="card-stats">
              <div class="stat">
                <i class="fas fa-thumbs-up"></i>
                <span>{{ prompt.likes }} 赞同</span>
              </div>
              <div class="stat">
                <i class="fas fa-download"></i>
                <span>{{ prompt.uses }} 使用</span>
              </div>
              <div class="stat">
                <i class="fas fa-comments"></i>
                <span>{{ prompt.comments }} 评论</span>
              </div>
            </div>
            <div class="card-actions" @click.stop>
              <button class="btn btn-primary" @click="usePrompt(prompt.id)">立即使用</button>
              <button 
                class="btn btn-secondary like-btn" 
                :class="{ liked: prompt.isLiked }"
                @click="toggleLike(prompt.id)"
              >
                <i class="fas fa-heart"></i>
                <span class="like-count">{{ prompt.likes }}</span>
              </button>
              <button 
                class="btn btn-secondary collect-btn" 
                :class="{ bookmarked: prompt.isBookmarked }"
                @click="toggleBookmark(prompt.id)"
              >
                <i class="fas fa-bookmark"></i>
              </button>
              <button class="btn btn-secondary copy-to-space-btn" @click="copyToSpace(prompt.id)">
                <i class="fas fa-copy"></i>
              </button>
              <button class="btn btn-secondary share-btn" @click="sharePrompt(prompt.id)">
                <i class="fas fa-share"></i>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div class="load-more" v-if="hasMore">
          <button class="btn btn-primary load-more-btn" @click="loadMore" :disabled="loading">
            <span v-if="loading">加载中...</span>
            <span v-else>加载更多</span>
          </button>
        </div>
      </div>
    </section>
  </Layout>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'Home',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    
    const heroSearchQuery = ref('')
    const selectedCategory = ref('all')
    const viewMode = ref('grid')
    const loading = ref(false)
    const hasMore = ref(true)
    
    const filters = reactive({
      timeFilter: 'all',
      sortBy: 'recommended'
    })
    
    const stats = reactive({
      prompts: '10,000+',
      users: '5,000+',
      categories: '50+',
      satisfaction: '98%'
    })
    
    const categories = ref([
      { id: 'all', name: '全部', count: null },
      { id: 'writing', name: '文案写作', count: 165 },
      { id: 'coding', name: '代码编程', count: 89 },
      { id: 'analysis', name: '数据分析', count: 67 },
      { id: 'design', name: '创意设计', count: 123 },
      { id: 'business', name: '商业策略', count: 78 },
      { id: 'education', name: '教育培训', count: 45 },
      { id: 'translation', name: '翻译润色', count: 34 }
    ])
    
    const featuredPrompts = ref([
      {
        id: 1,
        title: '完美文案生成器',
        badge: '精选',
        description: '专为营销文案量身定制的Prompt，能够生成吸引人的广告文案、产品描述和社交媒体内容。支持多种风格和语感选择，让您的文案更有感染力。',
        tags: ['文案写作', '营销', '商业沟通'],
        models: ['GPT-4', 'Claude'],
        author: '刘小明',
        likes: 2300,
        uses: 989,
        comments: 67,
        isLiked: false,
        isBookmarked: false,
        category: 'writing'
      },
      {
        id: 4,
        title: '智能产品分析师',
        badge: '精选',
        description: '深度分析产品功能、用户体验和市场表现，提供专业的产品优化建议和竞品分析报告。',
        tags: ['产品分析', '用户体验', '市场研究'],
        models: ['GPT-4', 'Claude'],
        author: '张产品',
        likes: 1850,
        uses: 723,
        comments: 45,
        isLiked: false,
        isBookmarked: false,
        category: 'business'
      },
      {
        id: 5,
        title: '创意设计助手',
        badge: '精选',
        description: '激发设计灵感，提供创意方案和设计建议。适用于UI/UX设计、平面设计和品牌设计等多个领域。',
        tags: ['UI设计', '创意设计', '品牌设计'],
        models: ['GPT-4', 'Midjourney'],
        author: '李设计',
        likes: 1650,
        uses: 542,
        comments: 38,
        isLiked: true,
        isBookmarked: false,
        category: 'design'
      }
    ])
    
    const prompts = ref([
      {
        id: 2,
        title: '代码助手专家',
        badge: '热门',
        description: '智能代码分析和优化助手，帮助开发者进行代码审查、或编写更优雅的代码。',
        tags: ['代码编程', '算法', '代码审查'],
        models: ['GPT-4', 'GPT-3.5'],
        author: '李开发',
        likes: 1800,
        uses: 754,
        comments: 45,
        isLiked: false,
        isBookmarked: false,
        category: 'coding'
      },
      {
        id: 3,
        title: '数据分析师助手',
        badge: '推荐',
        description: '专业数据分析工具，帮助您快速洞察数据背后的故事和趋势。',
        tags: ['数据分析', '商业智能', '报告'],
        models: ['GPT-4', 'Claude'],
        author: '王数据',
        likes: 1200,
        uses: 567,
        comments: 23,
        isLiked: false,
        isBookmarked: false,
        category: 'analysis'
      },
      {
        id: 6,
        title: '翻译润色专家',
        badge: '推荐',
        description: '精准翻译和文本润色，支持多语言互译，提升文本质量和可读性。',
        tags: ['翻译', '润色', '多语言'],
        models: ['GPT-4', 'Claude'],
        author: '陈翻译',
        likes: 980,
        uses: 432,
        comments: 28,
        isLiked: false,
        isBookmarked: true,
        category: 'translation'
      },
      {
        id: 7,
        title: '教学课件制作',
        badge: '热门',
        description: '帮助教师制作生动有趣的课件内容，提供教学设计和互动方案。',
        tags: ['教育', '课件制作', '教学设计'],
        models: ['GPT-4', 'Claude'],
        author: '王老师',
        likes: 1150,
        uses: 645,
        comments: 52,
        isLiked: false,
        isBookmarked: false,
        category: 'education'
      },
      {
        id: 8,
        title: '商业策略分析',
        badge: '推荐',
        description: '深度分析商业模式和市场机会，提供战略建议和竞争分析。',
        tags: ['商业策略', '市场分析', '竞争研究'],
        models: ['GPT-4', 'Claude'],
        author: '赵商业',
        likes: 890,
        uses: 378,
        comments: 19,
        isLiked: false,
        isBookmarked: false,
        category: 'business'
      },
      {
        id: 9,
        title: '前端开发助手',
        badge: '热门',
        description: '前端开发最佳实践和技术选型建议，帮助构建现代化的Web应用。',
        tags: ['前端开发', 'React', 'Vue.js'],
        models: ['GPT-4', 'Claude'],
        author: '孙前端',
        likes: 1680,
        uses: 821,
        comments: 76,
        isLiked: true,
        isBookmarked: false,
        category: 'coding'
      },
      {
        id: 10,
        title: '创意写作导师',
        badge: '推荐',
        description: '激发创作灵感，提供写作技巧和故事构思，适合小说、剧本等创意写作。',
        tags: ['创意写作', '故事构思', '文学创作'],
        models: ['GPT-4', 'Claude'],
        author: '林作家',
        likes: 1320,
        uses: 596,
        comments: 41,
        isLiked: false,
        isBookmarked: false,
        category: 'writing'
      }
    ])
    
    const filteredPrompts = computed(() => {
      let filtered = [...prompts.value]
      
      // 分类过滤
      if (selectedCategory.value !== 'all') {
        filtered = filtered.filter(prompt => prompt.category === selectedCategory.value)
      }
      
      // 排序
      switch (filters.sortBy) {
        case 'latest':
          filtered.sort((a, b) => b.id - a.id)
          break
        case 'popular':
          filtered.sort((a, b) => b.uses - a.uses)
          break
        default:
          filtered.sort((a, b) => b.likes - a.likes)
      }
      
      return filtered
    })
    
    const performHeroSearch = () => {
      if (heroSearchQuery.value.trim()) {
        router.push(`/search?q=${encodeURIComponent(heroSearchQuery.value)}`)
      }
    }
    
    const scrollToPopular = () => {
      const element = document.getElementById('popular-section')
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    
    const goToPublish = () => {
      router.push('/prompt-edit')
    }
    
    const selectCategory = (categoryId) => {
      selectedCategory.value = categoryId
    }
    
    const setViewMode = (mode) => {
      viewMode.value = mode
    }
    
    const viewPrompt = (id) => {
      router.push(`/prompt/${id}`)
    }
    
    const usePrompt = (id) => {
      toastStore.success('Prompt已复制到剪贴板')
    }
    
    const toggleLike = (id) => {
      const prompt = [...featuredPrompts.value, ...prompts.value].find(p => p.id === id)
      if (prompt) {
        prompt.isLiked = !prompt.isLiked
        prompt.likes += prompt.isLiked ? 1 : -1
        toastStore.success(prompt.isLiked ? '点赞成功' : '取消点赞')
      }
    }
    
    const toggleBookmark = (id) => {
      const prompt = [...featuredPrompts.value, ...prompts.value].find(p => p.id === id)
      if (prompt) {
        prompt.isBookmarked = !prompt.isBookmarked
        toastStore.success(prompt.isBookmarked ? '收藏成功' : '取消收藏')
      }
    }
    
    const copyToSpace = (id) => {
      toastStore.success('已复制到个人空间')
    }
    
    const sharePrompt = (id) => {
      toastStore.success('分享链接已复制到剪贴板')
    }
    
    const loadMore = () => {
      loading.value = true
      setTimeout(() => {
        // 模拟加载更多数据
        loading.value = false
        hasMore.value = false
      }, 1000)
    }
    
    onMounted(() => {
      // 初始化数据
    })
    
    return {
      heroSearchQuery,
      selectedCategory,
      viewMode,
      loading,
      hasMore,
      filters,
      stats,
      categories,
      featuredPrompts,
      prompts,
      filteredPrompts,
      performHeroSearch,
      scrollToPopular,
      goToPublish,
      selectCategory,
      setViewMode,
      viewPrompt,
      usePrompt,
      toggleLike,
      toggleBookmark,
      copyToSpace,
      sharePrompt,
      loadMore
    }
  }
}
</script>