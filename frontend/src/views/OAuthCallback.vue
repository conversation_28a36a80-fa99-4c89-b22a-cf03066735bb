<template>
  <div class="oauth-callback">
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在处理登录...</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useToastStore } from '@/stores/toast'
import { API_CONFIG, ApiClient } from '@/utils/api'

export default {
  name: 'OAuthCallback',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const userStore = useUserStore()
    const toastStore = useToastStore()
    
    onMounted(async () => {
      const { provider } = route.params
      const { token, success, error } = route.query
      
      if (error) {
        toastStore.showToast('OAuth认证失败: ' + error, 'error')
        router.push('/login')
        return
      }
      
      if (success === 'true' && token) {
        // 直接使用服务端返回的token
        localStorage.setItem('token', token)
        
        // 获取用户信息
        try {
          const response = await fetch(`http://localhost:8000/api/auth/user`, {
            headers: {
              'Authorization': 'Bearer ' + token
            }
          });
          const data = await response.json();
          
          if (data.code === 200) {
            // 设置用户信息
            const userData = {
              token: token,
              username: data.data.username,
              nickname: data.data.nickname,
              email: data.data.email,
              avatar: data.data.avatar
            };
            
            userStore.setUser(userData);
            toastStore.showToast('登录成功！', 'success');
            
            // 检查是否有保存的重定向地址
            const redirectPath = localStorage.getItem('redirectAfterLogin');
            if (redirectPath) {
              localStorage.removeItem('redirectAfterLogin');
              router.push(redirectPath);
            } else {
              router.push('/');
            }
          } else {
            toastStore.showToast('获取用户信息失败: ' + data.message, 'error');
            router.push('/login');
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
          toastStore.showToast('获取用户信息失败', 'error');
          router.push('/login');
        }
      } else {
        toastStore.showToast('OAuth认证失败', 'error');
        router.push('/login');
      }
    })
    
    return {}
  }
}
</script>

<style scoped>
.oauth-callback {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.loading-container {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #666;
  font-size: 16px;
  margin: 0;
}
</style>