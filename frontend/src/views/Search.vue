<template>
  <Layout>
    <div class="search-page">
      <div class="container">
        <!-- 搜索头部 -->
        <div class="search-header">
          <div class="search-box-large">
            <input 
              type="text" 
              placeholder="搜索Prompt、作者或关键词..."
              v-model="searchQuery"
              @keypress.enter="performSearch"
              ref="searchInput"
            >
            <button class="search-btn" @click="performSearch">
              <i class="fas fa-search"></i>
            </button>
          </div>
          
          <div class="search-info">
            <p v-if="searchQuery">
              为"<strong>{{ searchQuery }}</strong>"找到 {{ searchResults.length }} 个结果
            </p>
          </div>
        </div>
        
        <!-- 筛选和排序 -->
        <div class="filter-section">
          <div class="filter-left">
            <div class="filter-tabs">
              <button 
                v-for="tab in filterTabs" 
                :key="tab.id"
                class="tab-btn"
                :class="{ active: activeFilter === tab.id }"
                @click="setActiveFilter(tab.id)"
              >
                {{ tab.name }}
                <span v-if="tab.count" class="count">{{ tab.count }}</span>
              </button>
            </div>
          </div>
          
          <div class="filter-right">
            <select v-model="sortBy" @change="sortResults">
              <option value="relevance">相关性</option>
              <option value="latest">最新发布</option>
              <option value="popular">最受欢迎</option>
              <option value="rating">评分最高</option>
            </select>
            
            <div class="view-toggle">
              <button 
                class="view-btn"
                :class="{ active: viewMode === 'grid' }"
                @click="setViewMode('grid')"
              >
                <i class="fas fa-th"></i>
              </button>
              <button 
                class="view-btn"
                :class="{ active: viewMode === 'list' }"
                @click="setViewMode('list')"
              >
                <i class="fas fa-list"></i>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 搜索结果 -->
        <div class="search-results">
          <div v-if="loading" class="loading">
            <div class="spinner"></div>
            <p>搜索中...</p>
          </div>
          
          <div v-else-if="searchResults.length > 0" class="results-grid" :class="{ 'results-list': viewMode === 'list' }">
            <div 
              v-for="result in searchResults" 
              :key="result.id"
              class="result-card"
              @click="viewResult(result)"
            >
              <div class="result-type">
                <span class="type-badge" :class="result.type">{{ getTypeLabel(result.type) }}</span>
              </div>
              
              <div class="result-content">
                <h3 class="result-title">
                  <span v-html="highlightText(result.title)"></span>
                </h3>
                
                <p class="result-description">
                  <span v-html="highlightText(result.description)"></span>
                </p>
                
                <div class="result-tags">
                  <span v-for="tag in result.tags" :key="tag" class="tag">{{ tag }}</span>
                </div>
                
                <div class="result-meta">
                  <div class="meta-left">
                    <span v-if="result.author" class="author">作者：{{ result.author }}</span>
                    <span class="date">{{ formatDate(result.createdAt) }}</span>
                  </div>
                  
                  <div class="meta-right">
                    <span class="stat">
                      <i class="fas fa-thumbs-up"></i>
                      {{ result.likes }}
                    </span>
                    <span class="stat">
                      <i class="fas fa-eye"></i>
                      {{ result.views }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else-if="!loading && searchQuery" class="empty-results">
            <div class="empty-icon">
              <i class="fas fa-search"></i>
            </div>
            <h3>未找到相关结果</h3>
            <p>尝试使用不同的关键词或浏览热门内容</p>
            <button class="btn btn-primary" @click="goToHome">浏览热门</button>
          </div>
        </div>
        
        <!-- 分页 -->
        <div v-if="searchResults.length > 0" class="pagination">
          <button 
            class="pagination-btn"
            :disabled="currentPage === 1"
            @click="goToPage(currentPage - 1)"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <button 
            v-for="page in visiblePages"
            :key="page"
            class="pagination-btn"
            :class="{ active: page === currentPage }"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
          
          <button 
            class="pagination-btn"
            :disabled="currentPage === totalPages"
            @click="goToPage(currentPage + 1)"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Layout from '../components/Layout.vue'

export default {
  name: 'Search',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    const searchQuery = ref('')
    const activeFilter = ref('all')
    const sortBy = ref('relevance')
    const viewMode = ref('grid')
    const loading = ref(false)
    const currentPage = ref(1)
    const totalPages = ref(5)
    const searchInput = ref(null)
    
    const filterTabs = ref([
      { id: 'all', name: '全部', count: 45 },
      { id: 'prompts', name: 'Prompt', count: 32 },
      { id: 'tools', name: '工具', count: 8 },
      { id: 'users', name: '用户', count: 5 }
    ])
    
    const allResults = ref([
      {
        id: 1,
        type: 'prompt',
        title: '完美文案生成器',
        description: '专为营销文案量身定制的Prompt，能够生成吸引人的广告文案、产品描述和社交媒体内容',
        tags: ['文案写作', '营销', '商业沟通'],
        author: '刘小明',
        likes: 2300,
        views: 12500,
        createdAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        type: 'tool',
        title: 'ChatGPT',
        description: '地表最强AI聊天机器人，支持多种对话场景和任务',
        tags: ['AI助手', '聊天机器人', '文本生成'],
        author: 'OpenAI',
        likes: 15000,
        views: 500000,
        createdAt: '2024-01-10T08:20:00Z'
      },
      {
        id: 3,
        type: 'prompt',
        title: '代码审查助手',
        description: '帮助开发者进行代码审查和优化建议的专业Prompt',
        tags: ['代码编程', '审查', '优化'],
        author: '李开发',
        likes: 890,
        views: 3400,
        createdAt: '2024-01-12T16:45:00Z'
      }
    ])
    
    const searchResults = computed(() => {
      let filtered = allResults.value
      
      // 类型过滤
      if (activeFilter.value !== 'all') {
        filtered = filtered.filter(item => item.type === activeFilter.value)
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(item => 
          item.title.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }
      
      // 排序
      switch (sortBy.value) {
        case 'latest':
          filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          break
        case 'popular':
          filtered.sort((a, b) => b.views - a.views)
          break
        case 'rating':
          filtered.sort((a, b) => b.likes - a.likes)
          break
        default:
          // 相关性排序（简单实现）
          break
      }
      
      return filtered
    })
    
    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    })
    
    const performSearch = () => {
      if (searchQuery.value.trim()) {
        loading.value = true
        // 模拟搜索延迟
        setTimeout(() => {
          loading.value = false
          currentPage.value = 1
        }, 500)
      }
    }
    
    const setActiveFilter = (filter) => {
      activeFilter.value = filter
      currentPage.value = 1
    }
    
    const sortResults = () => {
      currentPage.value = 1
    }
    
    const setViewMode = (mode) => {
      viewMode.value = mode
    }
    
    const viewResult = (result) => {
      if (result.type === 'prompt') {
        router.push(`/prompt/${result.id}`)
      } else if (result.type === 'tool') {
        router.push(`/tool/${result.id}`)
      }
    }
    
    const getTypeLabel = (type) => {
      const labels = {
        prompt: 'Prompt',
        tool: '工具',
        user: '用户'
      }
      return labels[type] || type
    }
    
    const highlightText = (text) => {
      if (!searchQuery.value) return text
      
      const regex = new RegExp(`(${searchQuery.value})`, 'gi')
      return text.replace(regex, '<mark>$1</mark>')
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }
    
    const goToHome = () => {
      router.push('/')
    }
    
    onMounted(() => {
      // 从URL获取搜索参数
      const query = route.query.q
      if (query) {
        searchQuery.value = query
        nextTick(() => {
          performSearch()
        })
      }
      
      // 聚焦搜索框
      if (searchInput.value) {
        searchInput.value.focus()
      }
    })
    
    return {
      searchQuery,
      activeFilter,
      sortBy,
      viewMode,
      loading,
      currentPage,
      totalPages,
      searchInput,
      filterTabs,
      searchResults,
      visiblePages,
      performSearch,
      setActiveFilter,
      sortResults,
      setViewMode,
      viewResult,
      getTypeLabel,
      highlightText,
      formatDate,
      goToPage,
      goToHome
    }
  }
}
</script>

<style scoped>
.search-page {
  padding: 20px 0;
}

.search-header {
  margin-bottom: 30px;
}

.search-box-large {
  display: flex;
  max-width: 800px;
  margin: 0 auto 20px;
  background: white;
  border-radius: 12px;
  padding: 5px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-box-large input {
  flex: 1;
  border: none;
  padding: 15px 20px;
  font-size: 16px;
  border-radius: 8px;
  outline: none;
  color: #333;
}

.search-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.search-btn:hover {
  background: #3730a3;
}

.search-info {
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

.search-info strong {
  color: #4f46e5;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.filter-tabs {
  display: flex;
  gap: 10px;
}

.tab-btn {
  background: #f3f4f6;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab-btn.active {
  background: #4f46e5;
  color: white;
}

.count {
  background: rgba(255, 255, 255, 0.3);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 12px;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-right select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 4px;
}

.view-btn {
  background: none;
  border: none;
  padding: 6px 10px;
  cursor: pointer;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-results {
  margin-bottom: 40px;
}

.loading {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.spinner {
  border: 3px solid #f3f4f6;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.results-list {
  grid-template-columns: 1fr;
}

.result-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.result-type {
  margin-bottom: 15px;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.type-badge.prompt {
  background: #dbeafe;
  color: #1e40af;
}

.type-badge.tool {
  background: #dcfce7;
  color: #166534;
}

.type-badge.user {
  background: #fef3c7;
  color: #92400e;
}

.result-content {
  flex: 1;
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

.result-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
}

.result-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.result-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
}

.meta-left {
  display: flex;
  gap: 15px;
}

.meta-right {
  display: flex;
  gap: 15px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.empty-results {
  text-align: center;
  padding: 80px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.3;
}

.empty-results h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #374151;
}

.empty-results p {
  font-size: 16px;
  margin-bottom: 20px;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 40px;
}

.pagination-btn {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.pagination-btn:hover {
  background: #f3f4f6;
}

.pagination-btn.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

mark {
  background: #fef3c7;
  color: #92400e;
  padding: 2px 4px;
  border-radius: 3px;
}

@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    gap: 15px;
  }
  
  .filter-tabs {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .search-box-large {
    margin: 0 10px 20px;
  }
}
</style>