<template>
  <Layout>
    <div class="profile-page">
      <div class="container">
        <div class="profile-header">
          <div class="profile-avatar">
            <img v-if="userProfile.avatar" :src="userProfile.avatar" :alt="userProfile.name">
            <i v-else class="fas fa-user"></i>
          </div>
          <div class="profile-info">
            <h1>{{ userProfile.name }}</h1>
            <p class="profile-title">{{ userProfile.title }}</p>
            <p class="profile-bio">{{ userProfile.bio }}</p>
            <div class="profile-stats">
              <div class="stat">
                <span class="stat-number">{{ userProfile.stats.prompts }}</span>
                <span class="stat-label">Prompt</span>
              </div>
              <div class="stat">
                <span class="stat-number">{{ userProfile.stats.followers }}</span>
                <span class="stat-label">关注者</span>
              </div>
              <div class="stat">
                <span class="stat-number">{{ userProfile.stats.following }}</span>
                <span class="stat-label">关注</span>
              </div>
              <div class="stat">
                <span class="stat-number">{{ userProfile.stats.likes }}</span>
                <span class="stat-label">获赞</span>
              </div>
            </div>
          </div>
          <div class="profile-actions">
            <button class="btn btn-primary" @click="editProfile">
              <i class="fas fa-edit"></i>
              编辑资料
            </button>
            <button class="btn btn-outline" @click="shareProfile">
              <i class="fas fa-share"></i>
              分享
            </button>
          </div>
        </div>
        
        <div class="profile-content">
          <div class="content-tabs">
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'overview' }"
              @click="setActiveTab('overview')"
            >
              概览
            </button>
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'prompts' }"
              @click="setActiveTab('prompts')"
            >
              我的Prompt
            </button>
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'activity' }"
              @click="setActiveTab('activity')"
            >
              动态
            </button>
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'settings' }"
              @click="setActiveTab('settings')"
            >
              设置
            </button>
          </div>
          
          <div class="tab-content">
            <!-- 概览 -->
            <div v-if="activeTab === 'overview'" class="overview-tab">
              <div class="overview-grid">
                <div class="overview-card">
                  <h3>最近活动</h3>
                  <div class="activity-list">
                    <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                      <div class="activity-icon">
                        <i :class="getActivityIcon(activity.type)"></i>
                      </div>
                      <div class="activity-content">
                        <div class="activity-text">{{ activity.text }}</div>
                        <div class="activity-time">{{ formatDate(activity.time) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="overview-card">
                  <h3>成就徽章</h3>
                  <div class="badges-grid">
                    <div v-for="badge in badges" :key="badge.id" class="badge-item">
                      <div class="badge-icon">
                        <i :class="badge.icon"></i>
                      </div>
                      <div class="badge-info">
                        <div class="badge-name">{{ badge.name }}</div>
                        <div class="badge-description">{{ badge.description }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 我的Prompt -->
            <div v-if="activeTab === 'prompts'" class="prompts-tab">
              <div class="prompts-header">
                <div class="search-filter">
                  <input 
                    type="text" 
                    placeholder="搜索我的Prompt..."
                    v-model="searchQuery"
                  >
                  <select v-model="filterCategory">
                    <option value="">全部分类</option>
                    <option value="writing">文案写作</option>
                    <option value="coding">代码编程</option>
                    <option value="analysis">数据分析</option>
                  </select>
                </div>
                <div class="view-toggle">
                  <button 
                    class="view-btn"
                    :class="{ active: viewMode === 'grid' }"
                    @click="setViewMode('grid')"
                  >
                    <i class="fas fa-th"></i>
                  </button>
                  <button 
                    class="view-btn"
                    :class="{ active: viewMode === 'list' }"
                    @click="setViewMode('list')"
                  >
                    <i class="fas fa-list"></i>
                  </button>
                </div>
              </div>
              
              <div class="prompts-grid" :class="{ 'prompts-list': viewMode === 'list' }">
                <div v-for="prompt in filteredPrompts" :key="prompt.id" class="prompt-card">
                  <div class="card-header">
                    <h3>{{ prompt.title }}</h3>
                    <span class="status-badge" :class="prompt.status">{{ getStatusText(prompt.status) }}</span>
                  </div>
                  <p class="card-description">{{ prompt.description }}</p>
                  <div class="card-tags">
                    <span v-for="tag in prompt.tags" :key="tag" class="tag">{{ tag }}</span>
                  </div>
                  <div class="card-stats">
                    <div class="stat">
                      <i class="fas fa-eye"></i>
                      <span>{{ prompt.views }}</span>
                    </div>
                    <div class="stat">
                      <i class="fas fa-heart"></i>
                      <span>{{ prompt.likes }}</span>
                    </div>
                    <div class="stat">
                      <i class="fas fa-comments"></i>
                      <span>{{ prompt.comments }}</span>
                    </div>
                  </div>
                  <div class="card-actions">
                    <button class="btn btn-primary" @click="viewPrompt(prompt.id)">查看</button>
                    <button class="btn btn-outline" @click="editPrompt(prompt.id)">编辑</button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 动态 -->
            <div v-if="activeTab === 'activity'" class="activity-tab">
              <div class="activity-timeline">
                <div v-for="activity in activities" :key="activity.id" class="timeline-item">
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <div class="timeline-header">
                      <div class="timeline-icon">
                        <i :class="getActivityIcon(activity.type)"></i>
                      </div>
                      <div class="timeline-info">
                        <div class="timeline-text">{{ activity.text }}</div>
                        <div class="timeline-time">{{ formatDate(activity.time) }}</div>
                      </div>
                    </div>
                    <div v-if="activity.details" class="timeline-details">{{ activity.details }}</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 设置 -->
            <div v-if="activeTab === 'settings'" class="settings-tab">
              <div class="settings-section">
                <h3>个人信息</h3>
                <div class="form-group">
                  <label>姓名</label>
                  <input type="text" v-model="settings.name" class="form-input">
                </div>
                <div class="form-group">
                  <label>职位</label>
                  <input type="text" v-model="settings.title" class="form-input">
                </div>
                <div class="form-group">
                  <label>个人简介</label>
                  <textarea v-model="settings.bio" class="form-textarea" rows="4"></textarea>
                </div>
                <div class="form-group">
                  <label>邮箱</label>
                  <input type="email" v-model="settings.email" class="form-input">
                </div>
                <button class="btn btn-primary" @click="saveSettings">保存设置</button>
              </div>
              
              <div class="settings-section">
                <h3>通知设置</h3>
                <div class="notification-item">
                  <label>
                    <input type="checkbox" v-model="settings.notifications.email">
                    邮件通知
                  </label>
                </div>
                <div class="notification-item">
                  <label>
                    <input type="checkbox" v-model="settings.notifications.push">
                    推送通知
                  </label>
                </div>
                <div class="notification-item">
                  <label>
                    <input type="checkbox" v-model="settings.notifications.comments">
                    评论通知
                  </label>
                </div>
                <div class="notification-item">
                  <label>
                    <input type="checkbox" v-model="settings.notifications.likes">
                    点赞通知
                  </label>
                </div>
              </div>
              
              <div class="settings-section">
                <h3>隐私设置</h3>
                <div class="privacy-item">
                  <label>
                    <input type="checkbox" v-model="settings.privacy.publicProfile">
                    公开个人资料
                  </label>
                </div>
                <div class="privacy-item">
                  <label>
                    <input type="checkbox" v-model="settings.privacy.showEmail">
                    显示邮箱地址
                  </label>
                </div>
                <div class="privacy-item">
                  <label>
                    <input type="checkbox" v-model="settings.privacy.showActivity">
                    显示活动状态
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import { useToastStore } from '../stores/toast'

export default {
  name: 'Profile',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    
    const activeTab = ref('overview')
    const searchQuery = ref('')
    const filterCategory = ref('')
    const viewMode = ref('grid')
    
    const userProfile = reactive({
      name: '张三',
      title: '资深产品经理',
      bio: '专注于AI产品设计与用户体验优化，热爱探索新技术在产品中的应用。',
      avatar: null,
      stats: {
        prompts: 24,
        followers: 156,
        following: 89,
        likes: 892
      }
    })
    
    const settings = reactive({
      name: '张三',
      title: '资深产品经理',
      bio: '专注于AI产品设计与用户体验优化，热爱探索新技术在产品中的应用。',
      email: '<EMAIL>',
      notifications: {
        email: true,
        push: true,
        comments: true,
        likes: false
      },
      privacy: {
        publicProfile: true,
        showEmail: false,
        showActivity: true
      }
    })
    
    const recentActivities = ref([
      {
        id: 1,
        type: 'create',
        text: '创建了新的Prompt "产品需求文档模板"',
        time: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        type: 'like',
        text: '点赞了 "用户故事写作指南"',
        time: '2024-01-14T15:20:00Z'
      },
      {
        id: 3,
        type: 'comment',
        text: '评论了 "敏捷开发流程优化"',
        time: '2024-01-13T09:15:00Z'
      }
    ])
    
    const badges = ref([
      {
        id: 1,
        name: '创作新手',
        description: '发布第一个Prompt',
        icon: 'fas fa-seedling'
      },
      {
        id: 2,
        name: '受欢迎的创作者',
        description: '获得100个点赞',
        icon: 'fas fa-heart'
      },
      {
        id: 3,
        name: '活跃贡献者',
        description: '连续7天活跃',
        icon: 'fas fa-fire'
      }
    ])
    
    const userPrompts = ref([
      {
        id: 1,
        title: '产品需求文档模板',
        description: '标准化的产品需求文档写作模板',
        tags: ['产品', '需求', '文档'],
        status: 'published',
        views: 234,
        likes: 45,
        comments: 12,
        category: 'writing'
      },
      {
        id: 2,
        title: '用户故事写作指南',
        description: '敏捷开发中用户故事的写作规范',
        tags: ['敏捷', '用户故事', '开发'],
        status: 'draft',
        views: 0,
        likes: 0,
        comments: 0,
        category: 'writing'
      }
    ])
    
    const activities = ref([
      {
        id: 1,
        type: 'create',
        text: '创建了新的Prompt',
        details: '产品需求文档模板',
        time: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        type: 'edit',
        text: '编辑了Prompt',
        details: '用户故事写作指南',
        time: '2024-01-14T15:20:00Z'
      },
      {
        id: 3,
        type: 'like',
        text: '点赞了Prompt',
        details: '敏捷开发流程优化',
        time: '2024-01-13T09:15:00Z'
      }
    ])
    
    const filteredPrompts = computed(() => {
      return userPrompts.value.filter(prompt => {
        const matchesSearch = !searchQuery.value || 
          prompt.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          prompt.description.toLowerCase().includes(searchQuery.value.toLowerCase())
        
        const matchesCategory = !filterCategory.value || prompt.category === filterCategory.value
        
        return matchesSearch && matchesCategory
      })
    })
    
    const setActiveTab = (tab) => {
      activeTab.value = tab
    }
    
    const setViewMode = (mode) => {
      viewMode.value = mode
    }
    
    const editProfile = () => {
      setActiveTab('settings')
    }
    
    const shareProfile = () => {
      const url = window.location.href
      navigator.clipboard.writeText(url)
      toastStore.success('个人资料链接已复制到剪贴板')
    }
    
    const viewPrompt = (id) => {
      router.push(`/prompt/${id}`)
    }
    
    const editPrompt = (id) => {
      router.push(`/prompt-edit/${id}`)
    }
    
    const saveSettings = () => {
      // 更新用户资料
      userProfile.name = settings.name
      userProfile.title = settings.title
      userProfile.bio = settings.bio
      
      toastStore.success('设置已保存')
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        published: '已发布',
        draft: '草稿',
        private: '私有'
      }
      return statusMap[status] || status
    }
    
    const getActivityIcon = (type) => {
      const icons = {
        create: 'fas fa-plus',
        edit: 'fas fa-edit',
        like: 'fas fa-heart',
        comment: 'fas fa-comment',
        share: 'fas fa-share'
      }
      return icons[type] || 'fas fa-info'
    }
    
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = now - date
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 0) {
        return '今天'
      } else if (diffDays === 1) {
        return '昨天'
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    }
    
    return {
      activeTab,
      searchQuery,
      filterCategory,
      viewMode,
      userProfile,
      settings,
      recentActivities,
      badges,
      userPrompts,
      activities,
      filteredPrompts,
      setActiveTab,
      setViewMode,
      editProfile,
      shareProfile,
      viewPrompt,
      editPrompt,
      saveSettings,
      getStatusText,
      getActivityIcon,
      formatDate
    }
  }
}
</script>

<style scoped>
.profile-page {
  padding: 20px 0;
}

.profile-header {
  display: flex;
  align-items: flex-start;
  gap: 30px;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: #9ca3af;
  overflow: hidden;
  flex-shrink: 0;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
}

.profile-info h1 {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8px;
}

.profile-title {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 12px;
}

.profile-bio {
  color: #374151;
  line-height: 1.6;
  margin-bottom: 20px;
}

.profile-stats {
  display: flex;
  gap: 30px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.profile-actions {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.profile-actions .btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  font-size: 14px;
}

.profile-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.content-tabs {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
  flex: 1;
  padding: 15px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
  background: white;
}

.tab-content {
  padding: 30px;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.overview-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.overview-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 20px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  gap: 12px;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4f46e5;
  font-size: 14px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
}

.activity-time {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.badges-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.badge-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.badge-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.badge-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.badge-description {
  font-size: 12px;
  color: #6b7280;
}

.prompts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-filter {
  display: flex;
  gap: 10px;
}

.search-filter input,
.search-filter select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 4px;
}

.view-btn {
  background: none;
  border: none;
  padding: 6px 10px;
  cursor: pointer;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.prompts-list {
  grid-template-columns: 1fr;
}

.prompt-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.published {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.draft {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.private {
  background: #e5e7eb;
  color: #374151;
}

.card-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
}

.card-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.card-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 12px;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.activity-timeline {
  max-width: 600px;
}

.timeline-item {
  position: relative;
  padding-left: 30px;
  margin-bottom: 30px;
}

.timeline-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: 6px;
  top: 24px;
  width: 2px;
  height: calc(100% + 6px);
  background: #e5e7eb;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4f46e5;
}

.timeline-content {
  background: #f8fafc;
  border-radius: 8px;
  padding: 15px;
}

.timeline-header {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.timeline-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.timeline-info {
  flex: 1;
}

.timeline-text {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
}

.timeline-time {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.timeline-details {
  color: #6b7280;
  font-size: 13px;
  padding-left: 36px;
}

.settings-section {
  margin-bottom: 40px;
}

.settings-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-textarea {
  resize: vertical;
  font-family: inherit;
}

.notification-item,
.privacy-item {
  margin-bottom: 15px;
}

.notification-item label,
.privacy-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: normal;
}

.notification-item input[type="checkbox"],
.privacy-item input[type="checkbox"] {
  margin: 0;
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-actions {
    justify-content: center;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .prompts-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-filter {
    flex-direction: column;
    width: 100%;
  }
  
  .prompts-grid {
    grid-template-columns: 1fr;
  }
}
</style>