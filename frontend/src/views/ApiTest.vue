<template>
  <div class="api-test">
    <h1>API测试页面</h1>
    
    <div class="test-section">
      <h2>团队信息测试</h2>
      <button @click="testTeamProfile" :disabled="loading">
        {{ loading ? '加载中...' : '测试团队信息API' }}
      </button>
      <div v-if="teamData" class="result">
        <h3>团队信息：</h3>
        <pre>{{ JSON.stringify(teamData, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>团队成员测试</h2>
      <button @click="testTeamMembers" :disabled="loading">
        {{ loading ? '加载中...' : '测试团队成员API' }}
      </button>
      <div v-if="membersData" class="result">
        <h3>团队成员：</h3>
        <pre>{{ JSON.stringify(membersData, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>团队推荐内容测试</h2>
      <button @click="testTeamRecommendations" :disabled="loading">
        {{ loading ? '加载中...' : '测试团队推荐内容API' }}
      </button>
      <div v-if="recommendationsData" class="result">
        <h3>推荐内容：</h3>
        <pre>{{ JSON.stringify(recommendationsData, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>用户团队列表测试</h2>
      <button @click="testUserTeams" :disabled="loading">
        {{ loading ? '加载中...' : '测试用户团队列表API' }}
      </button>
      <div v-if="userTeamsData" class="result">
        <h3>用户团队列表：</h3>
        <pre>{{ JSON.stringify(userTeamsData, null, 2) }}</pre>
      </div>
    </div>

    <div v-if="error" class="error">
      <h3>错误信息：</h3>
      <pre>{{ error }}</pre>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import teamService from '../services/teamService'
import userService from '../services/userService'

export default {
  name: 'ApiTest',
  setup() {
    const loading = ref(false)
    const error = ref(null)
    const teamData = ref(null)
    const membersData = ref(null)
    const recommendationsData = ref(null)
    const userTeamsData = ref(null)

    const testTeamProfile = async () => {
      loading.value = true
      error.value = null
      try {
        const response = await teamService.getTeamProfile(1)
        teamData.value = response
        console.log('团队信息API响应:', response)
      } catch (err) {
        error.value = `团队信息API错误: ${err.message}`
        console.error('团队信息API错误:', err)
      } finally {
        loading.value = false
      }
    }

    const testTeamMembers = async () => {
      loading.value = true
      error.value = null
      try {
        const response = await teamService.getTeamMembers(1)
        membersData.value = response
        console.log('团队成员API响应:', response)
      } catch (err) {
        error.value = `团队成员API错误: ${err.message}`
        console.error('团队成员API错误:', err)
      } finally {
        loading.value = false
      }
    }

    const testTeamRecommendations = async () => {
      loading.value = true
      error.value = null
      try {
        const response = await teamService.getTeamRecommendations(1)
        recommendationsData.value = response
        console.log('团队推荐内容API响应:', response)
      } catch (err) {
        error.value = `团队推荐内容API错误: ${err.message}`
        console.error('团队推荐内容API错误:', err)
      } finally {
        loading.value = false
      }
    }

    const testUserTeams = async () => {
      loading.value = true
      error.value = null
      try {
        const response = await userService.getUserTeams(1)
        userTeamsData.value = response
        console.log('用户团队列表API响应:', response)
      } catch (err) {
        error.value = `用户团队列表API错误: ${err.message}`
        console.error('用户团队列表API错误:', err)
      } finally {
        loading.value = false
      }
    }

    return {
      loading,
      error,
      teamData,
      membersData,
      recommendationsData,
      userTeamsData,
      testTeamProfile,
      testTeamMembers,
      testTeamRecommendations,
      testUserTeams
    }
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
  color: #374151;
}

button {
  padding: 10px 20px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background: #4338ca;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background: #f9fafb;
  border-radius: 6px;
}

.result h3 {
  margin-top: 0;
  color: #059669;
}

.error {
  margin-top: 20px;
  padding: 15px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
}

.error h3 {
  margin-top: 0;
  color: #dc2626;
}

pre {
  background: white;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
