<template>
  <div class="crawler-test">
    <div class="test-header">
      <h1>爬虫功能测试</h1>
      <p>测试订阅内容爬虫的各项功能</p>
    </div>

    <!-- 快速测试区域 -->
    <div class="quick-test">
      <h2>快速测试</h2>
      <div class="test-buttons">
        <button
          :class="['test-btn', { loading: testLoading.dedao }]"
          @click="testDedaoBooks"
          :disabled="testLoading.dedao"
        >
          <i v-if="testLoading.dedao" class="icon-loading"></i>
          <i v-else class="icon-audio"></i>
          测试得到每天听本书
        </button>

        <button
          :class="['test-btn', { loading: testLoading.custom }]"
          @click="testCustomFeed"
          :disabled="testLoading.custom"
        >
          <i v-if="testLoading.custom" class="icon-loading"></i>
          <i v-else class="icon-test"></i>
          测试自定义Feed
        </button>

        <button
          class="test-btn"
          @click="clearResults"
        >
          <i class="icon-clear"></i>
          清空结果
        </button>
      </div>
    </div>

    <!-- 自定义测试区域 -->
    <div class="custom-test">
      <h2>自定义测试</h2>
      <div class="test-form">
        <div class="form-row">
          <label>Feed ID:</label>
          <input
            v-model="customFeedId"
            type="text"
            placeholder="请输入Folo.is的Feed ID"
            @keyup.enter="testCustomFeed"
          />
        </div>
        <div class="form-row">
          <label>条目数量:</label>
          <input
            v-model.number="customLimit"
            type="number"
            min="1"
            max="20"
            placeholder="默认8"
          />
        </div>
      </div>
    </div>

    <!-- 测试结果显示 -->
    <div v-if="testResults.length > 0" class="test-results">
      <h2>测试结果</h2>
      
      <div
        v-for="(result, index) in testResults"
        :key="index"
        class="result-item"
      >
        <div class="result-header">
          <h3>{{ result.title }}</h3>
          <span :class="['status', result.success ? 'success' : 'error']">
            {{ result.success ? '成功' : '失败' }}
          </span>
        </div>

        <div v-if="result.success" class="result-content">
          <!-- 订阅信息 -->
          <div class="subscription-info">
            <h4>订阅信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>名称:</label>
                <span>{{ result.data.subscription.name }}</span>
              </div>
              <div class="info-item">
                <label>类型:</label>
                <span>{{ result.data.subscription.type }}</span>
              </div>
              <div class="info-item">
                <label>描述:</label>
                <span>{{ result.data.subscription.description }}</span>
              </div>
              <div class="info-item">
                <label>内容数量:</label>
                <span>{{ result.data.contents.length }} 条</span>
              </div>
            </div>
          </div>

          <!-- 内容列表 -->
          <div class="content-list">
            <h4>内容列表 (前5条)</h4>
            <div class="content-items">
              <div
                v-for="content in result.data.contents.slice(0, 5)"
                :key="content.id"
                class="content-item"
                @click="showContentDetail(content)"
              >
                <div class="content-header">
                  <h5>{{ content.title }}</h5>
                  <span class="content-type">{{ getContentTypeLabel(content) }}</span>
                </div>
                <p class="content-summary">{{ content.summary }}</p>
                <div class="content-meta">
                  <span v-if="content.author || content.host">
                    作者: {{ content.author || content.host }}
                  </span>
                  <span v-if="content.publishTime">
                    时间: {{ formatTime(content.publishTime) }}
                  </span>
                  <span v-if="content.duration">
                    时长: {{ formatDuration(content.duration) }}
                  </span>
                  <span v-if="content.readTime">
                    阅读: {{ content.readTime }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="statistics">
            <h4>统计信息</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-label">总内容数:</span>
                <span class="stat-value">{{ result.data.contents.length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均标签数:</span>
                <span class="stat-value">
                  {{ getAverageTagCount(result.data.contents) }}
                </span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最新内容:</span>
                <span class="stat-value">
                  {{ getLatestContentTime(result.data.contents) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="result-error">
          <h4>错误信息</h4>
          <p>{{ result.error }}</p>
        </div>
      </div>
    </div>

    <!-- 内容详情模态框 -->
    <div v-if="selectedContent" class="content-modal" @click="closeContentDetail">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ selectedContent.title }}</h3>
          <button class="close-btn" @click="closeContentDetail">×</button>
        </div>
        
        <div class="modal-body">
          <div class="content-details">
            <div v-if="selectedContent.cover || selectedContent.thumbnail" class="content-media">
              <img :src="selectedContent.cover || selectedContent.thumbnail" :alt="selectedContent.title" />
            </div>
            
            <div class="content-info">
              <p><strong>作者:</strong> {{ selectedContent.author || selectedContent.host || '未知' }}</p>
              <p><strong>发布时间:</strong> {{ formatTime(selectedContent.publishTime) }}</p>
              <p v-if="selectedContent.duration"><strong>时长:</strong> {{ formatDuration(selectedContent.duration) }}</p>
              <p v-if="selectedContent.readTime"><strong>阅读时间:</strong> {{ selectedContent.readTime }}</p>
              <p v-if="selectedContent.tags && selectedContent.tags.length"><strong>标签:</strong> {{ selectedContent.tags.join(', ') }}</p>
            </div>
            
            <div class="content-description">
              <h4>内容描述</h4>
              <div v-html="selectedContent.content || selectedContent.description || selectedContent.summary"></div>
            </div>
            
            <div v-if="selectedContent.chapters && selectedContent.chapters.length" class="content-chapters">
              <h4>章节列表</h4>
              <ul>
                <li v-for="chapter in selectedContent.chapters" :key="chapter.id">
                  <span class="chapter-time">{{ formatDuration(chapter.startTime) }}</span>
                  <span class="chapter-title">{{ chapter.title }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { contentCrawler, PREDEFINED_FEEDS } from '@/utils/crawler.js'

export default {
  name: 'CrawlerTest',
  setup() {
    // 响应式数据
    const testLoading = reactive({
      dedao: false,
      custom: false
    })
    
    const testResults = ref([])
    const customFeedId = ref('66321883746344960')
    const customLimit = ref(8)
    const selectedContent = ref(null)

    // 方法
    const testDedaoBooks = async () => {
      try {
        testLoading.dedao = true
        
        const result = await contentCrawler.fetchFoloFeed(
          PREDEFINED_FEEDS.DEDAO_BOOKS.id,
          PREDEFINED_FEEDS.DEDAO_BOOKS.entriesLimit
        )
        
        testResults.value.unshift({
          title: '得到每天听本书测试',
          success: true,
          data: result,
          timestamp: new Date().toISOString()
        })
        
      } catch (error) {
        console.error('测试失败:', error)
        testResults.value.unshift({
          title: '得到每天听本书测试',
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        })
      } finally {
        testLoading.dedao = false
      }
    }

    const testCustomFeed = async () => {
      if (!customFeedId.value.trim()) {
        alert('请输入Feed ID')
        return
      }

      try {
        testLoading.custom = true
        
        const result = await contentCrawler.fetchFoloFeed(
          customFeedId.value.trim(),
          customLimit.value || 8
        )
        
        testResults.value.unshift({
          title: `自定义Feed测试 (${customFeedId.value})`,
          success: true,
          data: result,
          timestamp: new Date().toISOString()
        })
        
      } catch (error) {
        console.error('测试失败:', error)
        testResults.value.unshift({
          title: `自定义Feed测试 (${customFeedId.value})`,
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        })
      } finally {
        testLoading.custom = false
      }
    }

    const clearResults = () => {
      testResults.value = []
    }

    const showContentDetail = (content) => {
      selectedContent.value = content
    }

    const closeContentDetail = () => {
      selectedContent.value = null
    }

    const getContentTypeLabel = (content) => {
      if (content.duration && content.host) return '音频'
      if (content.duration && content.views !== undefined) return '视频'
      if (content.dimensions || content.photographer) return '图片'
      return '文章'
    }

    const formatTime = (timeString) => {
      const date = new Date(timeString)
      return date.toLocaleString()
    }

    const formatDuration = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }

    const getAverageTagCount = (contents) => {
      if (contents.length === 0) return 0
      const totalTags = contents.reduce((sum, content) => sum + (content.tags?.length || 0), 0)
      return (totalTags / contents.length).toFixed(1)
    }

    const getLatestContentTime = (contents) => {
      if (contents.length === 0) return '无'
      const latest = contents.reduce((latest, content) => {
        const contentTime = new Date(content.publishTime)
        const latestTime = new Date(latest.publishTime)
        return contentTime > latestTime ? content : latest
      })
      return formatTime(latest.publishTime)
    }

    return {
      testLoading,
      testResults,
      customFeedId,
      customLimit,
      selectedContent,
      testDedaoBooks,
      testCustomFeed,
      clearResults,
      showContentDetail,
      closeContentDetail,
      getContentTypeLabel,
      formatTime,
      formatDuration,
      getAverageTagCount,
      getLatestContentTime
    }
  }
}
</script>

<style lang="scss" scoped>
.crawler-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.test-header {
  text-align: center;
  margin-bottom: 3rem;
  
  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }
  
  p {
    font-size: 1.125rem;
    color: #6b7280;
    margin: 0;
  }
}

.quick-test,
.custom-test,
.test-results {
  margin-bottom: 3rem;
  
  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
  }
}

.test-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.test-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: #4f46e5;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:hover:not(:disabled) {
    background: #4338ca;
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.loading {
    pointer-events: none;
  }
}

.test-form {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  
  .form-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    label {
      min-width: 80px;
      font-weight: 500;
      color: #374151;
    }
    
    input {
      flex: 1;
      padding: 0.5rem 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      
      &:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
      }
    }
  }
}

.result-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  margin-bottom: 2rem;
  overflow: hidden;
  
  .result-header {
    padding: 1.5rem;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
    
    .status {
      padding: 0.25rem 0.75rem;
      border-radius: 6px;
      font-size: 0.875rem;
      font-weight: 500;
      
      &.success {
        background: #d1fae5;
        color: #065f46;
      }
      
      &.error {
        background: #fee2e2;
        color: #991b1b;
      }
    }
  }
  
  .result-content {
    padding: 1.5rem;
  }
  
  .result-error {
    padding: 1.5rem;
    
    h4 {
      margin: 0 0 0.5rem 0;
      color: #991b1b;
    }
    
    p {
      margin: 0;
      color: #6b7280;
    }
  }
}

.subscription-info,
.content-list,
.statistics {
  margin-bottom: 2rem;
  
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
  }
}

.info-grid,
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item,
.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  
  label,
  .stat-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
  }
  
  span,
  .stat-value {
    font-weight: 600;
    color: #1f2937;
  }
}

.content-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.content-item {
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #4f46e5;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
  }
  
  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    
    h5 {
      margin: 0;
      font-size: 1rem;
      font-weight: 600;
      color: #1f2937;
      flex: 1;
    }
    
    .content-type {
      background: #f3f4f6;
      color: #6b7280;
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      margin-left: 1rem;
    }
  }
  
  .content-summary {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .content-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: #9ca3af;
    flex-wrap: wrap;
  }
}

// 模态框样式
.content-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  
  .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .modal-header {
      padding: 1.5rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
      }
      
      .close-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 50%;
        background: #f3f4f6;
        color: #6b7280;
        cursor: pointer;
        font-size: 1.25rem;
        
        &:hover {
          background: #e5e7eb;
        }
      }
    }
    
    .modal-body {
      padding: 1.5rem;
      overflow-y: auto;
      
      .content-media {
        margin-bottom: 1rem;
        
        img {
          width: 100%;
          max-height: 200px;
          object-fit: cover;
          border-radius: 8px;
        }
      }
      
      .content-info {
        margin-bottom: 1.5rem;
        
        p {
          margin: 0.5rem 0;
          font-size: 0.875rem;
          color: #374151;
        }
      }
      
      .content-description {
        margin-bottom: 1.5rem;
        
        h4 {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
        }
      }
      
      .content-chapters {
        h4 {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
        }
        
        ul {
          margin: 0;
          padding-left: 1.5rem;
          
          li {
            margin: 0.5rem 0;
            display: flex;
            gap: 1rem;
            
            .chapter-time {
              font-size: 0.875rem;
              color: #6b7280;
              min-width: 60px;
            }
            
            .chapter-title {
              font-size: 0.875rem;
              color: #374151;
            }
          }
        }
      }
    }
  }
}

// 图标
.icon-loading::before { content: "⏳"; }
.icon-audio::before { content: "🎵"; }
.icon-test::before { content: "🔍"; }
.icon-clear::before { content: "🗑️"; }
</style>
