<template>
  <Layout>
    <div class="auth-page">
      <div class="auth-container">
        <div class="auth-card">
          <div class="auth-header">
            <h1>{{ isLogin ? '登录' : '注册' }}</h1>
            <p>{{ isLogin ? '欢迎回来' : '创建您的账户' }}</p>
          </div>
          
          <form @submit.prevent="handleSubmit" class="auth-form">
            <div v-if="!isLogin" class="form-group">
              <label class="form-label">用户名</label>
              <input 
                type="text" 
                class="form-input"
                v-model="form.username"
                required
                placeholder="请输入用户名"
              >
            </div>
            
            <div class="form-group">
              <label class="form-label">邮箱</label>
              <input 
                type="email" 
                class="form-input"
                v-model="form.email"
                required
                placeholder="请输入邮箱地址"
              >
            </div>
            
            <div class="form-group">
              <label class="form-label">密码</label>
              <input 
                type="password" 
                class="form-input"
                v-model="form.password"
                required
                placeholder="请输入密码"
              >
            </div>
            
            <div v-if="!isLogin" class="form-group">
              <label class="form-label">确认密码</label>
              <input 
                type="password" 
                class="form-input"
                v-model="form.confirmPassword"
                required
                placeholder="请再次输入密码"
              >
            </div>
            
            <button type="submit" class="btn btn-primary auth-btn" :disabled="loading">
              <span v-if="loading">{{ isLogin ? '登录中...' : '注册中...' }}</span>
              <span v-else>{{ isLogin ? '登录' : '注册' }}</span>
            </button>
          </form>
          
          <div class="auth-footer">
            <p>
              {{ isLogin ? '还没有账户？' : '已有账户？' }}
              <button @click="toggleMode" class="link-btn">
                {{ isLogin ? '立即注册' : '立即登录' }}
              </button>
            </p>
          </div>
          
          <div class="divider">
            <span>或</span>
          </div>
          
          <div class="social-auth">
            <button class="social-btn github-btn">
              <i class="fab fa-github"></i>
              GitHub登录
            </button>
            <button class="social-btn google-btn">
              <i class="fab fa-google"></i>
              Google登录
            </button>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import { useUserStore } from '../stores/user'
import { useToastStore } from '../stores/toast'

export default {
  name: 'Auth',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const toastStore = useToastStore()
    
    const isLogin = ref(true)
    const loading = ref(false)
    
    const form = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: ''
    })
    
    const toggleMode = () => {
      isLogin.value = !isLogin.value
      // 重置表单
      Object.keys(form).forEach(key => {
        form[key] = ''
      })
    }
    
    const handleSubmit = async () => {
      if (!isLogin.value && form.password !== form.confirmPassword) {
        toastStore.error('密码确认不一致')
        return
      }
      
      loading.value = true
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        if (isLogin.value) {
          // 登录逻辑
          const userData = {
            id: 1,
            name: form.username || 'User',
            email: form.email,
            avatar: null,
            token: 'mock-token-' + Date.now()
          }
          
          userStore.login(userData)
          toastStore.success('登录成功')
          router.push('/')
        } else {
          // 注册逻辑
          toastStore.success('注册成功，请登录')
          isLogin.value = true
          Object.keys(form).forEach(key => {
            form[key] = ''
          })
        }
      } catch (error) {
        toastStore.error('操作失败，请重试')
      } finally {
        loading.value = false
      }
    }
    
    return {
      isLogin,
      loading,
      form,
      toggleMode,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 400px;
}

.auth-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8px;
}

.auth-header p {
  color: #6b7280;
  font-size: 16px;
}

.auth-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.auth-btn {
  width: 100%;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
}

.auth-footer {
  text-align: center;
  margin-bottom: 20px;
}

.auth-footer p {
  color: #6b7280;
  font-size: 14px;
}

.link-btn {
  background: none;
  border: none;
  color: #4f46e5;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
}

.link-btn:hover {
  color: #3730a3;
}

.divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e5e7eb;
}

.divider span {
  background: white;
  padding: 0 15px;
  color: #9ca3af;
  font-size: 14px;
}

.social-auth {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.social-btn {
  width: 100%;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.social-btn:hover {
  background: #f9fafb;
}

.github-btn {
  color: #24292f;
}

.google-btn {
  color: #4285f4;
}

@media (max-width: 480px) {
  .auth-card {
    padding: 30px 20px;
  }
}
</style>