import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { contentCrawler, PREDEFINED_FEEDS } from '@/utils/crawler.js'

export const useSubscriptionStore = defineStore('subscription', () => {
  // 当前选中的订阅类型
  const currentType = ref('article')
  
  // 各类型的订阅数据
  const subscriptions = ref({
    article: [
      {
        id: 1,
        name: '科技前沿',
        description: '最新科技资讯和趋势分析',
        avatar: '/img/tech-avatar.jpg',
        unreadCount: 12,
        lastUpdate: '2024-01-20T10:30:00Z'
      },
      {
        id: 2,
        name: '人工智能',
        description: 'AI技术发展和应用案例',
        avatar: '/img/ai-avatar.jpg',
        unreadCount: 8,
        lastUpdate: '2024-01-20T09:15:00Z'
      },
      {
        id: 3,
        name: '前端开发',
        description: 'Web前端技术和最佳实践',
        avatar: '/img/frontend-avatar.jpg',
        unreadCount: 5,
        lastUpdate: '2024-01-20T08:45:00Z'
      }
    ],
    image: [
      {
        id: 4,
        name: '摄影艺术',
        description: '精美摄影作品分享',
        avatar: '/img/photo-avatar.jpg',
        unreadCount: 25,
        lastUpdate: '2024-01-20T11:00:00Z'
      },
      {
        id: 5,
        name: '设计灵感',
        description: '创意设计和视觉艺术',
        avatar: '/img/design-avatar.jpg',
        unreadCount: 18,
        lastUpdate: '2024-01-20T10:20:00Z'
      }
    ],
    video: [
      {
        id: 6,
        name: '技术教程',
        description: '编程和技术学习视频',
        avatar: '/img/tutorial-avatar.jpg',
        unreadCount: 7,
        lastUpdate: '2024-01-20T12:00:00Z'
      },
      {
        id: 7,
        name: '产品评测',
        description: '最新产品深度评测',
        avatar: '/img/review-avatar.jpg',
        unreadCount: 3,
        lastUpdate: '2024-01-20T11:30:00Z'
      }
    ],
    audio: [
      {
        id: 8,
        name: '技术播客',
        description: '技术大咖深度访谈',
        avatar: '/img/podcast-avatar.jpg',
        unreadCount: 4,
        lastUpdate: '2024-01-20T13:00:00Z'
      },
      {
        id: 9,
        name: '行业洞察',
        description: '行业趋势和商业分析',
        avatar: '/img/business-avatar.jpg',
        unreadCount: 6,
        lastUpdate: '2024-01-20T12:30:00Z'
      }
    ]
  })

  // 当前选中的订阅
  const selectedSubscription = ref(null)
  
  // 当前选中的内容项
  const selectedContent = ref(null)
  
  // 内容数据
  const contents = ref({
    1: { // 科技前沿
      articles: [
        {
          id: 101,
          title: 'ChatGPT-4的突破性进展：多模态AI的新纪元',
          summary: 'OpenAI发布的ChatGPT-4展现了前所未有的多模态能力，能够同时处理文本、图像和音频输入...',
          content: `<h2>ChatGPT-4：多模态AI的革命性突破</h2><p>OpenAI最新发布的ChatGPT-4标志着人工智能发展的一个重要里程碑。这个新版本不仅在文本理解和生成方面有了显著提升，更重要的是它实现了真正的多模态能力。</p><h3>主要特性</h3><ul><li><strong>多模态输入</strong>：支持文本、图像、音频的混合输入</li><li><strong>更强的推理能力</strong>：在复杂问题解决方面表现出色</li><li><strong>更好的上下文理解</strong>：能够处理更长的对话历史</li><li><strong>创意生成</strong>：在创意写作和艺术创作方面有突破</li></ul>`,
          publishTime: '2024-01-20T10:30:00Z',
          author: '张科技',
          readTime: '5分钟',
          tags: ['AI', 'ChatGPT', '多模态'],
          likes: 156,
          comments: 23
        },
        {
          id: 102,
          title: '量子计算的商业化进程：IBM的最新突破',
          summary: 'IBM宣布其量子计算机在特定任务上实现了量子优势，标志着量子计算向商业化迈出重要一步...',
          content: `<h2>量子计算：从实验室走向商业应用</h2><p>IBM最近宣布的量子计算突破为这一前沿技术的商业化应用带来了新的希望。</p><h3>技术突破</h3><p>IBM的最新量子处理器在以下方面取得了重要进展：</p><ul><li>量子比特稳定性显著提升</li><li>错误率降低到实用水平</li><li>计算速度大幅提升</li></ul>`,
          publishTime: '2024-01-20T09:15:00Z',
          author: '李量子',
          readTime: '7分钟',
          tags: ['量子计算', 'IBM', '商业化'],
          likes: 89,
          comments: 12
        }
      ]
    },
    4: { // 摄影艺术
      images: [
        {
          id: 201,
          title: '城市夜景摄影技巧',
          url: '/img/city-night.jpg',
          thumbnail: '/img/city-night-thumb.jpg',
          description: '掌握城市夜景摄影的关键技巧，拍出令人惊艳的都市夜色',
          photographer: '王摄影',
          uploadTime: '2024-01-20T11:00:00Z',
          likes: 234,
          downloads: 45
        },
        {
          id: 202,
          title: '自然风光构图法则',
          url: '/img/landscape.jpg',
          thumbnail: '/img/landscape-thumb.jpg',
          description: '学习自然风光摄影的构图技巧，捕捉大自然的美丽瞬间',
          photographer: '李风光',
          uploadTime: '2024-01-20T10:30:00Z',
          likes: 189,
          downloads: 32
        }
      ]
    },
    6: { // 技术教程
      videos: [
        {
          id: 301,
          title: 'Vue 3 Composition API 深度解析',
          description: '详细讲解Vue 3 Composition API的核心概念和实际应用，帮助开发者快速掌握新特性。',
          url: '/videos/vue3-composition-api.mp4',
          thumbnail: '/img/vue3-thumb.jpg',
          author: '前端大师',
          uploadTime: '2024-01-20T12:00:00Z',
          duration: 1800, // 30分钟
          views: 15420,
          likes: 892,
          comments: 156
        },
        {
          id: 302,
          title: 'React Hooks 最佳实践',
          description: '分享React Hooks在实际项目中的最佳实践和常见陷阱，提升开发效率。',
          url: '/videos/react-hooks.mp4',
          thumbnail: '/img/react-thumb.jpg',
          author: 'React专家',
          uploadTime: '2024-01-20T10:30:00Z',
          duration: 2100, // 35分钟
          views: 12350,
          likes: 743,
          comments: 89
        }
      ]
    },
    8: { // 技术播客
      audios: [
        {
          id: 401,
          title: '技术人的职业规划与成长',
          summary: '资深技术专家分享从初级工程师到技术领导者的成长路径，包括技能提升、团队管理等方面的经验。',
          description: '在这期节目中，我们邀请了多位技术大咖分享他们的职业成长经历。从技术深度到管理广度，从个人贡献者到团队领导者，每一个转变都充满挑战和机遇。我们将深入探讨技术人员在不同职业阶段应该关注的重点，以及如何在快速变化的技术环境中保持竞争力。',
          url: '/audio/career-growth.mp3',
          cover: '/img/career-cover.jpg',
          host: '技术大咖',
          publishTime: '2024-01-20T13:00:00Z',
          duration: 3600, // 60分钟
          plays: 25420,
          likes: 1892,
          comments: 256,
          tags: ['职业规划', '技术成长', '管理'],
          chapters: [
            { id: 1, title: '开场介绍', startTime: 0 },
            { id: 2, title: '初级工程师阶段', startTime: 300 },
            { id: 3, title: '高级工程师进阶', startTime: 900 },
            { id: 4, title: '技术领导力培养', startTime: 1800 },
            { id: 5, title: 'Q&A环节', startTime: 2700 }
          ]
        },
        {
          id: 402,
          title: 'AI时代的编程思维转变',
          summary: '探讨人工智能对编程工作的影响，以及程序员如何适应AI辅助开发的新时代。',
          description: '随着ChatGPT、GitHub Copilot等AI工具的普及，编程工作正在发生深刻变化。本期节目深入探讨AI对软件开发的影响，分析程序员应该如何调整技能结构和工作方式，在AI时代保持竞争力。',
          url: '/audio/ai-programming.mp3',
          cover: '/img/ai-programming-cover.jpg',
          host: 'AI专家',
          publishTime: '2024-01-20T12:30:00Z',
          duration: 2700, // 45分钟
          plays: 18650,
          likes: 1234,
          comments: 189,
          tags: ['人工智能', '编程', '未来趋势'],
          chapters: [
            { id: 1, title: 'AI工具现状', startTime: 0 },
            { id: 2, title: '对编程工作的影响', startTime: 600 },
            { id: 3, title: '技能转型建议', startTime: 1200 },
            { id: 4, title: '未来展望', startTime: 1800 }
          ]
        }
      ]
    }
  })

  // 音频播放状态
  const audioPlayer = ref({
    isPlaying: false,
    currentTrack: null,
    currentTime: 0,
    duration: 0,
    volume: 0.8
  })

  // 计算属性
  const currentSubscriptions = computed(() => {
    return subscriptions.value[currentType.value] || []
  })

  const totalUnreadCount = computed(() => {
    return currentSubscriptions.value.reduce((total, sub) => total + sub.unreadCount, 0)
  })

  // 方法
  const setCurrentType = (type) => {
    currentType.value = type
    selectedSubscription.value = null
    selectedContent.value = null
  }

  const selectSubscription = (subscription) => {
    selectedSubscription.value = subscription
    selectedContent.value = null
  }

  const selectContent = (content) => {
    selectedContent.value = content
  }

  const getContentBySubscription = (subscriptionId) => {
    return contents.value[subscriptionId] || {}
  }

  // 音频播放控制
  const playAudio = (track) => {
    audioPlayer.value.currentTrack = track
    audioPlayer.value.isPlaying = true
  }

  const pauseAudio = () => {
    audioPlayer.value.isPlaying = false
  }

  const stopAudio = () => {
    audioPlayer.value.isPlaying = false
    audioPlayer.value.currentTrack = null
    audioPlayer.value.currentTime = 0
  }

  const setAudioTime = (time) => {
    audioPlayer.value.currentTime = time
  }

  const setAudioVolume = (volume) => {
    audioPlayer.value.volume = volume
  }

  // 爬虫相关方法
  const fetchSubscriptionFromCrawler = async (feedId, type = 'folo') => {
    try {
      let result

      if (type === 'folo') {
        result = await contentCrawler.fetchFoloFeed(feedId, 10)
      }

      if (result) {
        // 将爬取的数据添加到订阅列表
        const subscriptionType = result.subscription.type || 'article'

        if (!subscriptions.value[subscriptionType]) {
          subscriptions.value[subscriptionType] = []
        }

        // 检查是否已存在
        const existingIndex = subscriptions.value[subscriptionType].findIndex(
          sub => sub.id === result.subscription.id
        )

        if (existingIndex >= 0) {
          // 更新现有订阅
          subscriptions.value[subscriptionType][existingIndex] = {
            ...subscriptions.value[subscriptionType][existingIndex],
            ...result.subscription,
            unreadCount: result.contents.length
          }
        } else {
          // 添加新订阅
          subscriptions.value[subscriptionType].push({
            ...result.subscription,
            unreadCount: result.contents.length
          })
        }

        // 添加内容数据
        contents.value[result.subscription.id] = {
          [subscriptionType === 'article' ? 'articles' :
           subscriptionType === 'image' ? 'images' :
           subscriptionType === 'video' ? 'videos' : 'audios']: result.contents
        }

        return result
      }
    } catch (error) {
      console.error('从爬虫获取订阅失败:', error)
      throw error
    }
  }

  const addPredefinedSubscription = async (feedKey) => {
    const feedConfig = PREDEFINED_FEEDS[feedKey]
    if (!feedConfig) {
      throw new Error(`未找到预定义订阅源: ${feedKey}`)
    }

    return await fetchSubscriptionFromCrawler(feedConfig.id, feedConfig.type)
  }

  const refreshSubscriptionContent = async (subscriptionId) => {
    try {
      // 查找订阅配置
      let feedConfig = null
      Object.values(PREDEFINED_FEEDS).forEach(config => {
        if (config.id === subscriptionId) {
          feedConfig = config
        }
      })

      if (feedConfig) {
        const result = await contentCrawler.fetchFoloFeed(feedConfig.id, feedConfig.entriesLimit || 10)

        // 更新内容数据
        const subscriptionType = result.subscription.type || 'article'
        contents.value[subscriptionId] = {
          [subscriptionType === 'article' ? 'articles' :
           subscriptionType === 'image' ? 'images' :
           subscriptionType === 'video' ? 'videos' : 'audios']: result.contents
        }

        // 更新未读数量
        Object.keys(subscriptions.value).forEach(type => {
          const subIndex = subscriptions.value[type].findIndex(sub => sub.id === subscriptionId)
          if (subIndex >= 0) {
            subscriptions.value[type][subIndex].unreadCount = result.contents.length
            subscriptions.value[type][subIndex].lastUpdate = new Date().toISOString()
          }
        })

        return result
      }
    } catch (error) {
      console.error('刷新订阅内容失败:', error)
      throw error
    }
  }

  return {
    // 状态
    currentType,
    subscriptions,
    selectedSubscription,
    selectedContent,
    contents,
    audioPlayer,
    
    // 计算属性
    currentSubscriptions,
    totalUnreadCount,
    
    // 方法
    setCurrentType,
    selectSubscription,
    selectContent,
    getContentBySubscription,
    playAudio,
    pauseAudio,
    stopAudio,
    setAudioTime,
    setAudioVolume,

    // 爬虫方法
    fetchSubscriptionFromCrawler,
    addPredefinedSubscription,
    refreshSubscriptionContent
  }
})
