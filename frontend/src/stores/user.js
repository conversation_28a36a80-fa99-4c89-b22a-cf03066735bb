import { defineStore } from 'pinia'
import { API_CONFIG, ApiClient } from '@/utils/api'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null,
    isAuthenticated: false,
    token: null,
    initialized: false
  }),
  
  getters: {
    isLoggedIn: (state) => state.isAuthenticated,
    currentUser: (state) => state.user,
    isInitialized: (state) => state.initialized
  },
  
  actions: {
    setUser(userData) {
      this.user = userData
      this.isAuthenticated = true
      this.token = userData.token
      this.initialized = true
      localStorage.setItem('token', userData.token)
      localStorage.setItem('user', JSON.stringify(userData))
    },
    
    login(userData) {
      this.setUser(userData)
    },
    
    logout() {
      const token = this.token || localStorage.getItem('token')
      
      // 调用后端注销接口
      if (token) {
        ApiClient.post(API_CONFIG.ENDPOINTS.AUTH.LOGOUT).catch(() => {
          // 忽略注销请求失败，继续清理本地状态
        })
      }
      
      this.user = null
      this.isAuthenticated = false
      this.token = null
      this.initialized = true
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    },
    
    checkAuth() {
      const token = localStorage.getItem('token')
      const user = localStorage.getItem('user')
      
      if (token && user) {
        this.token = token
        this.user = JSON.parse(user)
        this.isAuthenticated = true
        this.initialized = true
        return true
      } else {
        this.initialized = true
        return false
      }
    },
    
    updateUser(userData) {
      this.user = { ...this.user, ...userData }
      localStorage.setItem('user', JSON.stringify(this.user))
    },
    
    async fetchCurrentUser() {
      const token = this.token || localStorage.getItem('token')
      if (!token) {
        this.initialized = true
        return false
      }
      
      try {
        const data = await ApiClient.get(API_CONFIG.ENDPOINTS.AUTH.USER)
        
        if (data.code === 200) {
          this.user = data.data
          this.isAuthenticated = true
          this.initialized = true
          localStorage.setItem('user', JSON.stringify(data.data))
          return true
        } else {
          this.logout()
          return false
        }
      } catch (error) {
        this.logout()
        return false
      }
    },
    
    async initialize() {
      if (this.initialized) return this.isAuthenticated
      
      const hasLocalAuth = this.checkAuth()
      if (hasLocalAuth) {
        // 验证token是否仍然有效
        return await this.fetchCurrentUser()
      }
      
      this.initialized = true
      return false
    }
  }
})