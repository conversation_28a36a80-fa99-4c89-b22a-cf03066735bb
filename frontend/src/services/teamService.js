import api from './api'

/**
 * 团队相关API服务
 */
class TeamService {
  /**
   * 获取团队基础信息和成就
   * @param {number} teamId - 团队ID
   * @returns {Promise} 团队信息
   */
  async getTeamProfile(teamId) {
    return api.get(`/v1/teams/${teamId}`)
  }

  /**
   * 获取团队推荐的内容列表
   * @param {number} teamId - 团队ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 推荐内容列表
   */
  async getTeamRecommendations(teamId, params = {}) {
    return api.get(`/v1/teams/${teamId}/recommendations`, params)
  }

  /**
   * 获取团队成员列表
   * @param {number} teamId - 团队ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 成员列表
   */
  async getTeamMembers(teamId, params = {}) {
    return api.get(`/v1/teams/${teamId}/members`, params)
  }

  /**
   * 创建团队空间
   * @param {Object} teamData - 团队数据
   * @returns {Promise} 创建结果
   */
  async createTeam(teamData) {
    return api.post('/v1/teams', teamData)
  }

  /**
   * 申请加入团队
   * @param {number} teamId - 团队ID
   * @param {Object} applicationData - 申请数据
   * @returns {Promise} 申请结果
   */
  async applyToJoinTeam(teamId, applicationData) {
    return api.post(`/v1/teams/${teamId}/apply`, applicationData)
  }

  /**
   * 推荐内容到团队
   * @param {Object} recommendationData - 推荐数据
   * @returns {Promise} 推荐结果
   */
  async recommendContents(recommendationData) {
    return api.post('/v1/teams/recommend', recommendationData)
  }

  /**
   * 获取团队活动记录
   * @param {number} teamId - 团队ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 活动记录列表
   */
  async getTeamActivities(teamId, params = {}) {
    return api.get(`/v1/teams/${teamId}/activities`, params)
  }

  /**
   * 获取团队贡献者排行
   * @param {number} teamId - 团队ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 贡献者列表
   */
  async getTeamContributors(teamId, params = {}) {
    return api.get(`/v1/teams/${teamId}/contributors`, params)
  }

  /**
   * 获取所有团队列表（包括公开团队）
   * @param {Object} params - 查询参数
   * @returns {Promise} 团队列表
   */
  async getAllTeams(params = {}) {
    return api.get('/v1/teams', params)
  }

  /**
   * 收藏团队
   * @param {number} teamId - 团队ID
   * @returns {Promise} 收藏结果
   */
  async starTeam(teamId) {
    return api.post(`/v1/teams/${teamId}/star`)
  }

  /**
   * 取消收藏团队
   * @param {number} teamId - 团队ID
   * @returns {Promise} 取消收藏结果
   */
  async unstarTeam(teamId) {
    return api.delete(`/v1/teams/${teamId}/star`)
  }
}

export default new TeamService()
