/**
 * 订阅中心 API 服务
 * 提供与后端订阅相关接口的交互方法
 */

import axios from 'axios'

// 创建API实例
const api = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一处理响应和错误
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // 处理未授权，跳转到登录页
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error.response?.data || error.message)
  }
)

/**
 * 订阅管理 API
 */
export const subscriptionApi = {
  /**
   * 获取订阅列表
   * @param {Object} params - 查询参数
   * @param {string} params.type - 订阅类型 (article/image/video/audio)
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @returns {Promise} 订阅列表数据
   */
  getSubscriptions(params = {}) {
    return api.get('/subscriptions', { params })
  },

  /**
   * 添加订阅
   * @param {Object} data - 订阅数据
   * @param {string} data.name - 订阅名称
   * @param {string} data.description - 订阅描述
   * @param {string} data.type - 订阅类型
   * @param {string} data.sourceUrl - 订阅源URL
   * @param {Array} data.tags - 标签数组
   * @returns {Promise} 添加结果
   */
  addSubscription(data) {
    return api.post('/subscriptions', data)
  },

  /**
   * 取消订阅
   * @param {number} subscriptionId - 订阅ID
   * @returns {Promise} 取消结果
   */
  removeSubscription(subscriptionId) {
    return api.delete(`/subscriptions/${subscriptionId}`)
  },

  /**
   * 更新订阅设置
   * @param {number} subscriptionId - 订阅ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 更新结果
   */
  updateSubscription(subscriptionId, data) {
    return api.put(`/subscriptions/${subscriptionId}`, data)
  },

  /**
   * 获取订阅统计信息
   * @returns {Promise} 统计数据
   */
  getSubscriptionStats() {
    return api.get('/subscriptions/stats')
  }
}

/**
 * 内容获取 API
 */
export const contentApi = {
  /**
   * 获取文章列表
   * @param {number} subscriptionId - 订阅ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 文章列表
   */
  getArticles(subscriptionId, params = {}) {
    return api.get(`/subscriptions/${subscriptionId}/articles`, { params })
  },

  /**
   * 获取文章详情
   * @param {number} articleId - 文章ID
   * @returns {Promise} 文章详情
   */
  getArticleDetail(articleId) {
    return api.get(`/articles/${articleId}`)
  },

  /**
   * 获取图片列表
   * @param {number} subscriptionId - 订阅ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 图片列表
   */
  getImages(subscriptionId, params = {}) {
    return api.get(`/subscriptions/${subscriptionId}/images`, { params })
  },

  /**
   * 获取视频列表
   * @param {number} subscriptionId - 订阅ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 视频列表
   */
  getVideos(subscriptionId, params = {}) {
    return api.get(`/subscriptions/${subscriptionId}/videos`, { params })
  },

  /**
   * 获取音频列表
   * @param {number} subscriptionId - 订阅ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 音频列表
   */
  getAudios(subscriptionId, params = {}) {
    return api.get(`/subscriptions/${subscriptionId}/audios`, { params })
  }
}

/**
 * 交互操作 API
 */
export const interactionApi = {
  /**
   * 点赞/取消点赞
   * @param {string} contentType - 内容类型
   * @param {number} contentId - 内容ID
   * @param {string} action - 操作类型 (like/unlike)
   * @returns {Promise} 操作结果
   */
  toggleLike(contentType, contentId, action) {
    return api.post(`/content/${contentType}/${contentId}/like`, { action })
  },

  /**
   * 添加评论
   * @param {string} contentType - 内容类型
   * @param {number} contentId - 内容ID
   * @param {Object} data - 评论数据
   * @returns {Promise} 评论结果
   */
  addComment(contentType, contentId, data) {
    return api.post(`/content/${contentType}/${contentId}/comments`, data)
  },

  /**
   * 收藏/取消收藏
   * @param {string} contentType - 内容类型
   * @param {number} contentId - 内容ID
   * @param {string} action - 操作类型 (bookmark/unbookmark)
   * @returns {Promise} 操作结果
   */
  toggleBookmark(contentType, contentId, action) {
    return api.post(`/content/${contentType}/${contentId}/bookmark`, { action })
  },

  /**
   * 分享内容
   * @param {string} contentType - 内容类型
   * @param {number} contentId - 内容ID
   * @param {Object} data - 分享数据
   * @returns {Promise} 分享结果
   */
  shareContent(contentType, contentId, data) {
    return api.post(`/content/${contentType}/${contentId}/share`, data)
  },

  /**
   * 标记为已读
   * @param {string} contentType - 内容类型
   * @param {number} contentId - 内容ID
   * @returns {Promise} 操作结果
   */
  markAsRead(contentType, contentId) {
    return api.post(`/content/${contentType}/${contentId}/read`)
  },

  /**
   * 批量标记已读
   * @param {number} subscriptionId - 订阅ID
   * @returns {Promise} 操作结果
   */
  markAllAsRead(subscriptionId) {
    return api.post(`/subscriptions/${subscriptionId}/mark-all-read`)
  }
}

/**
 * 搜索 API
 */
export const searchApi = {
  /**
   * 搜索内容
   * @param {Object} params - 搜索参数
   * @param {string} params.q - 搜索关键词
   * @param {string} params.type - 内容类型过滤
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @returns {Promise} 搜索结果
   */
  searchContent(params) {
    return api.get('/search', { params })
  },

  /**
   * 获取搜索建议
   * @param {string} keyword - 关键词
   * @returns {Promise} 搜索建议
   */
  getSearchSuggestions(keyword) {
    return api.get('/search/suggestions', { params: { q: keyword } })
  }
}

/**
 * 推送设置 API
 */
export const notificationApi = {
  /**
   * 获取推送设置
   * @returns {Promise} 推送设置
   */
  getNotificationSettings() {
    return api.get('/notifications/settings')
  },

  /**
   * 更新推送设置
   * @param {Object} settings - 推送设置
   * @returns {Promise} 更新结果
   */
  updateNotificationSettings(settings) {
    return api.put('/notifications/settings', settings)
  },

  /**
   * 获取通知列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 通知列表
   */
  getNotifications(params = {}) {
    return api.get('/notifications', { params })
  },

  /**
   * 标记通知为已读
   * @param {number} notificationId - 通知ID
   * @returns {Promise} 操作结果
   */
  markNotificationAsRead(notificationId) {
    return api.put(`/notifications/${notificationId}/read`)
  }
}

/**
 * 文件上传 API
 */
export const uploadApi = {
  /**
   * 上传文件
   * @param {File} file - 文件对象
   * @param {Object} options - 上传选项
   * @returns {Promise} 上传结果
   */
  uploadFile(file, options = {}) {
    const formData = new FormData()
    formData.append('file', file)
    
    Object.keys(options).forEach(key => {
      formData.append(key, options[key])
    })

    return api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: options.onProgress
    })
  },

  /**
   * 上传头像
   * @param {File} file - 头像文件
   * @returns {Promise} 上传结果
   */
  uploadAvatar(file) {
    return this.uploadFile(file, { type: 'avatar' })
  }
}

/**
 * 用户相关 API
 */
export const userApi = {
  /**
   * 获取用户信息
   * @returns {Promise} 用户信息
   */
  getUserProfile() {
    return api.get('/user/profile')
  },

  /**
   * 更新用户信息
   * @param {Object} data - 用户数据
   * @returns {Promise} 更新结果
   */
  updateUserProfile(data) {
    return api.put('/user/profile', data)
  },

  /**
   * 获取用户偏好设置
   * @returns {Promise} 偏好设置
   */
  getUserPreferences() {
    return api.get('/user/preferences')
  },

  /**
   * 更新用户偏好设置
   * @param {Object} preferences - 偏好设置
   * @returns {Promise} 更新结果
   */
  updateUserPreferences(preferences) {
    return api.put('/user/preferences', preferences)
  }
}

// 导出默认API实例
export default api
