import { ApiClient } from '../utils/api'

// API基础配置
const API_BASE_URL = 'http://localhost:8001/api'

// 创建API客户端实例
class PortalApiClient {
  constructor() {
    this.baseURL = API_BASE_URL
  }

  // 构建完整URL
  buildUrl(endpoint) {
    return `${this.baseURL}${endpoint}`
  }

  // GET请求
  async get(endpoint, params = {}) {
    let url = this.buildUrl(endpoint)

    // 处理查询参数
    if (Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value)
        }
      })
      url += `?${searchParams.toString()}`
    }

    const response = await ApiClient.get(url)

    // 统一处理响应数据
    if (response && response.code === 200) {
      return response.data
    }

    // 如果不是成功响应，抛出错误
    if (response && response.code !== 200) {
      throw new Error(response.message || '请求失败')
    }

    return response
  }

  // POST请求
  async post(endpoint, data = {}) {
    const url = this.buildUrl(endpoint)
    const response = await ApiClient.post(url, data)

    // 统一处理响应数据
    if (response && response.code === 200) {
      return response.data
    }

    // 如果不是成功响应，抛出错误
    if (response && response.code !== 200) {
      throw new Error(response.message || '请求失败')
    }

    return response
  }

  // PUT请求
  async put(endpoint, data = {}) {
    const url = this.buildUrl(endpoint)
    const response = await ApiClient.put(url, data)

    // 统一处理响应数据
    if (response && response.code === 200) {
      return response.data
    }

    // 如果不是成功响应，抛出错误
    if (response && response.code !== 200) {
      throw new Error(response.message || '请求失败')
    }

    return response
  }

  // DELETE请求
  async delete(endpoint) {
    const url = this.buildUrl(endpoint)
    const response = await ApiClient.delete(url)

    // 统一处理响应数据
    if (response && response.code === 200) {
      return response.data
    }

    // 如果不是成功响应，抛出错误
    if (response && response.code !== 200) {
      throw new Error(response.message || '请求失败')
    }

    return response
  }
}

// 导出单例实例
export default new PortalApiClient()
