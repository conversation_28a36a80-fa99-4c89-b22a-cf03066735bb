import api from './api'

/**
 * 用户相关API服务
 */
class UserService {
  /**
   * 获取用户完整信息
   * @param {number} userId - 用户ID
   * @returns {Promise} 用户信息
   */
  async getUserProfile(userId) {
    return api.get(`/v1/users/${userId}/profile`)
  }

  /**
   * 更新用户资料
   * @param {number} userId - 用户ID
   * @param {Object} profileData - 更新数据
   * @returns {Promise} 更新结果
   */
  async updateUserProfile(userId, profileData) {
    return api.put(`/v1/users/${userId}/profile`, profileData)
  }

  /**
   * 获取用户关联的内容列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 内容列表
   */
  async getUserContents(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, params)
  }

  /**
   * 获取用户加入的团队列表
   * @param {number} userId - 用户ID
   * @returns {Promise} 团队列表
   */
  async getUserTeams(userId) {
    return api.get(`/v1/users/${userId}/teams`)
  }

  /**
   * 获取用户学习信息
   * @param {number} userId - 用户ID
   * @returns {Promise} 学习信息
   */
  async getUserLearnings(userId) {
    return api.get(`/v1/users/${userId}/learnings`)
  }

  /**
   * 获取用户创建的知识列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 知识列表
   */
  async getUserKnowledge(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, {
      associationType: 'published',
      ...params
    })
  }

  /**
   * 获取用户收藏的内容列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 收藏列表
   */
  async getUserFavorites(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, {
      associationType: 'bookmarked',
      ...params
    })
  }

  /**
   * 获取用户喜欢的内容列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 喜欢列表
   */
  async getUserLikes(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, {
      associationType: 'liked',
      ...params
    })
  }

  /**
   * 获取推荐给用户的团队列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 推荐团队列表
   */
  async getRecommendedTeams(userId, params = {}) {
    return api.get(`/v1/users/${userId}/recommended-teams`, params)
  }

  /**
   * 获取用户团队活动记录
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 团队活动列表
   */
  async getUserTeamActivities(userId, params = {}) {
    return api.get(`/v1/users/${userId}/team-activities`, params)
  }
}

export default new UserService()
