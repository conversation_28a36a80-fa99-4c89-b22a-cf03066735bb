/**
 * 爬虫使用示例
 * 演示如何使用爬虫获取订阅内容
 */

import { contentCrawler, PREDEFINED_FEEDS, batchFetchFeeds } from './crawler.js'

/**
 * 示例1: 获取得到每天听本书的内容
 */
export async function fetchDedaoBooks() {
  try {
    console.log('开始获取得到每天听本书内容...')
    
    const result = await contentCrawler.fetchFoloFeed(
      PREDEFINED_FEEDS.DEDAO_BOOKS.id,
      PREDEFINED_FEEDS.DEDAO_BOOKS.entriesLimit
    )
    
    console.log('获取成功！')
    console.log('订阅信息:', result.subscription)
    console.log('内容数量:', result.contents.length)
    
    // 打印前3个内容的标题
    result.contents.slice(0, 3).forEach((content, index) => {
      console.log(`${index + 1}. ${content.title}`)
    })
    
    return result
  } catch (error) {
    console.error('获取失败:', error.message)
    throw error
  }
}

/**
 * 示例2: 批量获取多个订阅源
 */
export async function fetchMultipleFeeds() {
  const feeds = [
    {
      id: '66321883746344960',
      name: '得到每天听本书',
      type: 'folo',
      entriesLimit: 5
    }
    // 可以添加更多订阅源
  ]
  
  try {
    console.log('开始批量获取订阅内容...')
    const results = await batchFetchFeeds(feeds)
    
    console.log(`成功获取 ${results.length} 个订阅源的内容`)
    
    results.forEach((result, index) => {
      console.log(`\n订阅源 ${index + 1}: ${result.subscription.name}`)
      console.log(`内容数量: ${result.contents.length}`)
      console.log(`内容类型: ${result.subscription.type}`)
    })
    
    return results
  } catch (error) {
    console.error('批量获取失败:', error.message)
    throw error
  }
}

/**
 * 示例3: 获取特定类型的内容
 */
export async function fetchContentByType(feedId, contentType) {
  try {
    const result = await contentCrawler.fetchFoloFeed(feedId, 10)
    
    // 根据内容类型过滤
    const filteredContents = result.contents.filter(content => {
      switch (contentType) {
        case 'audio':
          return content.url && (content.url.includes('.mp3') || content.duration)
        case 'video':
          return content.url && (content.url.includes('.mp4') || content.views !== undefined)
        case 'image':
          return content.dimensions || content.photographer
        case 'article':
        default:
          return content.readTime || (!content.duration && !content.views && !content.dimensions)
      }
    })
    
    console.log(`获取到 ${filteredContents.length} 个${contentType}类型的内容`)
    return {
      ...result,
      contents: filteredContents
    }
  } catch (error) {
    console.error(`获取${contentType}内容失败:`, error.message)
    throw error
  }
}

/**
 * 示例4: 数据处理和转换
 */
export function processSubscriptionData(rawData) {
  const processed = {
    subscription: {
      ...rawData.subscription,
      unreadCount: rawData.contents.length,
      totalCount: rawData.contents.length,
      lastUpdate: new Date().toISOString()
    },
    contents: rawData.contents.map(content => ({
      ...content,
      isRead: false,
      isLiked: false,
      isBookmarked: false,
      readProgress: 0
    }))
  }
  
  // 按发布时间排序
  processed.contents.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime))
  
  return processed
}

/**
 * 示例5: 保存数据到本地存储
 */
export function saveToLocalStorage(data, key = 'subscriptionData') {
  try {
    const jsonData = JSON.stringify(data)
    localStorage.setItem(key, jsonData)
    console.log(`数据已保存到本地存储: ${key}`)
  } catch (error) {
    console.error('保存到本地存储失败:', error.message)
  }
}

/**
 * 示例6: 从本地存储读取数据
 */
export function loadFromLocalStorage(key = 'subscriptionData') {
  try {
    const jsonData = localStorage.getItem(key)
    if (jsonData) {
      const data = JSON.parse(jsonData)
      console.log(`从本地存储读取数据: ${key}`)
      return data
    }
    return null
  } catch (error) {
    console.error('从本地存储读取失败:', error.message)
    return null
  }
}

/**
 * 示例7: 定时更新订阅内容
 */
export class SubscriptionUpdater {
  constructor() {
    this.updateInterval = null
    this.feeds = []
  }
  
  /**
   * 添加订阅源
   */
  addFeed(feed) {
    this.feeds.push(feed)
  }
  
  /**
   * 开始定时更新
   * @param {number} intervalMinutes 更新间隔（分钟）
   */
  startAutoUpdate(intervalMinutes = 30) {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
    }
    
    console.log(`开始定时更新，间隔: ${intervalMinutes}分钟`)
    
    // 立即执行一次
    this.updateAllFeeds()
    
    // 设置定时器
    this.updateInterval = setInterval(() => {
      this.updateAllFeeds()
    }, intervalMinutes * 60 * 1000)
  }
  
  /**
   * 停止定时更新
   */
  stopAutoUpdate() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
      console.log('已停止定时更新')
    }
  }
  
  /**
   * 更新所有订阅源
   */
  async updateAllFeeds() {
    try {
      console.log('开始更新所有订阅源...')
      const results = await batchFetchFeeds(this.feeds)
      
      // 保存到本地存储
      results.forEach((result, index) => {
        const key = `subscription_${this.feeds[index].id}`
        saveToLocalStorage(processSubscriptionData(result), key)
      })
      
      console.log('所有订阅源更新完成')
      return results
    } catch (error) {
      console.error('更新订阅源失败:', error.message)
    }
  }
}

/**
 * 完整的使用示例
 */
export async function completeExample() {
  console.log('=== 爬虫完整使用示例 ===\n')
  
  try {
    // 1. 获取单个订阅源
    console.log('1. 获取得到每天听本书内容')
    const dedaoData = await fetchDedaoBooks()
    
    // 2. 处理数据
    console.log('\n2. 处理数据')
    const processedData = processSubscriptionData(dedaoData)
    
    // 3. 保存到本地存储
    console.log('\n3. 保存到本地存储')
    saveToLocalStorage(processedData, 'dedao_books')
    
    // 4. 从本地存储读取
    console.log('\n4. 从本地存储读取')
    const loadedData = loadFromLocalStorage('dedao_books')
    console.log('读取的数据:', loadedData ? '成功' : '失败')
    
    // 5. 设置定时更新
    console.log('\n5. 设置定时更新')
    const updater = new SubscriptionUpdater()
    updater.addFeed(PREDEFINED_FEEDS.DEDAO_BOOKS)
    // updater.startAutoUpdate(30) // 每30分钟更新一次
    
    console.log('\n=== 示例完成 ===')
    return processedData
    
  } catch (error) {
    console.error('示例执行失败:', error.message)
  }
}

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
  // 将示例函数挂载到window对象，方便在控制台调用
  window.crawlerExample = {
    fetchDedaoBooks,
    fetchMultipleFeeds,
    fetchContentByType,
    completeExample,
    SubscriptionUpdater
  }
  
  console.log('爬虫示例已加载，可以在控制台使用 window.crawlerExample 调用示例函数')
}
