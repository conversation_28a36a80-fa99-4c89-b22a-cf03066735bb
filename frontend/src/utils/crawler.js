/**
 * 订阅内容爬虫工具
 * 用于从各种API和RSS源获取订阅内容
 */

import axios from 'axios'

// 创建axios实例
const crawler = axios.create({
  timeout: 30000,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  }
})

/**
 * 通用爬虫类
 */
export class ContentCrawler {
  constructor() {
    this.retryCount = 3
    this.retryDelay = 1000
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 重试机制
   * @param {Function} fn 要执行的函数
   * @param {number} retries 重试次数
   */
  async retry(fn, retries = this.retryCount) {
    try {
      return await fn()
    } catch (error) {
      if (retries > 0) {
        console.warn(`请求失败，${this.retryDelay}ms后重试，剩余重试次数: ${retries}`)
        await this.delay(this.retryDelay)
        return this.retry(fn, retries - 1)
      }
      throw error
    }
  }

  /**
   * 获取Folo.is API数据
   * @param {string} feedId 订阅源ID
   * @param {number} entriesLimit 条目限制数量
   */
  async fetchFoloFeed(feedId, entriesLimit = 8) {
    const url = `https://api.folo.is/feeds?id=${feedId}&entriesLimit=${entriesLimit}`
    
    return this.retry(async () => {
      console.log(`正在获取Folo订阅源: ${feedId}`)
      const response = await crawler.get(url)
      
      if (response.data.code !== 0) {
        throw new Error(`API返回错误: ${response.data.message || '未知错误'}`)
      }

      return this.parseFoloResponse(response.data)
    })
  }

  /**
   * 解析Folo API响应数据
   * @param {Object} data API响应数据
   */
  parseFoloResponse(data) {
    const { feed, entries } = data.data
    
    return {
      subscription: {
        id: feed.id,
        name: feed.title,
        description: feed.description,
        url: feed.url,
        siteUrl: feed.siteUrl,
        image: feed.image,
        type: this.detectContentType(entries),
        lastUpdate: new Date().toISOString()
      },
      contents: entries.map(entry => this.parseEntry(entry))
    }
  }

  /**
   * 检测内容类型
   * @param {Array} entries 条目数组
   */
  detectContentType(entries) {
    if (!entries || entries.length === 0) return 'article'
    
    const firstEntry = entries[0]
    
    // 检查是否有音频附件
    if (firstEntry.attachments && firstEntry.attachments.some(att => att.mime_type?.startsWith('audio/'))) {
      return 'audio'
    }
    
    // 检查是否有视频附件
    if (firstEntry.attachments && firstEntry.attachments.some(att => att.mime_type?.startsWith('video/'))) {
      return 'video'
    }
    
    // 检查是否主要是图片内容
    if (firstEntry.media && firstEntry.media.some(media => media.type === 'photo')) {
      return 'image'
    }
    
    return 'article'
  }

  /**
   * 解析单个条目
   * @param {Object} entry 条目数据
   */
  parseEntry(entry) {
    const baseEntry = {
      id: this.generateId(entry.title + entry.publishedAt),
      title: entry.title,
      summary: this.extractSummary(entry.description || entry.content),
      content: entry.content,
      author: entry.author,
      publishTime: entry.publishedAt,
      url: entry.url,
      tags: this.extractTags(entry.content),
      likes: Math.floor(Math.random() * 1000), // 模拟数据
      comments: Math.floor(Math.random() * 100) // 模拟数据
    }

    // 根据内容类型添加特定字段
    if (entry.attachments) {
      const audioAttachment = entry.attachments.find(att => att.mime_type?.startsWith('audio/'))
      if (audioAttachment) {
        return {
          ...baseEntry,
          url: audioAttachment.url,
          duration: this.estimateAudioDuration(entry.content),
          plays: Math.floor(Math.random() * 10000),
          cover: entry.media?.[0]?.url || '/img/default-audio-cover.jpg',
          host: entry.author,
          chapters: this.extractChapters(entry.content)
        }
      }

      const videoAttachment = entry.attachments.find(att => att.mime_type?.startsWith('video/'))
      if (videoAttachment) {
        return {
          ...baseEntry,
          url: videoAttachment.url,
          thumbnail: entry.media?.[0]?.url || '/img/default-video-thumb.jpg',
          duration: this.estimateVideoDuration(entry.content),
          views: Math.floor(Math.random() * 50000)
        }
      }
    }

    // 图片类型
    if (entry.media && entry.media.length > 0) {
      const imageMedia = entry.media.find(media => media.type === 'photo')
      if (imageMedia) {
        return {
          ...baseEntry,
          url: imageMedia.url,
          thumbnail: imageMedia.url,
          description: entry.description || this.extractSummary(entry.content),
          photographer: entry.author,
          uploadTime: entry.publishedAt,
          downloads: Math.floor(Math.random() * 500),
          dimensions: {
            width: imageMedia.width || 1920,
            height: imageMedia.height || 1080
          }
        }
      }
    }

    // 默认文章类型
    return {
      ...baseEntry,
      readTime: this.estimateReadTime(entry.content),
      thumbnail: entry.media?.[0]?.url || '/img/default-article-thumb.jpg'
    }
  }

  /**
   * 生成唯一ID
   * @param {string} input 输入字符串
   */
  generateId(input) {
    return Math.abs(this.hashCode(input))
  }

  /**
   * 简单哈希函数
   * @param {string} str 字符串
   */
  hashCode(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash
  }

  /**
   * 提取摘要
   * @param {string} content 内容
   */
  extractSummary(content) {
    if (!content) return ''
    
    // 移除HTML标签
    const textContent = content.replace(/<[^>]*>/g, '')
    
    // 提取前200个字符作为摘要
    return textContent.substring(0, 200) + (textContent.length > 200 ? '...' : '')
  }

  /**
   * 提取标签
   * @param {string} content 内容
   */
  extractTags(content) {
    if (!content) return []
    
    // 简单的标签提取逻辑，可以根据需要改进
    const commonTags = ['技术', '科学', '文学', '艺术', '历史', '哲学', '经济', '管理', '心理学', '教育']
    const foundTags = []
    
    commonTags.forEach(tag => {
      if (content.includes(tag)) {
        foundTags.push(tag)
      }
    })
    
    return foundTags.slice(0, 3) // 最多返回3个标签
  }

  /**
   * 估算阅读时间
   * @param {string} content 内容
   */
  estimateReadTime(content) {
    if (!content) return '1分钟'
    
    const textContent = content.replace(/<[^>]*>/g, '')
    const wordCount = textContent.length
    const readingSpeed = 300 // 每分钟阅读字数
    const minutes = Math.ceil(wordCount / readingSpeed)
    
    return `${minutes}分钟`
  }

  /**
   * 估算音频时长
   * @param {string} content 内容
   */
  estimateAudioDuration(content) {
    // 根据内容长度估算音频时长
    const textLength = content ? content.replace(/<[^>]*>/g, '').length : 1000
    const estimatedSeconds = Math.floor(textLength / 10) // 假设每10个字符1秒
    return Math.max(estimatedSeconds, 300) // 最少5分钟
  }

  /**
   * 估算视频时长
   * @param {string} content 内容
   */
  estimateVideoDuration(content) {
    // 根据内容长度估算视频时长
    const textLength = content ? content.replace(/<[^>]*>/g, '').length : 1000
    const estimatedSeconds = Math.floor(textLength / 5) // 假设每5个字符1秒
    return Math.max(estimatedSeconds, 600) // 最少10分钟
  }

  /**
   * 提取章节信息
   * @param {string} content 内容
   */
  extractChapters(content) {
    if (!content) return []
    
    const chapters = []
    const lines = content.split('\n')
    let currentTime = 0
    
    lines.forEach((line, index) => {
      // 查找可能的章节标题
      if (line.includes('第') && (line.includes('部分') || line.includes('章')) || 
          line.match(/^\d+\./) || 
          line.match(/^[一二三四五六七八九十]+、/)) {
        chapters.push({
          id: index + 1,
          title: line.replace(/<[^>]*>/g, '').trim(),
          startTime: currentTime
        })
        currentTime += 300 // 每章节假设5分钟
      }
    })
    
    return chapters.slice(0, 5) // 最多返回5个章节
  }

  /**
   * 获取RSS订阅源数据
   * @param {string} rssUrl RSS URL
   */
  async fetchRSSFeed(rssUrl) {
    return this.retry(async () => {
      console.log(`正在获取RSS订阅源: ${rssUrl}`)
      const response = await crawler.get(rssUrl)
      
      // 这里需要解析XML，可以使用xml2js库
      // 为了简化，这里返回一个示例结构
      return {
        subscription: {
          name: 'RSS订阅源',
          description: '从RSS获取的订阅内容',
          type: 'article'
        },
        contents: []
      }
    })
  }
}

// 导出爬虫实例
export const contentCrawler = new ContentCrawler()

// 预定义的一些订阅源
export const PREDEFINED_FEEDS = {
  DEDAO_BOOKS: {
    id: '66321883746344960',
    name: '得到每天听本书',
    type: 'folo',
    entriesLimit: 8
  }
  // 可以添加更多预定义的订阅源
}

/**
 * 批量获取订阅内容
 * @param {Array} feeds 订阅源配置数组
 */
export async function batchFetchFeeds(feeds) {
  const results = []
  
  for (const feed of feeds) {
    try {
      let result
      
      if (feed.type === 'folo') {
        result = await contentCrawler.fetchFoloFeed(feed.id, feed.entriesLimit)
      } else if (feed.type === 'rss') {
        result = await contentCrawler.fetchRSSFeed(feed.url)
      }
      
      if (result) {
        results.push({
          ...result,
          feedConfig: feed
        })
      }
    } catch (error) {
      console.error(`获取订阅源失败: ${feed.name}`, error)
    }
    
    // 添加延迟避免请求过于频繁
    await contentCrawler.delay(1000)
  }
  
  return results
}
