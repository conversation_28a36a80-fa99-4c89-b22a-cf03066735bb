// API 配置
const API_BASE_URL = 'http://localhost:8001'

export const API_CONFIG = {
  BASE_URL: API_BASE_URL,
  ENDPOINTS: {
    AUTH: {
      LOGIN: `${API_BASE_URL}/api/auth/login`,
      REGISTER: `${API_BASE_URL}/api/auth/register`,
      LOGOUT: `${API_BASE_URL}/api/auth/logout`,
      USER: `${API_BASE_URL}/api/auth/user`,
      REFRESH: `${API_BASE_URL}/api/auth/refresh`,
      OAUTH_GOOGLE: `${API_BASE_URL}/api/auth/oauth/google`,
      OAUTH_GITHUB: `${API_BASE_URL}/api/auth/oauth/github`,
      OAUTH_CALLBACK: (provider) => `${API_BASE_URL}/api/auth/oauth/${provider}/callback`
    },
    CRAWLER: {
      CONTENT_LIST: `${API_BASE_URL}/api/crawler/content/list`,
      CONTENT_BY_SUBSCRIPTION: `${API_BASE_URL}/api/crawler/content/by-subscription`,
      CONTENT_BY_ID: (id) => `${API_BASE_URL}/api/crawler/content/${id}`,
      CONTENT_SEARCH: `${API_BASE_URL}/api/crawler/content/search`,
      CONTENT_STATS: `${API_BASE_URL}/api/crawler/content/stats`,
      CONTENT_POPULAR: `${API_BASE_URL}/api/crawler/content/popular`
    }
  }
}

// HTTP 请求工具类
export class ApiClient {
  static async request(url, options = {}) {
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }

    const token = localStorage.getItem('token')
    if (token) {
      defaultOptions.headers.Authorization = `Bearer ${token}`
    }

    try {
      const response = await fetch(url, { ...defaultOptions, ...options })

      // 无论响应状态如何，都尝试解析JSON
      const data = await response.json().catch(() => ({
        code: response.status,
        message: '请求失败'
      }))

      // 如果响应不成功，但有JSON数据，返回JSON数据（让业务逻辑处理）
      if (!response.ok) {
        if (response.status === 0 || !response.status) {
          throw new Error('Network Error')
        }
        // 返回错误数据而不是抛出异常，让业务逻辑处理
        return data
      }

      return data
    } catch (error) {
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        throw new Error('Network Error')
      }
      throw error
    }
  }

  static async get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' })
  }

  static async post(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  static async put(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  static async delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' })
  }

  // 构建URL参数
  static buildUrlWithParams(url, params = {}) {
    const searchParams = new URLSearchParams()
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        searchParams.append(key, params[key])
      }
    })

    const queryString = searchParams.toString()
    return queryString ? `${url}?${queryString}` : url
  }
}

/**
 * 订阅内容相关API
 */
export const SubscriptionAPI = {
  /**
   * 获取内容列表
   */
  getContentList: (type, page = 1, size = 20, keyword = '') => {
    const url = ApiClient.buildUrlWithParams(API_CONFIG.ENDPOINTS.CRAWLER.CONTENT_LIST, {
      type,
      page,
      size,
      keyword
    })
    return ApiClient.get(url)
  },

  /**
   * 根据订阅源获取内容
   */
  getContentBySubscription: (type, taskName, page = 1, size = 20) => {
    const url = ApiClient.buildUrlWithParams(API_CONFIG.ENDPOINTS.CRAWLER.CONTENT_BY_SUBSCRIPTION, {
      type,
      taskName,
      page,
      size
    })
    return ApiClient.get(url)
  },

  /**
   * 根据ID获取内容详情
   */
  getContentById: (id) => {
    return ApiClient.get(API_CONFIG.ENDPOINTS.CRAWLER.CONTENT_BY_ID(id))
  },

  /**
   * 搜索内容
   */
  searchContent: (type, keyword, page = 1, size = 20) => {
    const url = ApiClient.buildUrlWithParams(API_CONFIG.ENDPOINTS.CRAWLER.CONTENT_SEARCH, {
      type,
      keyword,
      page,
      size
    })
    return ApiClient.get(url)
  },

  /**
   * 获取统计信息
   */
  getStats: () => {
    return ApiClient.get(API_CONFIG.ENDPOINTS.CRAWLER.CONTENT_STATS)
  },

  /**
   * 获取热门内容
   */
  getPopularContent: (type, limit = 10) => {
    const url = ApiClient.buildUrlWithParams(API_CONFIG.ENDPOINTS.CRAWLER.CONTENT_POPULAR, {
      type,
      limit
    })
    return ApiClient.get(url)
  }
}

/**
 * 错误处理工具
 */
export const ErrorHandler = {
  /**
   * 处理API错误
   */
  handleApiError: (error) => {
    console.error('API错误:', error)

    let message = '请求失败'

    if (error.message === 'Network Error') {
      message = '网络连接失败，请检查网络设置'
    } else if (error.message.includes('timeout')) {
      message = '请求超时，请稍后重试'
    } else if (error.message) {
      message = error.message
    }

    return message
  },

  /**
   * 检查响应状态
   */
  checkResponse: (response) => {
    if (response && response.code === 200) {
      return response
    } else {
      throw new Error(response?.message || '请求失败')
    }
  }
}

/**
 * 请求重试工具
 */
export const RetryUtil = {
  /**
   * 重试请求
   */
  retry: async (requestFn, maxRetries = 3, delay = 1000) => {
    let lastError

    for (let i = 0; i < maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error

        if (i === maxRetries - 1) {
          break
        }

        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
      }
    }

    throw lastError
  }
}