# 订阅中心 API 接口文档

## 📋 概述

本文档描述了订阅中心功能的前后端API接口规范，包括订阅管理、内容获取、媒体播放等功能的接口定义。

## 🔗 基础信息

- **Base URL**: `/api/v1`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 📊 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "error": "ERROR_CODE",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 🔐 认证接口

### 获取用户信息
```http
GET /api/v1/user/profile
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "username": "user123",
    "email": "<EMAIL>",
    "avatar": "/img/avatar.jpg"
  }
}
```

## 📚 订阅管理接口

### 1. 获取订阅列表

```http
GET /api/v1/subscriptions?type={type}&page={page}&limit={limit}
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| type | string | 否 | 订阅类型: article/image/video/audio |
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认20 |

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "subscriptions": [
      {
        "id": 1,
        "name": "科技前沿",
        "description": "最新科技资讯和趋势分析",
        "type": "article",
        "avatar": "/img/tech-avatar.jpg",
        "unreadCount": 12,
        "totalCount": 156,
        "lastUpdate": "2024-01-20T10:30:00Z",
        "isSubscribed": true,
        "tags": ["科技", "AI", "前沿"]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3
    }
  }
}
```

### 2. 添加订阅

```http
POST /api/v1/subscriptions
Content-Type: application/json
```

**请求体**:
```json
{
  "name": "订阅名称",
  "description": "订阅描述",
  "type": "article",
  "sourceUrl": "https://example.com/rss",
  "tags": ["标签1", "标签2"]
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "订阅添加成功",
  "data": {
    "id": 10,
    "name": "订阅名称",
    "type": "article",
    "isSubscribed": true
  }
}
```

### 3. 取消订阅

```http
DELETE /api/v1/subscriptions/{subscriptionId}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "取消订阅成功"
}
```

### 4. 更新订阅设置

```http
PUT /api/v1/subscriptions/{subscriptionId}
Content-Type: application/json
```

**请求体**:
```json
{
  "name": "新订阅名称",
  "description": "新描述",
  "tags": ["新标签"],
  "notificationEnabled": true
}
```

## 📄 内容获取接口

### 1. 获取文章列表

```http
GET /api/v1/subscriptions/{subscriptionId}/articles?page={page}&limit={limit}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "articles": [
      {
        "id": 101,
        "title": "ChatGPT-4的突破性进展：多模态AI的新纪元",
        "summary": "OpenAI发布的ChatGPT-4展现了前所未有的多模态能力...",
        "content": "<h2>ChatGPT-4：多模态AI的革命性突破</h2>...",
        "author": "张科技",
        "publishTime": "2024-01-20T10:30:00Z",
        "readTime": "5分钟",
        "tags": ["AI", "ChatGPT", "多模态"],
        "likes": 156,
        "comments": 23,
        "isRead": false,
        "thumbnail": "/img/article-thumb.jpg"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### 2. 获取文章详情

```http
GET /api/v1/articles/{articleId}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "id": 101,
    "title": "文章标题",
    "content": "<html>完整文章内容</html>",
    "author": "作者名",
    "publishTime": "2024-01-20T10:30:00Z",
    "readTime": "5分钟",
    "tags": ["标签1", "标签2"],
    "likes": 156,
    "comments": 23,
    "isLiked": false,
    "isBookmarked": false,
    "relatedArticles": [
      {
        "id": 102,
        "title": "相关文章标题",
        "thumbnail": "/img/related.jpg"
      }
    ]
  }
}
```

### 3. 获取图片列表

```http
GET /api/v1/subscriptions/{subscriptionId}/images?page={page}&limit={limit}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "images": [
      {
        "id": 201,
        "title": "城市夜景摄影技巧",
        "description": "掌握城市夜景摄影的关键技巧",
        "url": "/img/city-night.jpg",
        "thumbnail": "/img/city-night-thumb.jpg",
        "photographer": "王摄影",
        "uploadTime": "2024-01-20T11:00:00Z",
        "likes": 234,
        "downloads": 45,
        "tags": ["摄影", "夜景", "城市"],
        "dimensions": {
          "width": 1920,
          "height": 1080
        },
        "fileSize": "2.5MB"
      }
    ]
  }
}
```

### 4. 获取视频列表

```http
GET /api/v1/subscriptions/{subscriptionId}/videos?page={page}&limit={limit}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "videos": [
      {
        "id": 301,
        "title": "Vue 3 Composition API 深度解析",
        "description": "详细讲解Vue 3 Composition API的核心概念",
        "url": "/videos/vue3-composition-api.mp4",
        "thumbnail": "/img/vue3-thumb.jpg",
        "author": "前端大师",
        "uploadTime": "2024-01-20T12:00:00Z",
        "duration": 1800,
        "views": 15420,
        "likes": 892,
        "comments": 156,
        "quality": ["720p", "1080p"],
        "subtitles": ["zh-CN", "en-US"]
      }
    ]
  }
}
```

### 5. 获取音频列表

```http
GET /api/v1/subscriptions/{subscriptionId}/audios?page={page}&limit={limit}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "audios": [
      {
        "id": 401,
        "title": "技术人的职业规划与成长",
        "summary": "资深技术专家分享从初级工程师到技术领导者的成长路径",
        "description": "详细的节目描述...",
        "url": "/audio/career-growth.mp3",
        "cover": "/img/career-cover.jpg",
        "host": "技术大咖",
        "publishTime": "2024-01-20T13:00:00Z",
        "duration": 3600,
        "plays": 25420,
        "likes": 1892,
        "comments": 256,
        "tags": ["职业规划", "技术成长", "管理"],
        "chapters": [
          {
            "id": 1,
            "title": "开场介绍",
            "startTime": 0
          },
          {
            "id": 2,
            "title": "初级工程师阶段",
            "startTime": 300
          }
        ]
      }
    ]
  }
}
```

## 🎯 交互接口

### 1. 点赞/取消点赞

```http
POST /api/v1/content/{contentType}/{contentId}/like
```

**请求参数**:
| 参数 | 类型 | 说明 |
|------|------|------|
| contentType | string | 内容类型: article/image/video/audio |
| contentId | number | 内容ID |

**请求体**:
```json
{
  "action": "like" // 或 "unlike"
}
```

### 2. 添加评论

```http
POST /api/v1/content/{contentType}/{contentId}/comments
Content-Type: application/json
```

**请求体**:
```json
{
  "content": "评论内容",
  "parentId": null // 回复评论时填写父评论ID
}
```

### 3. 收藏/取消收藏

```http
POST /api/v1/content/{contentType}/{contentId}/bookmark
```

**请求体**:
```json
{
  "action": "bookmark" // 或 "unbookmark"
}
```

### 4. 分享内容

```http
POST /api/v1/content/{contentType}/{contentId}/share
```

**请求体**:
```json
{
  "platform": "wechat", // 分享平台: wechat/weibo/qq
  "message": "分享消息"
}
```

## 📊 统计接口

### 1. 获取订阅统计

```http
GET /api/v1/subscriptions/stats
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "totalSubscriptions": 15,
    "totalUnread": 45,
    "byType": {
      "article": {
        "count": 8,
        "unread": 25
      },
      "image": {
        "count": 3,
        "unread": 12
      },
      "video": {
        "count": 2,
        "unread": 5
      },
      "audio": {
        "count": 2,
        "unread": 3
      }
    },
    "recentActivity": [
      {
        "type": "new_content",
        "subscriptionName": "科技前沿",
        "contentTitle": "新文章标题",
        "timestamp": "2024-01-20T10:30:00Z"
      }
    ]
  }
}
```

### 2. 标记为已读

```http
POST /api/v1/content/{contentType}/{contentId}/read
```

### 3. 批量标记已读

```http
POST /api/v1/subscriptions/{subscriptionId}/mark-all-read
```

## 🔍 搜索接口

### 1. 搜索内容

```http
GET /api/v1/search?q={keyword}&type={type}&page={page}&limit={limit}
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | string | 是 | 搜索关键词 |
| type | string | 否 | 内容类型过滤 |
| page | number | 否 | 页码 |
| limit | number | 否 | 每页数量 |

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "results": [
      {
        "id": 101,
        "type": "article",
        "title": "搜索结果标题",
        "summary": "搜索结果摘要...",
        "highlight": "高亮的搜索关键词",
        "subscriptionName": "订阅源名称",
        "publishTime": "2024-01-20T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25
    },
    "suggestions": ["相关搜索建议1", "相关搜索建议2"]
  }
}
```

## 📱 推送接口

### 1. 获取推送设置

```http
GET /api/v1/notifications/settings
```

### 2. 更新推送设置

```http
PUT /api/v1/notifications/settings
Content-Type: application/json
```

**请求体**:
```json
{
  "emailNotification": true,
  "pushNotification": true,
  "notificationTypes": ["new_content", "weekly_digest"],
  "quietHours": {
    "enabled": true,
    "startTime": "22:00",
    "endTime": "08:00"
  }
}
```

## ❌ 错误码说明

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 400 | 400 | 请求参数错误 |
| 401 | 401 | 未授权访问 |
| 403 | 403 | 权限不足 |
| 404 | 404 | 资源不存在 |
| 409 | 409 | 资源冲突（如重复订阅） |
| 429 | 429 | 请求频率限制 |
| 500 | 500 | 服务器内部错误 |

## 📝 注意事项

1. **认证**: 所有接口都需要在请求头中携带有效的Bearer Token
2. **频率限制**: API调用频率限制为每分钟100次
3. **文件上传**: 图片和音频文件上传需要使用multipart/form-data格式
4. **缓存**: 内容列表接口支持ETag缓存，建议客户端实现缓存机制
5. **分页**: 所有列表接口都支持分页，默认每页20条记录
6. **时间格式**: 所有时间字段使用ISO 8601格式（UTC时间）

## 🔄 版本更新

- **v1.0.0** (2024-01-20): 初始版本，包含基础订阅和内容管理功能
- **v1.1.0** (计划): 添加AI推荐和个性化功能
- **v1.2.0** (计划): 添加社交功能和用户互动

## 📋 后端实现示例

### Node.js + Express 路由示例

```javascript
// routes/subscriptions.js
const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const subscriptionController = require('../controllers/subscriptionController');

// 获取订阅列表
router.get('/', authenticateToken, subscriptionController.getSubscriptions);

// 添加订阅
router.post('/', authenticateToken, subscriptionController.addSubscription);

// 取消订阅
router.delete('/:id', authenticateToken, subscriptionController.removeSubscription);

// 更新订阅
router.put('/:id', authenticateToken, subscriptionController.updateSubscription);

// 获取统计信息
router.get('/stats', authenticateToken, subscriptionController.getStats);

// 获取订阅内容
router.get('/:id/articles', authenticateToken, subscriptionController.getArticles);
router.get('/:id/images', authenticateToken, subscriptionController.getImages);
router.get('/:id/videos', authenticateToken, subscriptionController.getVideos);
router.get('/:id/audios', authenticateToken, subscriptionController.getAudios);

module.exports = router;
```

### 控制器示例

```javascript
// controllers/subscriptionController.js
const subscriptionService = require('../services/subscriptionService');

class SubscriptionController {
  async getSubscriptions(req, res) {
    try {
      const { type, page = 1, limit = 20 } = req.query;
      const userId = req.user.id;

      const result = await subscriptionService.getUserSubscriptions(userId, {
        type,
        page: parseInt(page),
        limit: parseInt(limit)
      });

      res.json({
        code: 200,
        message: 'success',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        code: 500,
        message: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async addSubscription(req, res) {
    try {
      const userId = req.user.id;
      const subscriptionData = req.body;

      const result = await subscriptionService.addSubscription(userId, subscriptionData);

      res.status(201).json({
        code: 201,
        message: '订阅添加成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      if (error.code === 'DUPLICATE_SUBSCRIPTION') {
        res.status(409).json({
          code: 409,
          message: '已经订阅过该内容',
          error: 'DUPLICATE_SUBSCRIPTION',
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          code: 500,
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
  }
}

module.exports = new SubscriptionController();
```

### 数据库模型示例 (Sequelize)

```javascript
// models/Subscription.js
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Subscription = sequelize.define('Subscription', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT
  },
  type: {
    type: DataTypes.ENUM('article', 'image', 'video', 'audio'),
    allowNull: false
  },
  sourceUrl: {
    type: DataTypes.STRING
  },
  avatar: {
    type: DataTypes.STRING
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  tags: {
    type: DataTypes.JSON
  }
}, {
  tableName: 'subscriptions',
  timestamps: true
});

module.exports = Subscription;
```

## 🔧 前端集成示例

### Pinia Store 集成

```javascript
// stores/subscription.js (更新版本)
import { defineStore } from 'pinia'
import { subscriptionApi, contentApi } from '@/services/subscriptionApi'

export const useSubscriptionStore = defineStore('subscription', () => {
  // ... 现有状态定义

  // API集成方法
  const fetchSubscriptions = async (type) => {
    try {
      loading.value = true
      const response = await subscriptionApi.getSubscriptions({ type })
      subscriptions.value[type] = response.data.subscriptions
    } catch (error) {
      console.error('获取订阅列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchContent = async (subscriptionId, type) => {
    try {
      let response
      switch (type) {
        case 'article':
          response = await contentApi.getArticles(subscriptionId)
          break
        case 'image':
          response = await contentApi.getImages(subscriptionId)
          break
        case 'video':
          response = await contentApi.getVideos(subscriptionId)
          break
        case 'audio':
          response = await contentApi.getAudios(subscriptionId)
          break
      }

      contents.value[subscriptionId] = response.data
    } catch (error) {
      console.error('获取内容失败:', error)
      throw error
    }
  }

  return {
    // ... 现有返回值
    fetchSubscriptions,
    fetchContent
  }
})
```

## 🧪 API测试示例

### Jest 单元测试

```javascript
// tests/subscriptionApi.test.js
import { subscriptionApi } from '@/services/subscriptionApi'
import axios from 'axios'

jest.mock('axios')
const mockedAxios = axios as jest.Mocked<typeof axios>

describe('Subscription API', () => {
  beforeEach(() => {
    mockedAxios.create.mockReturnValue(mockedAxios)
  })

  test('should fetch subscriptions successfully', async () => {
    const mockData = {
      code: 200,
      data: {
        subscriptions: [
          { id: 1, name: '测试订阅', type: 'article' }
        ]
      }
    }

    mockedAxios.get.mockResolvedValue({ data: mockData })

    const result = await subscriptionApi.getSubscriptions({ type: 'article' })

    expect(mockedAxios.get).toHaveBeenCalledWith('/subscriptions', {
      params: { type: 'article' }
    })
    expect(result).toEqual(mockData)
  })
})
```
