# 订阅内容爬虫使用指南

## 📖 概述

本文档介绍如何使用订阅中心的爬虫功能来获取外部订阅源的数据，包括Folo.is API和RSS订阅源。

## 🎯 支持的数据源

### 1. Folo.is API
- **URL格式**: `https://api.folo.is/feeds?id={feedId}&entriesLimit={limit}`
- **示例**: `https://api.folo.is/feeds?id=66321883746344960&entriesLimit=8`
- **特点**: 
  - 支持多种内容类型（文章、音频、视频、图片）
  - 结构化数据，解析准确
  - 支持附件和媒体文件

### 2. RSS订阅源
- **格式**: 标准RSS/Atom XML格式
- **特点**: 
  - 广泛支持的订阅格式
  - 需要XML解析
  - 内容类型需要智能检测

## 🛠️ 使用方法

### 方法一：使用预定义订阅源

```javascript
import { useSubscriptionStore } from '@/stores/subscription'

const subscriptionStore = useSubscriptionStore()

// 添加得到每天听本书订阅
await subscriptionStore.addPredefinedSubscription('DEDAO_BOOKS')
```

### 方法二：直接使用爬虫工具

```javascript
import { contentCrawler } from '@/utils/crawler.js'

// 获取Folo.is订阅源数据
const result = await contentCrawler.fetchFoloFeed('66321883746344960', 8)

console.log('订阅信息:', result.subscription)
console.log('内容列表:', result.contents)
```

### 方法三：批量获取多个订阅源

```javascript
import { batchFetchFeeds } from '@/utils/crawler.js'

const feeds = [
  {
    id: '66321883746344960',
    name: '得到每天听本书',
    type: 'folo',
    entriesLimit: 8
  }
  // 可以添加更多订阅源
]

const results = await batchFetchFeeds(feeds)
```

## 📊 数据结构

### 订阅源数据结构
```javascript
{
  subscription: {
    id: "66321883746344960",
    name: "得到每天听本书",
    description: "每天听本书，每天进步一点点",
    url: "https://example.com/feed",
    siteUrl: "https://example.com",
    image: "https://example.com/avatar.jpg",
    type: "audio", // article | image | video | audio
    lastUpdate: "2024-01-20T10:30:00Z"
  },
  contents: [
    // 内容数组，结构根据类型不同而变化
  ]
}
```

### 文章内容结构
```javascript
{
  id: 101,
  title: "文章标题",
  summary: "文章摘要...",
  content: "<html>完整内容</html>",
  author: "作者名",
  publishTime: "2024-01-20T10:30:00Z",
  url: "https://example.com/article/101",
  tags: ["标签1", "标签2"],
  likes: 156,
  comments: 23,
  readTime: "5分钟",
  thumbnail: "/img/article-thumb.jpg"
}
```

### 音频内容结构
```javascript
{
  id: 401,
  title: "音频标题",
  summary: "音频简介...",
  description: "详细描述...",
  url: "/audio/file.mp3",
  cover: "/img/audio-cover.jpg",
  host: "主播名",
  publishTime: "2024-01-20T13:00:00Z",
  duration: 3600, // 秒
  plays: 25420,
  likes: 1892,
  comments: 256,
  tags: ["标签1", "标签2"],
  chapters: [
    {
      id: 1,
      title: "章节标题",
      startTime: 0
    }
  ]
}
```

### 图片内容结构
```javascript
{
  id: 201,
  title: "图片标题",
  description: "图片描述",
  url: "/img/image.jpg",
  thumbnail: "/img/image-thumb.jpg",
  photographer: "摄影师",
  uploadTime: "2024-01-20T11:00:00Z",
  likes: 234,
  downloads: 45,
  tags: ["标签1", "标签2"],
  dimensions: {
    width: 1920,
    height: 1080
  }
}
```

### 视频内容结构
```javascript
{
  id: 301,
  title: "视频标题",
  description: "视频描述",
  url: "/videos/video.mp4",
  thumbnail: "/img/video-thumb.jpg",
  author: "作者名",
  uploadTime: "2024-01-20T12:00:00Z",
  duration: 1800, // 秒
  views: 15420,
  likes: 892,
  comments: 156,
  tags: ["标签1", "标签2"]
}
```

## 🔧 高级功能

### 1. 自动内容类型检测

爬虫会根据内容特征自动检测类型：

```javascript
// 检测逻辑
function detectContentType(entries) {
  const firstEntry = entries[0]
  
  // 检查音频附件
  if (firstEntry.attachments?.some(att => att.mime_type?.startsWith('audio/'))) {
    return 'audio'
  }
  
  // 检查视频附件
  if (firstEntry.attachments?.some(att => att.mime_type?.startsWith('video/'))) {
    return 'video'
  }
  
  // 检查图片媒体
  if (firstEntry.media?.some(media => media.type === 'photo')) {
    return 'image'
  }
  
  return 'article' // 默认为文章
}
```

### 2. 智能数据提取

```javascript
// 提取摘要
function extractSummary(content) {
  const textContent = content.replace(/<[^>]*>/g, '')
  return textContent.substring(0, 200) + (textContent.length > 200 ? '...' : '')
}

// 提取标签
function extractTags(content) {
  const commonTags = ['技术', '科学', '文学', '艺术', '历史']
  return commonTags.filter(tag => content.includes(tag)).slice(0, 3)
}

// 估算阅读时间
function estimateReadTime(content) {
  const wordCount = content.replace(/<[^>]*>/g, '').length
  const minutes = Math.ceil(wordCount / 300) // 每分钟300字
  return `${minutes}分钟`
}
```

### 3. 错误处理和重试机制

```javascript
// 重试机制
async function retry(fn, retries = 3) {
  try {
    return await fn()
  } catch (error) {
    if (retries > 0) {
      await delay(1000) // 延迟1秒
      return retry(fn, retries - 1)
    }
    throw error
  }
}
```

## 🎨 UI组件使用

### 在订阅中心页面中添加爬虫管理

```vue
<template>
  <div class="subscription-center">
    <!-- 现有内容 -->
    
    <!-- 添加爬虫管理标签 -->
    <div class="type-tabs">
      <div class="tabs-wrapper">
        <button @click="showCrawlerManager = true">
          <i class="icon-crawler"></i>
          <span>爬虫管理</span>
        </button>
      </div>
    </div>
    
    <!-- 爬虫管理弹窗 -->
    <div v-if="showCrawlerManager" class="crawler-modal">
      <CrawlerManager @close="showCrawlerManager = false" />
    </div>
  </div>
</template>

<script>
import CrawlerManager from '@/components/subscription/CrawlerManager.vue'

export default {
  components: {
    CrawlerManager
  },
  data() {
    return {
      showCrawlerManager: false
    }
  }
}
</script>
```

## 📱 实际应用示例

### 示例1：获取得到每天听本书

```javascript
// 在浏览器控制台中运行
import { contentCrawler } from '@/utils/crawler.js'

async function getDedaoBooks() {
  try {
    const result = await contentCrawler.fetchFoloFeed('66321883746344960', 8)
    
    console.log('=== 得到每天听本书 ===')
    console.log('订阅名称:', result.subscription.name)
    console.log('内容类型:', result.subscription.type)
    console.log('内容数量:', result.contents.length)
    
    result.contents.forEach((content, index) => {
      console.log(`${index + 1}. ${content.title}`)
      console.log(`   主播: ${content.host}`)
      console.log(`   时长: ${Math.floor(content.duration / 60)}分钟`)
      console.log(`   播放: ${content.plays}次`)
    })
    
    return result
  } catch (error) {
    console.error('获取失败:', error)
  }
}

// 调用函数
getDedaoBooks()
```

### 示例2：集成到Vue组件

```vue
<template>
  <div class="subscription-fetcher">
    <button @click="fetchData" :disabled="loading">
      {{ loading ? '获取中...' : '获取订阅数据' }}
    </button>
    
    <div v-if="result" class="result">
      <h3>{{ result.subscription.name }}</h3>
      <p>获取到 {{ result.contents.length }} 条内容</p>
      
      <ul>
        <li v-for="content in result.contents" :key="content.id">
          {{ content.title }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { contentCrawler } from '@/utils/crawler.js'

export default {
  setup() {
    const loading = ref(false)
    const result = ref(null)
    
    const fetchData = async () => {
      try {
        loading.value = true
        result.value = await contentCrawler.fetchFoloFeed('66321883746344960', 8)
      } catch (error) {
        console.error('获取失败:', error)
        alert('获取失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }
    
    return {
      loading,
      result,
      fetchData
    }
  }
}
</script>
```

## 🚀 性能优化建议

### 1. 缓存策略
- 使用localStorage缓存获取的数据
- 设置合理的缓存过期时间
- 避免重复请求相同的数据

### 2. 请求优化
- 使用请求去重避免并发请求
- 添加请求延迟避免频率限制
- 实现断点续传和增量更新

### 3. 错误处理
- 实现重试机制
- 记录错误日志
- 提供用户友好的错误提示

## 🔒 注意事项

1. **跨域问题**: 某些API可能存在跨域限制，需要配置代理
2. **频率限制**: 避免过于频繁的请求，遵守API使用条款
3. **数据格式**: 不同订阅源的数据格式可能不同，需要适配
4. **错误处理**: 网络异常、API变更等情况需要妥善处理
5. **用户体验**: 提供加载状态、错误提示等用户反馈

## 📞 技术支持

如果在使用过程中遇到问题，可以：

1. 查看浏览器控制台的错误信息
2. 检查网络连接和API可用性
3. 验证订阅源ID和URL的正确性
4. 参考本文档的示例代码

---

这个爬虫系统为订阅中心提供了强大的数据获取能力，支持多种订阅源格式，具有良好的扩展性和容错性。
