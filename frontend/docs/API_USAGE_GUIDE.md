# 订阅中心 API 使用指南

## 📖 概述

本文档提供了订阅中心API的详细使用指南，包括最佳实践、错误处理、性能优化等内容。

## 🚀 快速开始

### 1. 安装和配置

```javascript
// 在 main.js 中配置API基础URL
import { createApp } from 'vue'
import App from './App.vue'

// 设置环境变量
process.env.VUE_APP_API_BASE_URL = 'https://api.example.com/v1'

const app = createApp(App)
app.mount('#app')
```

### 2. 基础使用示例

```javascript
// 在组件中使用API
import { subscriptionApi, contentApi } from '@/services/subscriptionApi'

export default {
  async mounted() {
    try {
      // 获取订阅列表
      const subscriptions = await subscriptionApi.getSubscriptions({
        type: 'article',
        page: 1,
        limit: 20
      })
      
      console.log('订阅列表:', subscriptions.data)
    } catch (error) {
      console.error('获取订阅失败:', error)
    }
  }
}
```

## 🔧 在 Pinia Store 中集成

### 完整的 Store 实现

```javascript
// stores/subscription.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { subscriptionApi, contentApi, interactionApi } from '@/services/subscriptionApi'

export const useSubscriptionStore = defineStore('subscription', () => {
  // 状态
  const subscriptions = ref({})
  const contents = ref({})
  const loading = ref(false)
  const error = ref(null)
  const currentType = ref('article')
  const selectedSubscription = ref(null)
  const selectedContent = ref(null)

  // 计算属性
  const currentSubscriptions = computed(() => {
    return subscriptions.value[currentType.value] || []
  })

  const totalUnreadCount = computed(() => {
    return currentSubscriptions.value.reduce((total, sub) => total + sub.unreadCount, 0)
  })

  // 异步操作
  const fetchSubscriptions = async (type = currentType.value, refresh = false) => {
    if (subscriptions.value[type] && !refresh) {
      return subscriptions.value[type]
    }

    try {
      loading.value = true
      error.value = null
      
      const response = await subscriptionApi.getSubscriptions({ type })
      subscriptions.value[type] = response.data.subscriptions
      
      return response.data.subscriptions
    } catch (err) {
      error.value = err.message || '获取订阅列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchContent = async (subscriptionId, type = currentType.value) => {
    const cacheKey = `${subscriptionId}_${type}`
    
    if (contents.value[cacheKey]) {
      return contents.value[cacheKey]
    }

    try {
      loading.value = true
      let response

      switch (type) {
        case 'article':
          response = await contentApi.getArticles(subscriptionId)
          break
        case 'image':
          response = await contentApi.getImages(subscriptionId)
          break
        case 'video':
          response = await contentApi.getVideos(subscriptionId)
          break
        case 'audio':
          response = await contentApi.getAudios(subscriptionId)
          break
        default:
          throw new Error(`不支持的内容类型: ${type}`)
      }

      contents.value[cacheKey] = response.data
      return response.data
    } catch (err) {
      error.value = err.message || '获取内容失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const addSubscription = async (subscriptionData) => {
    try {
      loading.value = true
      const response = await subscriptionApi.addSubscription(subscriptionData)
      
      // 更新本地状态
      const type = subscriptionData.type
      if (!subscriptions.value[type]) {
        subscriptions.value[type] = []
      }
      subscriptions.value[type].unshift(response.data)
      
      return response.data
    } catch (err) {
      error.value = err.message || '添加订阅失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const removeSubscription = async (subscriptionId) => {
    try {
      loading.value = true
      await subscriptionApi.removeSubscription(subscriptionId)
      
      // 从本地状态中移除
      Object.keys(subscriptions.value).forEach(type => {
        subscriptions.value[type] = subscriptions.value[type].filter(
          sub => sub.id !== subscriptionId
        )
      })
      
      // 清除相关内容缓存
      Object.keys(contents.value).forEach(key => {
        if (key.startsWith(`${subscriptionId}_`)) {
          delete contents.value[key]
        }
      })
      
    } catch (err) {
      error.value = err.message || '取消订阅失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const toggleLike = async (contentType, contentId, isLiked) => {
    try {
      const action = isLiked ? 'unlike' : 'like'
      await interactionApi.toggleLike(contentType, contentId, action)
      
      // 更新本地状态
      updateContentInteraction(contentId, 'likes', isLiked ? -1 : 1)
      
    } catch (err) {
      error.value = err.message || '操作失败'
      throw err
    }
  }

  const markAsRead = async (contentType, contentId) => {
    try {
      await interactionApi.markAsRead(contentType, contentId)
      
      // 更新本地状态
      updateContentReadStatus(contentId, true)
      
    } catch (err) {
      error.value = err.message || '标记已读失败'
      throw err
    }
  }

  // 辅助方法
  const updateContentInteraction = (contentId, field, delta) => {
    Object.values(contents.value).forEach(contentGroup => {
      Object.values(contentGroup).forEach(items => {
        if (Array.isArray(items)) {
          const item = items.find(item => item.id === contentId)
          if (item && typeof item[field] === 'number') {
            item[field] += delta
          }
        }
      })
    })
  }

  const updateContentReadStatus = (contentId, isRead) => {
    Object.values(contents.value).forEach(contentGroup => {
      Object.values(contentGroup).forEach(items => {
        if (Array.isArray(items)) {
          const item = items.find(item => item.id === contentId)
          if (item) {
            item.isRead = isRead
          }
        }
      })
    })
  }

  const clearError = () => {
    error.value = null
  }

  const setCurrentType = (type) => {
    currentType.value = type
    selectedSubscription.value = null
    selectedContent.value = null
  }

  const selectSubscription = (subscription) => {
    selectedSubscription.value = subscription
    selectedContent.value = null
  }

  const selectContent = (content) => {
    selectedContent.value = content
  }

  return {
    // 状态
    subscriptions,
    contents,
    loading,
    error,
    currentType,
    selectedSubscription,
    selectedContent,
    
    // 计算属性
    currentSubscriptions,
    totalUnreadCount,
    
    // 方法
    fetchSubscriptions,
    fetchContent,
    addSubscription,
    removeSubscription,
    toggleLike,
    markAsRead,
    clearError,
    setCurrentType,
    selectSubscription,
    selectContent
  }
})
```

## 🎯 组件中的使用示例

### 订阅列表组件

```vue
<template>
  <div class="subscription-list">
    <div v-if="loading" class="loading">
      <LoadingSpinner text="加载中..." />
    </div>
    
    <div v-else-if="error" class="error">
      <ErrorMessage 
        :message="error" 
        :show-retry="true"
        @retry="handleRetry"
      />
    </div>
    
    <div v-else class="subscription-items">
      <div
        v-for="subscription in subscriptions"
        :key="subscription.id"
        :class="['subscription-item', { active: selectedId === subscription.id }]"
        @click="handleSelect(subscription)"
      >
        <img :src="subscription.avatar" :alt="subscription.name" />
        <div class="info">
          <h4>{{ subscription.name }}</h4>
          <p>{{ subscription.description }}</p>
          <span v-if="subscription.unreadCount" class="unread">
            {{ subscription.unreadCount }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, watch } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import ErrorMessage from '@/components/common/ErrorMessage.vue'

export default {
  name: 'SubscriptionList',
  components: {
    LoadingSpinner,
    ErrorMessage
  },
  props: {
    type: {
      type: String,
      required: true
    }
  },
  emits: ['select'],
  setup(props, { emit }) {
    const subscriptionStore = useSubscriptionStore()

    // 计算属性
    const subscriptions = computed(() => 
      subscriptionStore.subscriptions[props.type] || []
    )
    const loading = computed(() => subscriptionStore.loading)
    const error = computed(() => subscriptionStore.error)
    const selectedId = computed(() => 
      subscriptionStore.selectedSubscription?.id
    )

    // 方法
    const loadSubscriptions = async () => {
      try {
        await subscriptionStore.fetchSubscriptions(props.type)
      } catch (err) {
        console.error('加载订阅失败:', err)
      }
    }

    const handleSelect = (subscription) => {
      subscriptionStore.selectSubscription(subscription)
      emit('select', subscription)
    }

    const handleRetry = () => {
      subscriptionStore.clearError()
      loadSubscriptions()
    }

    // 生命周期
    onMounted(() => {
      loadSubscriptions()
    })

    // 监听类型变化
    watch(() => props.type, () => {
      loadSubscriptions()
    })

    return {
      subscriptions,
      loading,
      error,
      selectedId,
      handleSelect,
      handleRetry
    }
  }
}
</script>
```

## 🛡️ 错误处理最佳实践

### 1. 全局错误处理

```javascript
// utils/errorHandler.js
export const handleApiError = (error, context = '') => {
  console.error(`API错误 [${context}]:`, error)
  
  // 根据错误类型进行不同处理
  if (error.code === 401) {
    // 未授权，跳转登录
    router.push('/login')
    return '请重新登录'
  } else if (error.code === 403) {
    return '权限不足'
  } else if (error.code === 404) {
    return '请求的资源不存在'
  } else if (error.code === 429) {
    return '请求过于频繁，请稍后再试'
  } else if (error.code >= 500) {
    return '服务器错误，请稍后再试'
  } else {
    return error.message || '操作失败'
  }
}
```

### 2. 组件级错误处理

```javascript
// composables/useAsyncOperation.js
import { ref } from 'vue'
import { handleApiError } from '@/utils/errorHandler'

export function useAsyncOperation() {
  const loading = ref(false)
  const error = ref(null)

  const execute = async (operation, context = '') => {
    try {
      loading.value = true
      error.value = null
      
      const result = await operation()
      return result
    } catch (err) {
      error.value = handleApiError(err, context)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    loading,
    error,
    execute,
    clearError
  }
}
```

## 🚀 性能优化建议

### 1. 请求缓存

```javascript
// utils/cache.js
class ApiCache {
  constructor(ttl = 5 * 60 * 1000) { // 默认5分钟
    this.cache = new Map()
    this.ttl = ttl
  }

  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  get(key) {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  clear() {
    this.cache.clear()
  }
}

export const apiCache = new ApiCache()
```

### 2. 请求去重

```javascript
// utils/requestDeduplication.js
class RequestDeduplication {
  constructor() {
    this.pendingRequests = new Map()
  }

  async execute(key, requestFn) {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)
    }

    const promise = requestFn()
    this.pendingRequests.set(key, promise)

    try {
      const result = await promise
      return result
    } finally {
      this.pendingRequests.delete(key)
    }
  }
}

export const requestDeduplication = new RequestDeduplication()
```

## 📱 移动端适配

### 响应式API调用

```javascript
// composables/useResponsiveApi.js
import { ref, computed } from 'vue'
import { useBreakpoints } from '@vueuse/core'

export function useResponsiveApi() {
  const breakpoints = useBreakpoints({
    mobile: 768,
    tablet: 1024,
    desktop: 1280
  })

  const isMobile = breakpoints.smaller('mobile')
  const isTablet = breakpoints.between('mobile', 'desktop')
  const isDesktop = breakpoints.greater('tablet')

  const getPageSize = computed(() => {
    if (isMobile.value) return 10
    if (isTablet.value) return 15
    return 20
  })

  const getImageQuality = computed(() => {
    if (isMobile.value) return 'low'
    if (isTablet.value) return 'medium'
    return 'high'
  })

  return {
    isMobile,
    isTablet,
    isDesktop,
    getPageSize,
    getImageQuality
  }
}
```

## 🧪 测试策略

### API Mock 数据

```javascript
// tests/mocks/subscriptionMocks.js
export const mockSubscriptions = {
  article: [
    {
      id: 1,
      name: '科技前沿',
      description: '最新科技资讯',
      type: 'article',
      unreadCount: 12,
      avatar: '/img/tech.jpg'
    }
  ]
}

export const mockArticles = [
  {
    id: 101,
    title: '测试文章',
    summary: '文章摘要',
    author: '测试作者',
    publishTime: '2024-01-20T10:30:00Z',
    likes: 100,
    comments: 20
  }
]
```

这个完整的API文档和使用指南提供了：

1. **完整的接口规范** - 包含所有订阅中心相关的API接口
2. **前端服务层实现** - 提供了完整的API调用封装
3. **后端实现示例** - Node.js + Express的实现参考
4. **最佳实践指南** - 错误处理、性能优化、缓存策略等
5. **测试策略** - 单元测试和Mock数据示例

这样的文档可以帮助前后端开发人员快速理解和实现订阅中心的API对接工作。
