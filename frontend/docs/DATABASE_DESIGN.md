# 订阅中心数据库设计文档

## 📊 概述

本文档描述了订阅中心功能的数据库设计，包括表结构、关系设计、索引策略等。

## 🗄️ 数据库表结构

### 1. 用户表 (users)

```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    avatar VARCHAR(255),
    display_name VARCHAR(100),
    bio TEXT,
    preferences JSON,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_created_at (created_at)
);
```

### 2. 订阅源表 (subscription_sources)

```sql
CREATE TABLE subscription_sources (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HAR(200) NOT NULL,
    description TEXT,
    type ENUM('article', 'image', 'video', 'audio') NOT NULL,
    source_url VARCHAR(500),
    rss_url VARCHAR(500),
    api_endpoint VARCHAR(500),
    avatar VARCHAR(255),
    website VARCHAR(255),
    language VARCHAR(10) DEFAULT 'zh-CN',
    category VARCHAR(100),
    tags JSON,
    crawl_config JSON,
    is_active BOOLEAN DEFAULT TRUE,
    last_crawled_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_is_active (is_active),
    INDEX idx_last_crawled (last_crawled_at)
);
```

### 3. 用户订阅关系表 (user_subscriptions)

```sql
CREATE TABLE user_subscriptions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    source_id BIGINT NOT NULL,
    custom_name VARCHAR(200),
    notification_enabled BOOLEAN DEFAULT TRUE,
    auto_mark_read BOOLEAN DEFAULT FALSE,
    priority TINYINT DEFAULT 5, -- 1-10, 10为最高优先级
    tags JSON,
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP NULL,
    
    UNIQUE KEY uk_user_source (user_id, source_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (source_id) REFERENCES subscription_sources(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_source_id (source_id),
    INDEX idx_subscribed_at (subscribed_at)
);
```

### 4. 内容表 (contents)

```sql
CREATE TABLE contents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    source_id BIGINT NOT NULL,
    type ENUM('article', 'image', 'video', 'audio') NOT NULL,
    title VARCHAR(500) NOT NULL,
    summary TEXT,
    content LONGTEXT,
    author VARCHAR(200),
    original_url VARCHAR(1000),
    thumbnail VARCHAR(500),
    media_url VARCHAR(1000),
    media_size BIGINT,
    duration INT, -- 视频/音频时长(秒)
    dimensions JSON, -- 图片/视频尺寸 {"width": 1920, "height": 1080}
    metadata JSON, -- 其他元数据
    tags JSON,
    language VARCHAR(10) DEFAULT 'zh-CN',
    published_at TIMESTAMP,
    crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (source_id) REFERENCES subscription_sources(id) ON DELETE CASCADE,
    
    INDEX idx_source_id (source_id),
    INDEX idx_type (type),
    INDEX idx_published_at (published_at),
    INDEX idx_crawled_at (crawled_at),
    INDEX idx_is_active (is_active),
    FULLTEXT idx_title_content (title, summary, content)
);
```

### 5. 用户内容交互表 (user_content_interactions)

```sql
CREATE TABLE user_content_interactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    content_id BIGINT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    is_liked BOOLEAN DEFAULT FALSE,
    is_bookmarked BOOLEAN DEFAULT FALSE,
    is_shared BOOLEAN DEFAULT FALSE,
    read_progress DECIMAL(5,2) DEFAULT 0.00, -- 阅读/观看进度百分比
    read_time INT DEFAULT 0, -- 阅读/观看时间(秒)
    first_read_at TIMESTAMP NULL,
    last_read_at TIMESTAMP NULL,
    liked_at TIMESTAMP NULL,
    bookmarked_at TIMESTAMP NULL,
    shared_at TIMESTAMP NULL,
    
    UNIQUE KEY uk_user_content (user_id, content_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_content_id (content_id),
    INDEX idx_is_read (is_read),
    INDEX idx_is_liked (is_liked),
    INDEX idx_is_bookmarked (is_bookmarked),
    INDEX idx_last_read_at (last_read_at)
);
```

### 6. 评论表 (comments)

```sql
CREATE TABLE comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    content_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    parent_id BIGINT NULL, -- 回复评论的父评论ID
    content TEXT NOT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    likes_count INT DEFAULT 0,
    replies_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    
    INDEX idx_content_id (content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_created_at (created_at)
);
```

### 7. 评论点赞表 (comment_likes)

```sql
CREATE TABLE comment_likes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    comment_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_comment_user (comment_id, user_id),
    FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_comment_id (comment_id),
    INDEX idx_user_id (user_id)
);
```

### 8. 音频章节表 (audio_chapters)

```sql
CREATE TABLE audio_chapters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    content_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    start_time INT NOT NULL, -- 开始时间(秒)
    end_time INT, -- 结束时间(秒)
    description TEXT,
    order_index INT NOT NULL,
    
    FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE,
    
    INDEX idx_content_id (content_id),
    INDEX idx_order_index (order_index)
);
```

### 9. 通知表 (notifications)

```sql
CREATE TABLE notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    type ENUM('new_content', 'comment_reply', 'like', 'system') NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    data JSON, -- 相关数据
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);
```

### 10. 用户偏好设置表 (user_preferences)

```sql
CREATE TABLE user_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    email_notifications BOOLEAN DEFAULT TRUE,
    push_notifications BOOLEAN DEFAULT TRUE,
    notification_types JSON, -- 通知类型偏好
    quiet_hours JSON, -- 免打扰时间设置
    language VARCHAR(10) DEFAULT 'zh-CN',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    theme VARCHAR(20) DEFAULT 'light',
    auto_play_video BOOLEAN DEFAULT FALSE,
    auto_play_audio BOOLEAN DEFAULT FALSE,
    default_quality VARCHAR(20) DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 11. 统计表 (statistics)

```sql
CREATE TABLE statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    date DATE NOT NULL,
    metric_type ENUM('content_views', 'user_activity', 'subscription_growth') NOT NULL,
    source_id BIGINT NULL,
    user_id BIGINT NULL,
    value BIGINT NOT NULL,
    metadata JSON,
    
    UNIQUE KEY uk_date_type_source_user (date, metric_type, source_id, user_id),
    FOREIGN KEY (source_id) REFERENCES subscription_sources(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_date (date),
    INDEX idx_metric_type (metric_type),
    INDEX idx_source_id (source_id),
    INDEX idx_user_id (user_id)
);
```

## 🔗 关系设计说明

### 主要关系

1. **用户 ↔ 订阅源**: 多对多关系，通过 `user_subscriptions` 表关联
2. **订阅源 ↔ 内容**: 一对多关系，一个订阅源可以有多个内容
3. **用户 ↔ 内容**: 多对多关系，通过 `user_content_interactions` 表记录交互
4. **内容 ↔ 评论**: 一对多关系，一个内容可以有多个评论
5. **评论 ↔ 评论**: 自关联关系，支持评论回复
6. **音频内容 ↔ 章节**: 一对多关系，一个音频可以有多个章节

### 数据完整性

- 使用外键约束确保数据一致性
- 级联删除策略：删除用户时删除相关订阅和交互记录
- 软删除策略：评论使用 `is_deleted` 字段标记删除

## 📈 索引策略

### 主要索引

1. **查询优化索引**
   - 用户订阅查询：`(user_id, source_id)`
   - 内容列表查询：`(source_id, published_at)`
   - 用户交互查询：`(user_id, is_read, last_read_at)`

2. **全文搜索索引**
   - 内容搜索：`FULLTEXT(title, summary, content)`

3. **统计查询索引**
   - 时间范围查询：`(date, metric_type)`
   - 用户活跃度：`(user_id, date)`

## 🚀 性能优化建议

### 1. 分区策略

```sql
-- 按时间分区内容表
ALTER TABLE contents PARTITION BY RANGE (YEAR(published_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 按时间分区统计表
ALTER TABLE statistics PARTITION BY RANGE (YEAR(date)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 2. 读写分离

- 主库：处理写操作（订阅、点赞、评论等）
- 从库：处理读操作（内容列表、搜索等）
- 缓存：Redis 缓存热点数据

### 3. 数据归档

```sql
-- 创建归档表
CREATE TABLE contents_archive LIKE contents;
CREATE TABLE user_content_interactions_archive LIKE user_content_interactions;

-- 定期归档旧数据（保留最近1年的数据）
INSERT INTO contents_archive 
SELECT * FROM contents 
WHERE published_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);

DELETE FROM contents 
WHERE published_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

## 🔍 常用查询示例

### 1. 获取用户订阅列表

```sql
SELECT 
    ss.*,
    us.custom_name,
    us.notification_enabled,
    us.subscribed_at,
    COUNT(c.id) as total_content,
    COUNT(CASE WHEN uci.is_read = FALSE THEN 1 END) as unread_count
FROM subscription_sources ss
JOIN user_subscriptions us ON ss.id = us.source_id
LEFT JOIN contents c ON ss.id = c.source_id AND c.is_active = TRUE
LEFT JOIN user_content_interactions uci ON c.id = uci.content_id AND uci.user_id = us.user_id
WHERE us.user_id = ? AND ss.type = ?
GROUP BY ss.id, us.id
ORDER BY us.subscribed_at DESC;
```

### 2. 获取订阅内容列表

```sql
SELECT 
    c.*,
    COALESCE(uci.is_read, FALSE) as is_read,
    COALESCE(uci.is_liked, FALSE) as is_liked,
    COALESCE(uci.is_bookmarked, FALSE) as is_bookmarked,
    uci.read_progress
FROM contents c
LEFT JOIN user_content_interactions uci ON c.id = uci.content_id AND uci.user_id = ?
WHERE c.source_id = ? AND c.is_active = TRUE
ORDER BY c.published_at DESC
LIMIT ? OFFSET ?;
```

### 3. 搜索内容

```sql
SELECT 
    c.*,
    ss.name as source_name,
    MATCH(c.title, c.summary, c.content) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance
FROM contents c
JOIN subscription_sources ss ON c.source_id = ss.id
JOIN user_subscriptions us ON ss.id = us.source_id
WHERE us.user_id = ? 
    AND c.is_active = TRUE
    AND MATCH(c.title, c.summary, c.content) AGAINST(? IN NATURAL LANGUAGE MODE)
ORDER BY relevance DESC, c.published_at DESC
LIMIT ? OFFSET ?;
```

## 🛡️ 数据安全

### 1. 敏感数据加密

- 用户密码：使用 bcrypt 哈希
- 个人信息：考虑字段级加密
- API密钥：使用环境变量存储

### 2. 数据备份策略

- 每日全量备份
- 实时增量备份
- 异地备份存储

### 3. 访问控制

- 数据库用户权限最小化
- 应用层权限验证
- SQL注入防护

这个数据库设计文档提供了完整的订阅中心数据库架构，包括表结构、关系设计、性能优化和安全策略，可以作为后端开发的重要参考。
