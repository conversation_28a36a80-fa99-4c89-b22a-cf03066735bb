package com.jdl.aic.portal.service.log;

import com.jdl.aic.portal.dao.entity.LoginLog;
import com.jdl.aic.portal.dao.mapper.LoginLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

@Service
public class LoginLogService {
    
    @Autowired
    private LoginLogMapper loginLogMapper;
    
    /**
     * 记录登录日志
     */
    public void recordLoginLog(Long userId, String username, String loginType, String loginStatus, 
                              String failureReason, HttpServletRequest request) {
        LoginLog loginLog = new LoginLog();
        loginLog.setUserId(userId);
        loginLog.setUsername(username);
        loginLog.setLoginType(loginType);
        loginLog.setLoginStatus(loginStatus);
        loginLog.setFailureReason(failureReason);
        loginLog.setLoginTime(new Date());
        
        if (request != null) {
            loginLog.setIpAddress(getClientIpAddress(request));
            loginLog.setUserAgent(request.getHeader("User-Agent"));
        }
        
        loginLogMapper.insert(loginLog);
    }
    
    /**
     * 记录登录成功日志
     */
    public void recordLoginSuccess(Long userId, String username, String loginType, HttpServletRequest request) {
        recordLoginLog(userId, username, loginType, "success", null, request);
    }
    
    /**
     * 记录登录失败日志
     */
    public void recordLoginFailure(String username, String loginType, String failureReason, HttpServletRequest request) {
        recordLoginLog(null, username, loginType, "failure", failureReason, request);
    }
    
    /**
     * 获取用户登录日志
     */
    public List<LoginLog> getUserLoginLogs(Long userId) {
        return loginLogMapper.selectByUserId(userId);
    }
    
    /**
     * 获取最近的登录日志
     */
    public List<LoginLog> getRecentLoginLogs(Integer limit) {
        return loginLogMapper.selectRecentLogs(limit);
    }
    
    /**
     * 获取真实的客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}