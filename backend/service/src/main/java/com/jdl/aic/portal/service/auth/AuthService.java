package com.jdl.aic.portal.service.auth;

import com.jdl.aic.portal.common.dto.LoginRequest;
import com.jdl.aic.portal.common.dto.LoginResponse;
import com.jdl.aic.portal.common.dto.RegisterRequest;
import com.jdl.aic.portal.dao.entity.User;

import javax.servlet.http.HttpServletRequest;

public interface AuthService {
    
    LoginResponse login(LoginRequest loginRequest, HttpServletRequest request);
    
    LoginResponse register(RegisterRequest registerRequest, HttpServletRequest request);
    
    LoginResponse oauthLogin(String provider, String code, HttpServletRequest request);
    
    void logout(String token);
    
    User getCurrentUser(String token);
    
    LoginResponse refreshToken(String refreshToken);
}