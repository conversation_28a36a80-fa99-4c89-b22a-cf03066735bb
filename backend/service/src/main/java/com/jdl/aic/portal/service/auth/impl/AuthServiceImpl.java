package com.jdl.aic.portal.service.auth.impl;

import com.jdl.aic.portal.common.dto.LoginRequest;
import com.jdl.aic.portal.common.dto.LoginResponse;
import com.jdl.aic.portal.common.dto.RegisterRequest;
import com.jdl.aic.portal.common.exception.BusinessException;
import com.jdl.aic.portal.common.utils.JwtUtil;
import com.jdl.aic.portal.common.utils.PasswordUtil;
import com.jdl.aic.portal.dao.entity.User;
import com.jdl.aic.portal.dao.mapper.UserMapper;
import com.jdl.aic.portal.service.auth.AuthService;
import com.jdl.aic.portal.service.oauth.OAuth2Service;
import com.jdl.aic.portal.service.log.LoginLogService;
import com.jdl.aic.portal.service.jwt.JwtTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordUtil passwordUtil;

    @Autowired
    private OAuth2Service oAuth2Service;

    @Autowired
    private LoginLogService loginLogService;

    @Autowired
    private JwtTokenService jwtTokenService;

    @Override
    public LoginResponse login(LoginRequest loginRequest, HttpServletRequest request) {
        String username = loginRequest.getUsername();
        String password = loginRequest.getPassword();

        if (username == null || username.trim().isEmpty()) {
            loginLogService.recordLoginFailure(username, "local", "用户名不能为空", request);
            throw new BusinessException("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            loginLogService.recordLoginFailure(username, "local", "密码不能为空", request);
            throw new BusinessException("密码不能为空");
        }

        User user = userMapper.selectByUsername(username);
        if (user == null) {
            user = userMapper.selectByEmail(username);
        }

        if (user == null) {
            loginLogService.recordLoginFailure(username, "local", "用户不存在", request);
            throw new BusinessException("用户名或密码错误");
        }

        if (!user.getEnabled()) {
            loginLogService.recordLoginFailure(username, "local", "用户被禁用", request);
            throw new BusinessException("用户已被禁用");
        }

        if (!passwordUtil.matches(password, user.getPassword())) {
            loginLogService.recordLoginFailure(username, "local", "密码错误", request);
            throw new BusinessException("用户名或密码错误");
        }

        // 更新最后登录时间
        userMapper.updateLastLoginTime(user.getId());

        // 生成JWT token并记录到数据库
        JwtUtil.TokenInfo tokenInfo = jwtUtil.generateTokenWithId(user.getUsername(), user.getId());
        jwtTokenService.saveToken(user.getId(), tokenInfo.getTokenId(), tokenInfo.getToken(), 
                                  tokenInfo.getExpiration(), request);

        // 限制用户并发登录数量（可选，比如限制最多5个设备同时登录）
        jwtTokenService.limitUserConcurrentSessions(user.getId(), 5);

        // 记录登录成功日志
        loginLogService.recordLoginSuccess(user.getId(), user.getUsername(), "local", request);

        return new LoginResponse(tokenInfo.getToken(), user.getUsername(), user.getNickname(), user.getEmail(), user.getAvatar());
    }

    @Override
    @Transactional
    public LoginResponse register(RegisterRequest registerRequest, HttpServletRequest request) {
        String username = registerRequest.getUsername();
        String password = registerRequest.getPassword();
        String email = registerRequest.getEmail();
        String nickname = registerRequest.getNickname();

        if (username == null || username.trim().isEmpty()) {
            throw new BusinessException("用户名不能为空");
        }
        if (password == null || password.length() < 6) {
            throw new BusinessException("密码不能少于6位");
        }
        if (email == null || email.trim().isEmpty()) {
            throw new BusinessException("邮箱不能为空");
        }

        User existingUser = userMapper.selectByUsername(username);
        if (existingUser != null) {
            throw new BusinessException("用户名已存在");
        }

        existingUser = userMapper.selectByEmail(email);
        if (existingUser != null) {
            throw new BusinessException("邮箱已被注册");
        }

        User user = new User();
        user.setUsername(username);
        user.setPassword(passwordUtil.encode(password));
        user.setEmail(email);
        user.setNickname(nickname != null ? nickname : username);
        user.setProvider("local");
        user.setEnabled(true);
        user.setLastLoginTime(new Date());

        int result = userMapper.insert(user);
        if (result <= 0) {
            throw new BusinessException("注册失败");
        }

        // 生成JWT token并记录到数据库
        JwtUtil.TokenInfo tokenInfo = jwtUtil.generateTokenWithId(user.getUsername(), user.getId());
        jwtTokenService.saveToken(user.getId(), tokenInfo.getTokenId(), tokenInfo.getToken(), 
                                  tokenInfo.getExpiration(), request);
        
        // 记录注册成功日志
        loginLogService.recordLoginSuccess(user.getId(), user.getUsername(), "local", request);
        
        return new LoginResponse(tokenInfo.getToken(), user.getUsername(), user.getNickname(), user.getEmail(), user.getAvatar());
    }

    @Override
    @Transactional
    public LoginResponse oauthLogin(String provider, String code, HttpServletRequest request) {
        try {
            LoginResponse response;
            switch (provider) {
                case "google":
                    response = oAuth2Service.handleGoogleCallback(code);
                    break;
                case "github":
                    response = oAuth2Service.handleGithubCallback(code);
                    break;
                default:
                    throw new BusinessException("不支持的OAuth提供商: " + provider);
            }
            
            // 记录OAuth登录成功日志
            String username = response.getUsername();
            if (username != null) {
                // 需要通过用户名查找用户ID
                User user = userMapper.selectByUsername(username);
                if (user != null) {
                    loginLogService.recordLoginSuccess(user.getId(), username, provider, request);
                }
            }
            
            return response;
        } catch (Exception e) {
            // 记录OAuth登录失败日志
            loginLogService.recordLoginFailure(null, provider, e.getMessage(), request);
            throw e;
        }
    }
    
    @Override
    public void logout(String token) {
        if (token == null || token.trim().isEmpty()) {
            return;
        }

        try {
            // 从token中获取tokenId
            String tokenId = jwtUtil.getTokenIdFromToken(token);
            if (tokenId != null) {
                // 撤销token
                jwtTokenService.revokeToken(tokenId);
            }
        } catch (Exception e) {
            // 忽略解析错误，可能是旧的token格式
        }
    }

    @Override
    public User getCurrentUser(String token) {
        if (token == null || token.trim().isEmpty()) {
            throw new BusinessException("Token不能为空");
        }

        if (!jwtUtil.validateToken(token)) {
            throw new BusinessException("Token无效或已过期");
        }

        // 检查token是否在数据库中有效
        try {
            String tokenId = jwtUtil.getTokenIdFromToken(token);
            if (tokenId != null && !jwtTokenService.isTokenValid(tokenId)) {
                throw new BusinessException("Token已被撤销");
            }
        } catch (Exception e) {
            // 如果是旧的token格式（没有tokenId），则继续使用JWT验证
        }

        String username = jwtUtil.getUsernameFromToken(token);
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        return user;
    }

    @Override
    public LoginResponse refreshToken(String refreshToken) {
        if (refreshToken == null || refreshToken.trim().isEmpty()) {
            throw new BusinessException("刷新Token不能为空");
        }

        if (!jwtUtil.validateToken(refreshToken)) {
            throw new BusinessException("刷新Token无效或已过期");
        }

        String username = jwtUtil.getUsernameFromToken(refreshToken);
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 生成新的Token并记录到数据库
        JwtUtil.TokenInfo tokenInfo = jwtUtil.generateTokenWithId(user.getUsername(), user.getId());
        // 注意：这里需要HttpServletRequest，但refreshToken方法没有提供，这是一个设计问题
        // 暂时传null，实际项目中应该修改方法签名
        jwtTokenService.saveToken(user.getId(), tokenInfo.getTokenId(), tokenInfo.getToken(), 
                                  tokenInfo.getExpiration(), null);

        return new LoginResponse(tokenInfo.getToken(), user.getUsername(), user.getNickname(), user.getEmail(), user.getAvatar());
    }
}