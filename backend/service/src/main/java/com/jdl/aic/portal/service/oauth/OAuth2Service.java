package com.jdl.aic.portal.service.oauth;

import com.jdl.aic.portal.common.config.OAuthProperties;
import com.jdl.aic.portal.common.dto.LoginResponse;
import com.jdl.aic.portal.common.exception.BusinessException;
import com.jdl.aic.portal.common.utils.JwtUtil;
import com.jdl.aic.portal.dao.entity.User;
import com.jdl.aic.portal.dao.mapper.UserMapper;
import com.jdl.aic.portal.service.jwt.JwtTokenService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Date;

@Service
public class OAuth2Service {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private OAuthProperties oAuthProperties;

    @Autowired
    private JwtTokenService jwtTokenService;

    public LoginResponse handleGoogleCallback(String code) {
        try {
            String accessToken = getGoogleAccessToken(code);
            JsonNode userInfo = getGoogleUserInfo(accessToken);
            
            // Debug: 打印完整的Google用户信息
            System.out.println("Google OAuth Response: " + userInfo.toString());
            
            String providerId = userInfo.get("id") != null ? userInfo.get("id").asText() : null;
            String email = userInfo.get("email") != null ? userInfo.get("email").asText() : null;
            String name = userInfo.get("name") != null && !userInfo.get("name").isNull() ? userInfo.get("name").asText() : email;
            String picture = userInfo.get("picture") != null ? userInfo.get("picture").asText() : null;
            
            System.out.println("Parsed values - providerId: " + providerId + ", email: " + email + ", name: " + name);
            
            if (providerId == null) {
                throw new BusinessException("无法获取Google用户ID");
            }
            if (email == null) {
                throw new BusinessException("无法获取Google用户邮箱");
            }
            
            User user = userMapper.selectByProviderAndProviderId("google", providerId);
            if (user == null) {
                user = userMapper.selectByEmail(email);
                if (user == null) {
                    user = new User();
                    user.setUsername(email);
                    user.setEmail(email);
                    user.setNickname(name);
                    user.setAvatar(picture);
                    user.setProvider("google");
                    user.setProviderId(providerId);
                    user.setEnabled(true);
                    user.setLastLoginTime(new Date());
                    userMapper.insert(user);
                } else {
                    user.setProvider("google");
                    user.setProviderId(providerId);
                    user.setAvatar(picture);
                    user.setLastLoginTime(new Date());
                    userMapper.updateById(user);
                }
            } else {
                userMapper.updateLastLoginTime(user.getId());
            }
            
            // 生成JWT token并记录到数据库
            JwtUtil.TokenInfo tokenInfo = jwtUtil.generateTokenWithId(user.getUsername(), user.getId());
            jwtTokenService.saveToken(user.getId(), tokenInfo.getTokenId(), tokenInfo.getToken(), 
                                      tokenInfo.getExpiration(), null);
            
            return new LoginResponse(tokenInfo.getToken(), user.getUsername(), user.getNickname(), user.getEmail(), user.getAvatar());
            
        } catch (Exception e) {
            throw new BusinessException("Google登录失败: " + e.getMessage());
        }
    }

    public LoginResponse handleGithubCallback(String code) {
        try {
            String accessToken = getGithubAccessToken(code);
            JsonNode userInfo = getGithubUserInfo(accessToken);
            
            String providerId = userInfo.get("id").asText();
            String login = userInfo.get("login").asText();
            String name = userInfo.get("name") != null && !userInfo.get("name").isNull() ? userInfo.get("name").asText() : login;
            String avatar = userInfo.get("avatar_url").asText();
            
            JsonNode emails = getGithubUserEmails(accessToken);
            String email = null;
            if (emails.isArray() && emails.size() > 0) {
                for (JsonNode emailNode : emails) {
                    if (emailNode.get("primary").asBoolean()) {
                        email = emailNode.get("email").asText();
                        break;
                    }
                }
                if (email == null) {
                    email = emails.get(0).get("email").asText();
                }
            }
            
            User user = userMapper.selectByProviderAndProviderId("github", providerId);
            if (user == null) {
                user = userMapper.selectByEmail(email);
                if (user == null) {
                    user = new User();
                    user.setUsername(login);
                    user.setEmail(email);
                    user.setNickname(name);
                    user.setAvatar(avatar);
                    user.setProvider("github");
                    user.setProviderId(providerId);
                    user.setEnabled(true);
                    user.setLastLoginTime(new Date());
                    userMapper.insert(user);
                } else {
                    user.setProvider("github");
                    user.setProviderId(providerId);
                    user.setAvatar(avatar);
                    user.setLastLoginTime(new Date());
                    userMapper.updateById(user);
                }
            } else {
                userMapper.updateLastLoginTime(user.getId());
            }
            
            // 生成JWT token并记录到数据库
            JwtUtil.TokenInfo tokenInfo = jwtUtil.generateTokenWithId(user.getUsername(), user.getId());
            jwtTokenService.saveToken(user.getId(), tokenInfo.getTokenId(), tokenInfo.getToken(), 
                                      tokenInfo.getExpiration(), null);
            
            return new LoginResponse(tokenInfo.getToken(), user.getUsername(), user.getNickname(), user.getEmail(), user.getAvatar());
            
        } catch (Exception e) {
            throw new BusinessException("GitHub登录失败: " + e.getMessage());
        }
    }

    private String getGoogleAccessToken(String code) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("client_id", oAuthProperties.getGoogle().getClientId());
        params.add("client_secret", oAuthProperties.getGoogle().getClientSecret());
        params.add("code", code);
        params.add("grant_type", "authorization_code");
        params.add("redirect_uri", oAuthProperties.getGoogle().getRedirectUri());
        
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(
            "https://oauth2.googleapis.com/token", request, String.class);
        
        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.readTree(response.getBody());
        return jsonNode.get("access_token").asText();
    }

    private JsonNode getGoogleUserInfo(String accessToken) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        
        HttpEntity<String> request = new HttpEntity<>(headers);
        
        ResponseEntity<String> response = restTemplate.exchange(
            "https://www.googleapis.com/oauth2/v2/userinfo", 
            HttpMethod.GET, request, String.class);
        
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readTree(response.getBody());
    }

    private String getGithubAccessToken(String code) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Accept", "application/json");
        
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("client_id", oAuthProperties.getGithub().getClientId());
        params.add("client_secret", oAuthProperties.getGithub().getClientSecret());
        params.add("code", code);
        
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(
            "https://github.com/login/oauth/access_token", request, String.class);
        
        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.readTree(response.getBody());
        return jsonNode.get("access_token").asText();
    }

    private JsonNode getGithubUserInfo(String accessToken) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        
        HttpEntity<String> request = new HttpEntity<>(headers);
        
        ResponseEntity<String> response = restTemplate.exchange(
            "https://api.github.com/user", 
            HttpMethod.GET, request, String.class);
        
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readTree(response.getBody());
    }

    private JsonNode getGithubUserEmails(String accessToken) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        
        HttpEntity<String> request = new HttpEntity<>(headers);
        
        ResponseEntity<String> response = restTemplate.exchange(
            "https://api.github.com/user/emails", 
            HttpMethod.GET, request, String.class);
        
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readTree(response.getBody());
    }
}