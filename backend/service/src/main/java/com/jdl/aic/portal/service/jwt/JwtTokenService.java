package com.jdl.aic.portal.service.jwt;

import com.jdl.aic.portal.dao.entity.JwtToken;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface JwtTokenService {
    
    /**
     * 记录新的JWT Token
     */
    void saveToken(Long userId, String tokenId, String tokenValue, long expirationTime, HttpServletRequest request);
    
    /**
     * 验证Token是否有效
     */
    boolean isTokenValid(String tokenId);
    
    /**
     * 撤销Token
     */
    void revokeToken(String tokenId);
    
    /**
     * 撤销用户的所有Token（用于退出登录）
     */
    void revokeAllUserTokens(Long userId);
    
    /**
     * 获取用户的有效Token列表
     */
    List<JwtToken> getUserValidTokens(Long userId);
    
    /**
     * 清理过期Token
     */
    void cleanExpiredTokens();
    
    /**
     * 限制用户同时登录设备数量
     */
    void limitUserConcurrentSessions(Long userId, int maxSessions);
    
    /**
     * 获取设备信息
     */
    String getDeviceInfo(HttpServletRequest request);
}