package com.jdl.aic.portal.service.jwt.impl;

import com.jdl.aic.portal.dao.entity.JwtToken;
import com.jdl.aic.portal.dao.mapper.JwtTokenMapper;
import com.jdl.aic.portal.service.jwt.JwtTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class JwtTokenServiceImpl implements JwtTokenService {
    
    @Autowired
    private JwtTokenMapper jwtTokenMapper;
    
    @Override
    public void saveToken(Long userId, String tokenId, String tokenValue, long expirationTime, HttpServletRequest request) {
        String deviceInfo = request != null ? getDeviceInfo(request) : "Unknown Device";
        String ipAddress = request != null ? getClientIpAddress(request) : "Unknown IP";
        
        Date expiresAt = new Date(System.currentTimeMillis() + expirationTime * 1000);
        
        JwtToken jwtToken = new JwtToken(
            userId, 
            tokenId, 
            "access", 
            tokenValue, 
            expiresAt, 
            deviceInfo, 
            ipAddress
        );
        
        jwtTokenMapper.insert(jwtToken);
    }
    
    @Override
    public boolean isTokenValid(String tokenId) {
        JwtToken token = jwtTokenMapper.selectByTokenId(tokenId);
        
        if (token == null) {
            return false;
        }
        
        // 检查是否已撤销
        if (token.getIsRevoked()) {
            return false;
        }
        
        // 检查是否过期
        if (token.getExpiresAt().before(new Date())) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public void revokeToken(String tokenId) {
        jwtTokenMapper.revokeToken(tokenId);
    }
    
    @Override
    public void revokeAllUserTokens(Long userId) {
        jwtTokenMapper.revokeAllTokensByUserId(userId);
    }
    
    @Override
    public List<JwtToken> getUserValidTokens(Long userId) {
        return jwtTokenMapper.selectValidTokensByUserId(userId);
    }
    
    @Override
    public void cleanExpiredTokens() {
        jwtTokenMapper.deleteExpiredTokens();
    }
    
    @Override
    public void limitUserConcurrentSessions(Long userId, int maxSessions) {
        jwtTokenMapper.revokeOldestTokensByUserId(userId, maxSessions);
    }
    
    @Override
    public String getDeviceInfo(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            return "Unknown Device";
        }
        
        // 简单的设备信息解析
        StringBuilder deviceInfo = new StringBuilder();
        
        if (userAgent.contains("Mobile")) {
            deviceInfo.append("Mobile ");
        } else if (userAgent.contains("Tablet")) {
            deviceInfo.append("Tablet ");
        } else {
            deviceInfo.append("Desktop ");
        }
        
        if (userAgent.contains("Chrome")) {
            deviceInfo.append("Chrome");
        } else if (userAgent.contains("Firefox")) {
            deviceInfo.append("Firefox");
        } else if (userAgent.contains("Safari")) {
            deviceInfo.append("Safari");
        } else if (userAgent.contains("Edge")) {
            deviceInfo.append("Edge");
        } else {
            deviceInfo.append("Unknown Browser");
        }
        
        return deviceInfo.toString();
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}