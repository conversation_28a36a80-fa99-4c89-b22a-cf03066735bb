package com.jdl.aic.portal.service.task;

import com.jdl.aic.portal.service.jwt.JwtTokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class TokenCleanupTask {
    
    private static final Logger logger = LoggerFactory.getLogger(TokenCleanupTask.class);
    
    @Autowired
    private JwtTokenService jwtTokenService;
    
    /**
     * 每天凌晨2点清理过期的Token
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredTokens() {
        logger.info("开始清理过期的JWT Token...");
        
        try {
            jwtTokenService.cleanExpiredTokens();
            logger.info("过期JWT Token清理完成");
        } catch (Exception e) {
            logger.error("清理过期JWT Token时发生错误", e);
        }
    }
}