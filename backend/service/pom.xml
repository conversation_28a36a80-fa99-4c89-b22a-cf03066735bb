<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>aic-portal-backend</artifactId>
        <groupId>com.jdl.aic.portal</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.jdl.aic.portal</groupId>
            <artifactId>common</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.jdl.aic.portal</groupId>
            <artifactId>dao</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
            <version>1.7.8</version>
        </dependency>
        <dependency>
            <groupId>com.jdl.aic.core</groupId>
            <artifactId>aic-client</artifactId>
        </dependency>
    </dependencies>
</project>