package com.jdl.aic.portal.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class OAuthConfigurationValidator implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(OAuthConfigurationValidator.class);

    @Autowired
    private OAuthProperties oAuthProperties;

    @Override
    public void run(String... args) {
        validateOAuthConfiguration();
    }

    private void validateOAuthConfiguration() {
        logger.info("🔍 验证OAuth配置...");

        // 验证Google OAuth配置
        OAuthProperties.GoogleOAuth google = oAuthProperties.getGoogle();
        if (isConfigValid(google.getClientId(), google.getClientSecret(), google.getRedirectUri())) {
            logger.info("✅ Google OAuth配置有效");
            logger.debug("Google redirect URI: {}", google.getRedirectUri());
        } else {
            logger.warn("⚠️  Google OAuth配置不完整，请检查application.yml中的oauth.google配置");
        }

        // 验证GitHub OAuth配置
        OAuthProperties.GithubOAuth github = oAuthProperties.getGithub();
        if (isConfigValid(github.getClientId(), github.getClientSecret(), github.getRedirectUri())) {
            logger.info("✅ GitHub OAuth配置有效");
            logger.debug("GitHub redirect URI: {}", github.getRedirectUri());
        } else {
            logger.warn("⚠️  GitHub OAuth配置不完整，请检查application.yml中的oauth.github配置");
        }

        logger.info("🔧 OAuth配置验证完成");
    }

    private boolean isConfigValid(String clientId, String clientSecret, String redirectUri) {
        return clientId != null && !clientId.isEmpty() && !clientId.startsWith("YOUR_") &&
               clientSecret != null && !clientSecret.isEmpty() && !clientSecret.startsWith("YOUR_") &&
               redirectUri != null && !redirectUri.isEmpty();
    }
}