package com.jdl.aic.portal.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "oauth")
public class OAuthProperties {
    
    private GoogleOAuth google = new GoogleOAuth();
    private GithubOAuth github = new GithubOAuth();
    
    public GoogleOAuth getGoogle() {
        return google;
    }
    
    public void setGoogle(GoogleOAuth google) {
        this.google = google;
    }
    
    public GithubOAuth getGithub() {
        return github;
    }
    
    public void setGithub(GithubOAuth github) {
        this.github = github;
    }
    
    public static class GoogleOAuth {
        private String clientId;
        private String clientSecret;
        private String redirectUri;
        
        public String getClientId() {
            return clientId;
        }
        
        public void setClientId(String clientId) {
            this.clientId = clientId;
        }
        
        public String getClientSecret() {
            return clientSecret;
        }
        
        public void setClientSecret(String clientSecret) {
            this.clientSecret = clientSecret;
        }
        
        public String getRedirectUri() {
            return redirectUri;
        }
        
        public void setRedirectUri(String redirectUri) {
            this.redirectUri = redirectUri;
        }
        
        public String getAuthUrl() {
            return "https://accounts.google.com/o/oauth2/v2/auth" +
                   "?client_id=" + clientId +
                   "&redirect_uri=" + redirectUri +
                   "&response_type=code" +
                   "&scope=openid%20email%20profile" +
                   "&prompt=consent" +
                   "&access_type=offline";
        }
    }
    
    public static class GithubOAuth {
        private String clientId;
        private String clientSecret;
        private String redirectUri;
        
        public String getClientId() {
            return clientId;
        }
        
        public void setClientId(String clientId) {
            this.clientId = clientId;
        }
        
        public String getClientSecret() {
            return clientSecret;
        }
        
        public void setClientSecret(String clientSecret) {
            this.clientSecret = clientSecret;
        }
        
        public String getRedirectUri() {
            return redirectUri;
        }
        
        public void setRedirectUri(String redirectUri) {
            this.redirectUri = redirectUri;
        }
        
        public String getAuthUrl() {
            return "https://github.com/login/oauth/authorize" +
                   "?client_id=" + clientId +
                   "&redirect_uri=" + redirectUri +
                   "&scope=user:email" +
                   "&prompt=consent" +
                   "&state=" + System.currentTimeMillis();
        }
    }
}