package com.jdl.aic.portal.web.controller.auth;

import com.jdl.aic.portal.common.config.OAuthProperties;
import com.jdl.aic.portal.common.dto.LoginRequest;
import com.jdl.aic.portal.common.dto.LoginResponse;
import com.jdl.aic.portal.common.dto.RegisterRequest;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.dao.entity.User;
import com.jdl.aic.portal.service.auth.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
public class AuthController {

    @Autowired
    private AuthService authService;
    
    @Autowired
    private OAuthProperties oAuthProperties;

    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody LoginRequest loginRequest, HttpServletRequest request) {
        LoginResponse response = authService.login(loginRequest, request);
        return Result.success(response);
    }

    @PostMapping("/register")
    public Result<LoginResponse> register(@RequestBody RegisterRequest registerRequest, HttpServletRequest request) {
        LoginResponse response = authService.register(registerRequest, request);
        return Result.success(response);
    }

    @PostMapping("/logout")
    public Result<Void> logout(@RequestHeader("Authorization") String token) {
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        authService.logout(token);
        return Result.success();
    }

    @GetMapping("/user")
    public Result<User> getCurrentUser(@RequestHeader("Authorization") String token) {
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        User user = authService.getCurrentUser(token);
        return Result.success(user);
    }

    @PostMapping("/refresh")
    public Result<LoginResponse> refreshToken(@RequestHeader("Authorization") String refreshToken) {
        if (refreshToken != null && refreshToken.startsWith("Bearer ")) {
            refreshToken = refreshToken.substring(7);
        }
        LoginResponse response = authService.refreshToken(refreshToken);
        return Result.success(response);
    }

    @GetMapping("/oauth/{provider}")
    public Result<String> getOAuthUrl(@PathVariable String provider) {
        String authUrl;
        switch (provider) {
            case "google":
                authUrl = oAuthProperties.getGoogle().getAuthUrl();
                break;
            case "github":
                authUrl = oAuthProperties.getGithub().getAuthUrl();
                break;
            default:
                return Result.error("不支持的OAuth提供商");
        }
        return Result.success(authUrl);
    }

    @GetMapping("/oauth/{provider}/callback")
    public void oauthCallback(@PathVariable String provider, @RequestParam String code, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            LoginResponse loginResponse = authService.oauthLogin(provider, code, request);
            
            // 生成JWT token
            String token = loginResponse.getToken();
            
            // 重定向到前端，带上token
            String redirectUrl = "http://localhost:4000/auth/callback/" + provider 
                + "?token=" + token 
                + "&success=true";
            
            response.sendRedirect(redirectUrl);
        } catch (Exception e) {
            // 错误时重定向到登录页面
            String redirectUrl = "http://localhost:4000/login?error=oauth_failed&provider=" + provider;
            response.sendRedirect(redirectUrl);
        }
    }
}