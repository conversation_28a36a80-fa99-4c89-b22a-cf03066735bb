package com.jdl.aic.portal.space.dto;

import java.util.Date;

/**
 * 团队推荐实体类
 */
public class TeamRecommendation {
    private Long id;
    private Long teamId;
    private Long contentId;
    private Long recommenderId;
    private String recommenderName;
    private String recommenderAvatar;
    private Date recommendedAt;
    private String reason;
    private Date createTime;
    private Date updateTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public Long getRecommenderId() {
        return recommenderId;
    }

    public void setRecommenderId(Long recommenderId) {
        this.recommenderId = recommenderId;
    }

    public String getRecommenderName() {
        return recommenderName;
    }

    public void setRecommenderName(String recommenderName) {
        this.recommenderName = recommenderName;
    }

    public String getRecommenderAvatar() {
        return recommenderAvatar;
    }

    public void setRecommenderAvatar(String recommenderAvatar) {
        this.recommenderAvatar = recommenderAvatar;
    }

    public Date getRecommendedAt() {
        return recommendedAt;
    }

    public void setRecommendedAt(Date recommendedAt) {
        this.recommendedAt = recommendedAt;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
