package com.jdl.aic.portal.recommendation.controller;

import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.recommendation.service.RecommendationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 推荐领域控制器
 * 
 * <AUTHOR> Portal Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/recommendations")
@CrossOrigin(origins = "*")
public class RecommendationController {

    @Autowired
    private RecommendationService recommendationService;

    /**
     * 推荐内容到团队
     */
    @PostMapping
    public Result<Boolean> recommendContents(@RequestBody Map<String, Object> request) {
        try {
            List<Long> teamIds = (List<Long>) request.get("teamIds");
            List<Long> contentIds = (List<Long>) request.get("contentIds");
            String reason = (String) request.get("reason");
            
            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long recommenderId = 1L;

            boolean result = recommendationService.recommendContentsToTeams(teamIds, contentIds, recommenderId, reason);
            return Result.success(result, "推荐成功");
        } catch (Exception e) {
            return Result.error("推荐失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队推荐内容列表
     */
    @GetMapping("/teams/{teamId}")
    public Result<Map<String, Object>> getTeamRecommendations(
            @PathVariable Long teamId,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        try {
            Map<String, Object> result = recommendationService.getTeamRecommendations(teamId, knowledgeTypeCode, page, pageSize);
            return Result.success(result, "获取团队推荐内容成功");
        } catch (Exception e) {
            return Result.error("获取团队推荐内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户推荐历史
     */
    @GetMapping("/users/{userId}/history")
    public Result<Map<String, Object>> getUserRecommendationHistory(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        try {
            Map<String, Object> result = recommendationService.getUserRecommendationHistory(userId, page, pageSize);
            return Result.success(result, "获取用户推荐历史成功");
        } catch (Exception e) {
            return Result.error("获取用户推荐历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐统计
     */
    @GetMapping("/teams/{teamId}/statistics")
    public Result<Map<String, Object>> getRecommendationStatistics(
            @PathVariable Long teamId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            Map<String, Object> result = recommendationService.getRecommendationStatistics(teamId, startDate, endDate);
            return Result.success(result, "获取推荐统计成功");
        } catch (Exception e) {
            return Result.error("获取推荐统计失败: " + e.getMessage());
        }
    }

    /**
     * 删除推荐
     */
    @DeleteMapping("/{recommendationId}")
    public Result<Boolean> deleteRecommendation(@PathVariable Long recommendationId) {
        try {
            // TODO: 从认证信息中获取当前用户ID
            Long userId = 1L;
            
            boolean result = recommendationService.deleteRecommendation(recommendationId, userId);
            return Result.success(result, "删除推荐成功");
        } catch (Exception e) {
            return Result.error("删除推荐失败: " + e.getMessage());
        }
    }

    /**
     * 获取个性化推荐
     */
    @GetMapping("/personalized/{userId}")
    public Result<List<Map<String, Object>>> getPersonalizedRecommendations(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            List<Map<String, Object>> result = recommendationService.getPersonalizedRecommendations(userId, limit);
            return Result.success(result, "获取个性化推荐成功");
        } catch (Exception e) {
            return Result.error("获取个性化推荐失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门推荐
     */
    @GetMapping("/popular")
    public Result<List<Map<String, Object>>> getPopularRecommendations(
            @RequestParam(required = false) Long teamId,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            List<Map<String, Object>> result = recommendationService.getPopularRecommendations(teamId, limit);
            return Result.success(result, "获取热门推荐成功");
        } catch (Exception e) {
            return Result.error("获取热门推荐失败: " + e.getMessage());
        }
    }

    /**
     * 标记推荐为已读
     */
    @PutMapping("/{recommendationId}/read")
    public Result<Boolean> markAsRead(@PathVariable Long recommendationId) {
        try {
            // TODO: 从认证信息中获取当前用户ID
            Long userId = 1L;
            
            boolean result = recommendationService.markRecommendationAsRead(recommendationId, userId);
            return Result.success(result, "标记为已读成功");
        } catch (Exception e) {
            return Result.error("标记为已读失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐详情
     */
    @GetMapping("/{recommendationId}")
    public Result<Map<String, Object>> getRecommendationDetail(@PathVariable Long recommendationId) {
        try {
            Map<String, Object> result = recommendationService.getRecommendationDetail(recommendationId);
            if (result != null) {
                return Result.success(result, "获取推荐详情成功");
            } else {
                return Result.error("推荐不存在");
            }
        } catch (Exception e) {
            return Result.error("获取推荐详情失败: " + e.getMessage());
        }
    }
}
