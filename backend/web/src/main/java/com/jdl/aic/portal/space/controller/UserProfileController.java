package com.jdl.aic.portal.space.controller;

import com.jdl.aic.portal.space.dto.TeamByUserResult;
import com.jdl.aic.portal.space.dto.UserProfileDTO;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.space.service.UserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户个人空间控制器
 */
@RestController
@RequestMapping("/api/v1/users")
@CrossOrigin(origins = "*")
public class UserProfileController {

    @Autowired
    private UserProfileService userProfileService;

    /**
     * 获取用户完整信息
     */
    @GetMapping("/{userId}/profile")
    public Result<UserProfileDTO> getUserProfile(@PathVariable Long userId) {
        try {
            UserProfileDTO userProfile = userProfileService.getUserProfile(userId);
            return Result.success(userProfile, "获取用户信息成功");
        } catch (Exception e) {
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/{userId}/profile")
    public Result<UserProfileDTO> updateUserProfile(@PathVariable Long userId, @RequestBody Map<String, Object> profileData) {
        try {
            UserProfileDTO updatedProfile = userProfileService.updateUserProfile(userId, profileData);
            return Result.success(updatedProfile, "更新用户资料成功");
        } catch (Exception e) {
            return Result.error("更新用户资料失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户关联的内容列表
     */
    @GetMapping("/{userId}/contents")
    public Result<Map<String, Object>> getUserContents(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "published") String associationType,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        try {
            // 验证关联类型
            if (!isValidAssociationType(associationType)) {
                return Result.error("关联类型无效");
            }

            Map<String, Object> result = userProfileService.getUserContents(userId, associationType, knowledgeTypeCode, page, pageSize);
            return Result.success(result, "获取用户内容成功");
        } catch (Exception e) {
            return Result.error("获取用户内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户加入的团队列表
     */
    @GetMapping("/{userId}/teams")
    public Result<List<TeamByUserResult>> getUserTeams(@PathVariable Long userId) {
        try {
            List<TeamByUserResult> teams = userProfileService.getUserTeams(userId);
            System.out.println("Controller: 获取到用户团队数据，userId=" + userId + ", teamsCount=" +
                (teams != null ? teams.size() : 0));
            return Result.success(teams, "获取用户团队列表成功");
        } catch (Exception e) {
            System.err.println("Controller: 获取用户团队失败，userId=" + userId + ", error=" + e.getMessage());
            e.printStackTrace();
            return Result.error("获取用户团队列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户学习信息
     */
    @GetMapping("/{userId}/learnings")
    public Result<Map<String, Object>> getUserLearnings(@PathVariable Long userId) {
        try {
            Map<String, Object> learnings = userProfileService.getUserLearnings(userId);
            return Result.success(learnings, "获取用户学习信息成功");
        } catch (Exception e) {
            return Result.error("获取用户学习信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐给用户的团队列表
     */
    @GetMapping("/{userId}/recommended-teams")
    public Result<List<Map<String, Object>>> getRecommendedTeams(@PathVariable Long userId,
                                                                @RequestParam(defaultValue = "6") Integer limit) {
        try {
            // TODO: 实现推荐团队逻辑，目前返回空列表
            List<Map<String, Object>> recommendedTeams = new ArrayList<>();
            System.out.println("Controller: 获取推荐团队，userId=" + userId + ", limit=" + limit);
            return Result.success(recommendedTeams, "获取推荐团队成功");
        } catch (Exception e) {
            System.err.println("Controller: 获取推荐团队失败，userId=" + userId + ", error=" + e.getMessage());
            return Result.error("获取推荐团队失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户团队活动记录
     */
    @GetMapping("/{userId}/team-activities")
    public Result<List<Map<String, Object>>> getUserTeamActivities(@PathVariable Long userId,
                                                                  @RequestParam(defaultValue = "5") Integer limit) {
        try {
            // TODO: 实现团队活动记录逻辑，目前返回空列表
            List<Map<String, Object>> activities = new ArrayList<>();
            System.out.println("Controller: 获取团队活动，userId=" + userId + ", limit=" + limit);
            return Result.success(activities, "获取团队活动成功");
        } catch (Exception e) {
            System.err.println("Controller: 获取团队活动失败，userId=" + userId + ", error=" + e.getMessage());
            return Result.error("获取团队活动失败: " + e.getMessage());
        }
    }

    /**
     * 验证关联类型是否有效
     */
    private boolean isValidAssociationType(String associationType) {
        return "published".equals(associationType) ||
               "favorited".equals(associationType) ||
               "liked".equals(associationType);
    }
}
