package com.jdl.aic.web.controller;

import com.jdl.aic.portal.common.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 测试控制器
 * 用于验证API接口是否正常工作
 */
@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestController {

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("API服务运行正常");
    }

    /**
     * 测试订阅内容列表接口
     */
    @GetMapping("/subscription/list")
    public Result<Map<String, Object>> testSubscriptionList(
            @RequestParam(required = false, defaultValue = "article") String type) {
        
        // 模拟数据
        List<Map<String, Object>> contents = new ArrayList<>();
        
        // 创建测试内容
        for (int i = 1; i <= 5; i++) {
            Map<String, Object> content = new HashMap<>();
            content.put("id", (long) i);
            content.put("title", type + "标题" + i);
            content.put("description", "这是" + type + "的描述内容" + i);
            content.put("author", "作者" + i);
            content.put("pubDate", "2024-01-" + (10 + i) + "T10:30:00Z");
            content.put("taskName", "测试订阅源" + (i % 3 + 1));
            content.put("type", type);
            
            if ("video".equals(type)) {
                content.put("thumbnail", "/img/video-thumb-" + i + ".jpg");
                content.put("duration", 300 + i * 60);
                content.put("views", 1000 + i * 100);
            } else if ("audio".equals(type)) {
                content.put("cover", "/img/audio-cover-" + i + ".jpg");
                content.put("duration", 1800 + i * 300);
                content.put("plays", 500 + i * 50);
                content.put("host", "主播" + i);
            }
            
            contents.add(content);
        }
        
        // 创建订阅源列表
        List<String> subscriptions = Arrays.asList("测试订阅源1", "测试订阅源2", "测试订阅源3");
        
        Map<String, Object> result = new HashMap<>();
        result.put("contents", contents);
        result.put("subscriptions", subscriptions);
        result.put("total", contents.size());
        result.put("page", 1);
        result.put("size", 20);
        
        return Result.success(result);
    }

    /**
     * 测试根据订阅源获取内容
     */
    @GetMapping("/subscription/by-source")
    public Result<Map<String, Object>> testContentBySubscription(
            @RequestParam(defaultValue = "article") String type,
            @RequestParam(defaultValue = "测试订阅源1") String taskName) {
        
        // 模拟指定订阅源的内容
        List<Map<String, Object>> contents = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            Map<String, Object> content = new HashMap<>();
            content.put("id", (long) i);
            content.put("title", taskName + "的" + type + "标题" + i);
            content.put("description", "来自" + taskName + "的" + type + "描述" + i);
            content.put("author", "作者" + i);
            content.put("pubDate", "2024-01-" + (15 + i) + "T14:30:00Z");
            content.put("taskName", taskName);
            content.put("type", type);
            contents.add(content);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("contents", contents);
        result.put("total", contents.size());
        result.put("page", 1);
        result.put("size", 20);
        result.put("taskName", taskName);
        
        return Result.success(result);
    }

    /**
     * 测试获取内容详情
     */
    @GetMapping("/subscription/{id}")
    public Result<Map<String, Object>> testContentDetail(@PathVariable Long id) {
        Map<String, Object> content = new HashMap<>();
        content.put("id", id);
        content.put("title", "详细内容标题" + id);
        content.put("content", "<h1>这是详细的HTML内容</h1><p>这里是文章的详细内容，包含<strong>粗体</strong>和<em>斜体</em>文字。</p><p>还可以包含图片和链接等元素。</p>");
        content.put("author", "详细作者" + id);
        content.put("pubDate", "2024-01-20T10:30:00Z");
        content.put("taskName", "测试订阅源1");
        
        return Result.success(content);
    }

    /**
     * 测试统计信息
     */
    @GetMapping("/subscription/stats")
    public Result<Map<String, Object>> testStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 各类型统计
        stats.put("articleCount", 25);
        stats.put("articleSubscriptionCount", 5);
        stats.put("videoCount", 18);
        stats.put("videoSubscriptionCount", 3);
        stats.put("audioCount", 32);
        stats.put("audioSubscriptionCount", 7);
        
        // 总计
        stats.put("totalContent", 75);
        stats.put("totalSubscriptions", 15);
        
        return Result.success(stats);
    }

    /**
     * 测试搜索功能
     */
    @GetMapping("/subscription/search")
    public Result<Map<String, Object>> testSearch(
            @RequestParam(required = false) String type,
            @RequestParam String keyword) {
        
        // 模拟搜索结果
        List<Map<String, Object>> contents = new ArrayList<>();
        
        for (int i = 1; i <= 2; i++) {
            Map<String, Object> content = new HashMap<>();
            content.put("id", (long) i);
            content.put("title", "包含" + keyword + "的标题" + i);
            content.put("description", "这是搜索到的包含" + keyword + "关键词的内容描述");
            content.put("author", "搜索作者" + i);
            content.put("pubDate", "2024-01-" + (20 + i) + "T16:30:00Z");
            content.put("taskName", "搜索订阅源" + i);
            content.put("type", type != null ? type : "article");
            contents.add(content);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("contents", contents);
        result.put("total", contents.size());
        result.put("page", 1);
        result.put("size", 20);
        result.put("keyword", keyword);
        
        return Result.success(result);
    }
}
