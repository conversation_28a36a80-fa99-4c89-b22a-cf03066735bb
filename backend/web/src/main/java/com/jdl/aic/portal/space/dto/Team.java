package com.jdl.aic.portal.space.dto;

import java.util.Date;

/**
 * 团队实体类
 */
public class Team {
    private Long teamId;
    private String name;
    private String avatarUrl;
    private String description;
    private Date createdAt;
    private String privacy; // public, private
    private String inviteSetting; // admin_approval, member_invite, open
    private String tags; // JSON字符串存储标签数组
    private Integer memberCount;
    
    // 成就统计字段
    private Integer articlesRecommended;
    private Long totalViews;
    private Long totalLikes;
    private Long totalFavorites;
    
    private Date createTime;
    private Date updateTime;

    // Getters and Setters
    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getPrivacy() {
        return privacy;
    }

    public void setPrivacy(String privacy) {
        this.privacy = privacy;
    }

    public String getInviteSetting() {
        return inviteSetting;
    }

    public void setInviteSetting(String inviteSetting) {
        this.inviteSetting = inviteSetting;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Integer getMemberCount() {
        return memberCount;
    }

    public void setMemberCount(Integer memberCount) {
        this.memberCount = memberCount;
    }

    public Integer getArticlesRecommended() {
        return articlesRecommended;
    }

    public void setArticlesRecommended(Integer articlesRecommended) {
        this.articlesRecommended = articlesRecommended;
    }

    public Long getTotalViews() {
        return totalViews;
    }

    public void setTotalViews(Long totalViews) {
        this.totalViews = totalViews;
    }

    public Long getTotalLikes() {
        return totalLikes;
    }

    public void setTotalLikes(Long totalLikes) {
        this.totalLikes = totalLikes;
    }

    public Long getTotalFavorites() {
        return totalFavorites;
    }

    public void setTotalFavorites(Long totalFavorites) {
        this.totalFavorites = totalFavorites;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
