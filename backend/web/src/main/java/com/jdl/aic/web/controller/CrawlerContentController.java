package com.jdl.aic.web.controller;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.dto.system.CrawlerContentDTO;
import com.jdl.aic.core.service.client.service.CrawlerContentService;
import com.jdl.aic.portal.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 爬虫内容控制器
 * 提供订阅内容的查询和管理功能
 */
@RestController
@RequestMapping("/api/crawler/content")
@CrossOrigin(origins = "*")
public class CrawlerContentController {

    @Autowired
    private CrawlerContentService crawlerContentService;

    /**
     * 获取内容列表（支持分页和类型过滤）
     * @param type 内容类型：article、video、audio
     * @param page 页码，默认1
     * @param size 每页大小，默认20
     * @param keyword 搜索关键词
     * @return 内容列表和订阅源列表
     */
    @GetMapping("/list")
    public Result<Map<String, Object>> getContentList(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword) {

        try {
            // 构建分页请求
            PageRequest pageRequest = new PageRequest(page, size);

            // 调用服务获取内容
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents =
                crawlerContentService.getCrawlerContentList(null, null, null, null, null, null, type, null, null);

            if (contents == null || contents.getData() == null) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("contents", new ArrayList<>());
                emptyResult.put("subscriptions", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("page", page);
                emptyResult.put("size", size);
                return Result.success(emptyResult);
            }

            List<CrawlerContentDTO> contentList = contents.getData().getRecords();

            // 提取订阅源列表（taskName去重）
            Set<String> subscriptionSet = contentList.stream()
                    .map(CrawlerContentDTO::getTaskName)
                    .filter(Objects::nonNull)
                    .filter(taskName -> !taskName.trim().isEmpty())
                    .collect(Collectors.toSet());
            List<String> subscriptions = new ArrayList<>(subscriptionSet);

            // 计算总数
            long total = contents.getData().getPagination() != null ?
                contents.getData().getPagination().getTotalElements() : contentList.size();

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contents", contentList);
            result.put("subscriptions", subscriptions);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);

            return Result.success(result);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取内容列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据订阅源获取内容
     * @param type 内容类型
     * @param taskName 订阅源名称
     * @param page 页码
     * @param size 每页大小
     * @return 指定订阅源的内容列表
     */
    @GetMapping("/by-subscription")
    public Result<Map<String, Object>> getContentBySubscription(
            @RequestParam String type,
            @RequestParam String taskName,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        try {
            // 构建分页请求
            PageRequest pageRequest = new PageRequest(page, size);

            // 调用服务获取指定订阅源的内容
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents =
                crawlerContentService.getCrawlerContentList(null, null, null, null, null, null, type, null, null);

            if (contents == null || contents.getData() == null) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("contents", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("page", page);
                emptyResult.put("size", size);
                emptyResult.put("taskName", taskName);
                return Result.success(emptyResult);
            }

            List<CrawlerContentDTO> contentList = contents.getData().getRecords();

            // 过滤指定订阅源的内容
            List<CrawlerContentDTO> filteredContents = contentList.stream()
                .filter(content -> taskName.equals(content.getTaskName()))
                .collect(Collectors.toList());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contents", filteredContents);
            result.put("total", filteredContents.size());
            result.put("page", page);
            result.put("size", size);
            result.put("taskName", taskName);

            return Result.success(result);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取订阅内容失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取内容详情
     * @param id 内容ID
     * @return 内容详情（包含完整的HTML内容）
     */
    @GetMapping("/{id}")
    public Result<CrawlerContentDTO> getContentById(@PathVariable Long id) {
        try {
            if (id == null) {
                return Result.error(-1, "内容ID不能为空");
            }

            // 调用服务获取内容详情
            com.jdl.aic.core.service.client.common.Result<CrawlerContentDTO> content =
                crawlerContentService.getCrawlerContentById(id);

            if (content == null || content.getData() == null) {
                return Result.error(-1, "内容不存在");
            }

            return Result.success(content.getData());

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取内容详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索内容
     * @param type 内容类型
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    @GetMapping("/search")
    public Result<Map<String, Object>> searchContent(
            @RequestParam(required = false) String type,
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        try {
            if (!StringUtils.hasText(keyword)) {
                return Result.error(-1, "搜索关键词不能为空");
            }

            // 构建分页请求

            // 调用服务搜索内容
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents =
                crawlerContentService.getCrawlerContentList(null, null, null, null, null, null, type, null, null);

            if (contents == null || contents.getData() == null) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("contents", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("page", page);
                emptyResult.put("size", size);
                emptyResult.put("keyword", keyword);
                return Result.success(emptyResult);
            }

            List<CrawlerContentDTO> contentList = contents.getData().getRecords();

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contents", contentList);
            result.put("total", contentList.size());
            result.put("page", page);
            result.put("size", size);
            result.put("keyword", keyword);

            return Result.success(result);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "搜索内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取统计信息
     * @return 各类型内容的统计数据
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取各类型内容统计
            String[] types = {"article", "video", "audio"};
            for (String type : types) {
                try {
                    // 调用服务获取指定类型的内容
                    com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents =
                        crawlerContentService.getCrawlerContentList(null, null, null, null, null, null, type, null, null);

                    if (contents != null && contents.getData() != null) {
                        List<CrawlerContentDTO> contentList = contents.getData().getRecords();
                        stats.put(type + "Count", contentList.size());

                        // 统计订阅源数量
                        Set<String> subscriptions = contentList.stream()
                                .map(CrawlerContentDTO::getTaskName)
                                .filter(Objects::nonNull)
                                .filter(taskName -> !taskName.trim().isEmpty())
                                .collect(Collectors.toSet());
                        stats.put(type + "SubscriptionCount", subscriptions.size());
                    } else {
                        stats.put(type + "Count", 0);
                        stats.put(type + "SubscriptionCount", 0);
                    }
                } catch (Exception e) {
                    // 如果某个类型获取失败，设置为0
                    stats.put(type + "Count", 0);
                    stats.put(type + "SubscriptionCount", 0);
                }
            }

            // 总计
            int totalContent = (Integer) stats.getOrDefault("articleCount", 0) +
                              (Integer) stats.getOrDefault("videoCount", 0) +
                              (Integer) stats.getOrDefault("audioCount", 0);
            int totalSubscriptions = (Integer) stats.getOrDefault("articleSubscriptionCount", 0) +
                                   (Integer) stats.getOrDefault("videoSubscriptionCount", 0) +
                                   (Integer) stats.getOrDefault("audioSubscriptionCount", 0);

            stats.put("totalContent", totalContent);
            stats.put("totalSubscriptions", totalSubscriptions);

            return Result.success(stats);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门内容
     * @param type 内容类型
     * @param limit 限制数量，默认10
     * @return 热门内容列表
     */
    @GetMapping("/popular")
    public Result<List<CrawlerContentDTO>> getPopularContent(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "10") Integer limit) {

        try {
            // 构建分页请求

            // 调用服务获取内容（这里可以添加排序逻辑，比如按浏览量、点赞数等排序）
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents =
                crawlerContentService.getCrawlerContentList(null, null, null, null, null, null, type, null, null);

            if (contents == null || contents.getData() == null) {
                return Result.success(new ArrayList<>());
            }

            List<CrawlerContentDTO> contentList = contents.getData().getRecords();

            // 限制返回数量
            List<CrawlerContentDTO> limitedContents = contentList.stream()
                .limit(limit)
                .collect(Collectors.toList());

            return Result.success(limitedContents);

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(-1, "获取热门内容失败: " + e.getMessage());
        }
    }
}
