package com.jdl.aic.web.controller;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.dto.system.CrawlerContentDTO;
import com.jdl.aic.core.service.client.service.CrawlerContentService;
import com.jdl.aic.portal.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 爬虫内容控制器
 * 提供订阅内容的查询和管理功能
 */
@RestController
@RequestMapping("/api/crawler/content")
@CrossOrigin(origins = "*")
public class CrawlerContentController {

    @Autowired
    private CrawlerContentService crawlerContentService;

    /**
     * 获取内容列表（支持分页和类型过滤）
     * @param type 内容类型：article、video、audio
     * @param page 页码，默认1
     * @param size 每页大小，默认20
     * @param keyword 搜索关键词
     * @return 内容列表和订阅源列表
     */
    @GetMapping("/list")
    public Result<Map<String, Object>> getContentList(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword) {
        
        try {
            // 构建查询条件
            
            // 调用服务获取内容
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents = crawlerContentService.getCrawlerContentList(
                    null,null,null,null,null,null,type,null,null);
            
            // 提取订阅源列表（taskName去重）
            Set<String> subscriptionSet = contents.getData().getRecords().stream()
                    .map(CrawlerContentDTO::getTaskName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            List<String> subscriptions = new ArrayList<>(subscriptionSet);
            
            // 计算总数（这里简化处理，实际应该调用count方法）
            long total = contents.getData().getPagination().getTotalElements();
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contents", contents);
            result.put("subscriptions", subscriptions);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            
            return Result.success(result);
            
        } catch (Exception e) {
            return Result.error(-1, "获取内容列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据订阅源获取内容
     * @param type 内容类型
     * @param taskName 订阅源名称
     * @param page 页码
     * @param size 每页大小
     * @return 指定订阅源的内容列表
     */
    @GetMapping("/by-subscription")
    public Result<Map<String, Object>> getContentBySubscription(
            @RequestParam String type,
            @RequestParam String taskName,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        try {
            com.jdl.aic.core.service.client.common.Result<PageResult<CrawlerContentDTO>> contents = crawlerContentService.getCrawlerContentList(
                    null,null,null,null,null,null,type,null,null);

            List<CrawlerContentDTO> subscriptionSet = contents.getData().getRecords();
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contents", subscriptionSet);
            result.put("total", contents.getData().getPagination().getTotalElements());
            result.put("page", page);
            result.put("size", size);
            result.put("taskName", taskName);
            
            return Result.success(result);
            
        } catch (Exception e) {
            return Result.error(-1, "获取订阅内容失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取内容详情
     * @param id 内容ID
     * @return 内容详情（包含完整的HTML内容）
     */
    @GetMapping("/{id}")
    public Result<CrawlerContentDTO> getContentById(@PathVariable Long id) {
        try {
            if (id == null) {
                return Result.error(-1, "内容ID不能为空");
            }
            
            // 调用服务获取内容详情
            com.jdl.aic.core.service.client.common.Result<CrawlerContentDTO> content = crawlerContentService.getCrawlerContentById(id);
            
            if (content == null) {
                return Result.error(-1, "内容不存在");
            }
            
            return Result.success(content.getData());
            
        } catch (Exception e) {
            return Result.error(-1, "获取内容详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索内容
     * @param type 内容类型
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    @GetMapping("/search")
    public Result<Map<String, Object>> searchContent(
            @RequestParam(required = false) String type,
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        try {
            if (!StringUtils.hasText(keyword)) {
                return Result.error(-1, "搜索关键词不能为空");
            }

            
            // 调用服务搜索内容
            List<CrawlerContentDTO> contents = crawlerContentService.getCrawlerContentByCondition(condition);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contents", contents);
            result.put("total", contents.size());
            result.put("page", page);
            result.put("size", size);
            result.put("keyword", keyword);
            
            return Result.success(result);
            
        } catch (Exception e) {
            return Result.error(ResultCode.SYSTEM_ERROR, "搜索内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取统计信息
     * @return 各类型内容的统计数据
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 获取各类型内容统计
            String[] types = {"article", "video", "audio"};
            for (String type : types) {
                CrawlerContentConditionDTO condition = new CrawlerContentConditionDTO();
                condition.setType(type);
                List<CrawlerContentDTO> contents = crawlerContentService.getCrawlerContentByCondition(condition);
                stats.put(type + "Count", contents.size());
                
                // 统计订阅源数量
                Set<String> subscriptions = contents.stream()
                        .map(CrawlerContentDTO::getTaskName)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                stats.put(type + "SubscriptionCount", subscriptions.size());
            }
            
            // 总计
            int totalContent = (Integer) stats.get("articleCount") + 
                              (Integer) stats.get("videoCount") + 
                              (Integer) stats.get("audioCount");
            int totalSubscriptions = (Integer) stats.get("articleSubscriptionCount") + 
                                   (Integer) stats.get("videoSubscriptionCount") + 
                                   (Integer) stats.get("audioSubscriptionCount");
            
            stats.put("totalContent", totalContent);
            stats.put("totalSubscriptions", totalSubscriptions);
            
            return Result.success(stats);
            
        } catch (Exception e) {
            return Result.error(ResultCode.SYSTEM_ERROR, "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门内容
     * @param type 内容类型
     * @param limit 限制数量，默认10
     * @return 热门内容列表
     */
    @GetMapping("/popular")
    public Result<List<CrawlerContentDTO>> getPopularContent(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            CrawlerContentConditionDTO condition = new CrawlerContentConditionDTO();
            condition.setType(type);
            condition.setSize(limit);
            // 这里可以添加排序逻辑，比如按浏览量、点赞数等排序
            
            List<CrawlerContentDTO> contents = crawlerContentService.getCrawlerContentByCondition(condition);
            
            return Result.success(contents);
            
        } catch (Exception e) {
            return Result.error(ResultCode.SYSTEM_ERROR, "获取热门内容失败: " + e.getMessage());
        }
    }
}
