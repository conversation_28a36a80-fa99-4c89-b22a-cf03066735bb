<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    	http://www.springframework.org/schema/beans/spring-beans.xsd http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <!-- #测试index服务地址   index="test.i.jsf.jd.local"   -->
    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jsf.registry.provider}"/>

    <!-- AIPortal 用户信息 -->
    <jsf:consumer id="userDataService" interface="com.jdl.aic.core.service.portal.client.UserDataService"
                  alias="${jsf.consumer.aiportal.userDataService.alias}" timeout="${jsf.consumer.aiportal.userDataService.timeout}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.consumer.aiportal.userDataService.token}"/>
    </jsf:consumer>

    <!-- AIPortal 团队信息 -->
    <jsf:consumer id="teamDataService" interface="com.jdl.aic.core.service.portal.client.TeamDataService"
                  alias="${jsf.consumer.aiportal.teamDataService.alias}" timeout="${jsf.consumer.aiportal.teamDataService.timeout}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.consumer.aiportal.teamDataService.token}"/>
    </jsf:consumer>

    <!-- AIPortal 收藏信息 -->
    <jsf:consumer id="favoriteDataService" interface="com.jdl.aic.core.service.portal.client.FavoriteDataService"
                  alias="${jsf.consumer.aiportal.teamDataService.alias}" timeout="${jsf.consumer.aiportal.teamDataService.timeout}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.consumer.aiportal.teamDataService.token}"/>
    </jsf:consumer>

    <!-- AIPortal 点赞信息 -->
    <jsf:consumer id="likeDataService" interface="com.jdl.aic.core.service.portal.client.LikeDataService"
                  alias="${jsf.consumer.aiportal.teamDataService.alias}" timeout="${jsf.consumer.aiportal.teamDataService.timeout}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.consumer.aiportal.teamDataService.token}"/>
    </jsf:consumer>

    <!-- AIPortal 内容信息 -->
    <jsf:consumer id="knowledgeService" interface="com.jdl.aic.core.service.client.service.KnowledgeService"
                  alias="${jsf.consumer.aiportal.teamDataService.alias}" timeout="${jsf.consumer.aiportal.teamDataService.timeout}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.consumer.aiportal.teamDataService.token}"/>
    </jsf:consumer>

    <!-- AIPortal 团队信息 -->
    <jsf:consumer id="crawlerContentService" interface="com.jdl.aic.core.service.client.service.CrawlerContentService"
                  alias="${jsf.consumer.aiportal.teamDataService.alias}" timeout="${jsf.consumer.aiportal.teamDataService.timeout}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.consumer.aiportal.teamDataService.token}"/>
    </jsf:consumer>
</beans>