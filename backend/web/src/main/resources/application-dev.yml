spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************
    username: adminwl
    password: 2yfTkySnW6EcD
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.jdl.aic.portal.dao.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

server:
  port: 8000
  servlet:
    context-path: /

logging:
  level:
    com.jdl.aic.portal: debug
    org.springframework: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

jwt:
  secret: bXlTZWNyZXRLZXlGb3JKV1RUb2tlbjEyMzQ1Njc4OTA=
  expiration: 86400

oauth:
  google:
    client-id: ************-60q450k9sdq7pdv4gmo669mgag4gnvnl.apps.googleusercontent.com
    client-secret: 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
    redirect-uri: http://localhost:8000/api/auth/oauth/google/callback
  github:
    client-id: Ov23li87Urd6l8uQqwCh
    client-secret: c92f2fe17b025d9477be66969e83f8e8741e5c28
    redirect-uri: http://localhost:8000/api/auth/oauth/github/callback