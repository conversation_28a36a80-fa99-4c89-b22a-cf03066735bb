<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/cache
        http://www.springframework.org/schema/cache/spring-cache.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop.xsd
        http://www.springframework.org/schema/task
        http://www.springframework.org/schema/task/spring-task.xsd"
       default-lazy-init="false">

    <!-- 启用注解配置 -->
    <context:annotation-config/>
    
    <!-- AspectJ自动代理已禁用 - 当前项目中没有使用@Aspect类 -->
    <!-- <aop:aspectj-autoproxy proxy-target-class="true" expose-proxy="true"/> -->
    
    <!-- 启用缓存注解驱动 -->
    <cache:annotation-driven cache-manager="cacheManager" proxy-target-class="true"/>
    
    <!-- 启用异步任务注解驱动 -->
    <task:annotation-driven executor="taskExecutor" scheduler="taskScheduler" proxy-target-class="true"/>

    <!-- 任务执行器配置 -->
    <task:executor id="taskExecutor" 
                   pool-size="5-25" 
                   queue-capacity="100" 
                   keep-alive="60"
                   rejection-policy="CALLER_RUNS"/>
    
    <!-- 任务调度器配置 -->
    <task:scheduler id="taskScheduler" pool-size="10"/>

    <!-- 简单缓存管理器配置 -->
    <bean id="cacheManager" class="org.springframework.cache.concurrent.ConcurrentMapCacheManager">
        <property name="cacheNames">
            <set>
                <value>userCache</value>
                <value>teamCache</value>
                <value>contentCache</value>
            </set>
        </property>
    </bean>

    <!-- 导入其他Spring配置文件 -->
    <import resource="classpath:spring/spring-jsf-consumer.xml"/>

</beans>
