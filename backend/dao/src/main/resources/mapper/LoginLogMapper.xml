<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.portal.dao.mapper.LoginLogMapper">
    
    <resultMap id="BaseResultMap" type="com.jdl.aic.portal.dao.entity.LoginLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="login_type" property="loginType" jdbcType="VARCHAR"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="login_location" property="loginLocation" jdbcType="VARCHAR"/>
        <result column="login_status" property="loginStatus" jdbcType="VARCHAR"/>
        <result column="failure_reason" property="failureReason" jdbcType="VARCHAR"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, username, login_type, ip_address, user_agent, login_location, login_status, failure_reason, login_time
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.portal.dao.entity.LoginLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sys_login_log (user_id, username, login_type, ip_address, user_agent, login_location, login_status, failure_reason, login_time)
        VALUES (#{userId}, #{username}, #{loginType}, #{ipAddress}, #{userAgent}, #{loginLocation}, #{loginStatus}, #{failureReason}, #{loginTime})
    </insert>

    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_login_log 
        WHERE user_id = #{userId}
        ORDER BY login_time DESC
    </select>

    <select id="selectByUsername" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_login_log 
        WHERE username = #{username}
        ORDER BY login_time DESC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_login_log
        ORDER BY login_time DESC
    </select>

    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_login_log 
        WHERE login_status = #{status}
        ORDER BY login_time DESC
    </select>

    <select id="selectRecentLogs" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_login_log
        ORDER BY login_time DESC
        LIMIT #{limit}
    </select>

</mapper>