<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.portal.dao.mapper.UserMapper">
    
    <resultMap id="BaseResultMap" type="com.jdl.aic.portal.dao.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="provider" property="provider" jdbcType="VARCHAR"/>
        <result column="provider_id" property="providerId" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, username, password, email, nickname, avatar, provider, provider_id, enabled, last_login_time, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.portal.dao.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sys_user (username, password, email, nickname, avatar, provider, provider_id, enabled, last_login_time, create_time, update_time)
        VALUES (#{username}, #{password}, #{email}, #{nickname}, #{avatar}, #{provider}, #{providerId}, #{enabled}, #{lastLoginTime}, NOW(), NOW())
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM sys_user WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.jdl.aic.portal.dao.entity.User">
        UPDATE sys_user 
        SET username = #{username},
            password = #{password},
            email = #{email},
            nickname = #{nickname},
            avatar = #{avatar},
            provider = #{provider},
            provider_id = #{providerId},
            enabled = #{enabled},
            last_login_time = #{lastLoginTime},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user 
        WHERE id = #{id}
    </select>

    <select id="selectByUsername" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user 
        WHERE username = #{username}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user
        ORDER BY create_time DESC
    </select>

    <select id="selectByEmail" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user 
        WHERE email = #{email}
    </select>

    <select id="selectByProviderAndProviderId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user 
        WHERE provider = #{provider} AND provider_id = #{providerId}
    </select>

    <update id="updateLastLoginTime" parameterType="java.lang.Long">
        UPDATE sys_user 
        SET last_login_time = NOW(),
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>