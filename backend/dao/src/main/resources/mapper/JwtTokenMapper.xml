<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jdl.aic.portal.dao.mapper.JwtTokenMapper">
    
    <!-- 结果映射 -->
    <resultMap id="JwtTokenResultMap" type="com.jdl.aic.portal.dao.entity.JwtToken">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="tokenId" column="token_id"/>
        <result property="tokenType" column="token_type"/>
        <result property="tokenValue" column="token_value"/>
        <result property="expiresAt" column="expires_at"/>
        <result property="issuedAt" column="issued_at"/>
        <result property="isRevoked" column="is_revoked"/>
        <result property="deviceInfo" column="device_info"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 插入新的JWT Token记录 -->
    <insert id="insert" parameterType="com.jdl.aic.portal.dao.entity.JwtToken" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sys_jwt_token (
            user_id, token_id, token_type, token_value, expires_at, 
            issued_at, is_revoked, device_info, ip_address, create_time
        ) VALUES (
            #{userId}, #{tokenId}, #{tokenType}, #{tokenValue}, #{expiresAt}, 
            #{issuedAt}, #{isRevoked}, #{deviceInfo}, #{ipAddress}, #{createTime}
        )
    </insert>

    <!-- 根据Token ID查询Token记录 -->
    <select id="selectByTokenId" resultMap="JwtTokenResultMap">
        SELECT * FROM sys_jwt_token 
        WHERE token_id = #{tokenId}
    </select>

    <!-- 根据用户ID查询有效的Token列表 -->
    <select id="selectValidTokensByUserId" resultMap="JwtTokenResultMap">
        SELECT * FROM sys_jwt_token 
        WHERE user_id = #{userId} 
          AND is_revoked = FALSE 
          AND expires_at > NOW()
        ORDER BY create_time DESC
    </select>

    <!-- 撤销Token（设置为已撤销状态） -->
    <update id="revokeToken">
        UPDATE sys_jwt_token 
        SET is_revoked = TRUE 
        WHERE token_id = #{tokenId}
    </update>

    <!-- 撤销用户的所有Token -->
    <update id="revokeAllTokensByUserId">
        UPDATE sys_jwt_token 
        SET is_revoked = TRUE 
        WHERE user_id = #{userId}
    </update>

    <!-- 清理过期的Token -->
    <delete id="deleteExpiredTokens">
        DELETE FROM sys_jwt_token 
        WHERE expires_at &lt; NOW() OR is_revoked = TRUE
    </delete>

    <!-- 根据用户ID和设备信息查询Token -->
    <select id="selectTokensByUserIdAndDevice" resultMap="JwtTokenResultMap">
        SELECT * FROM sys_jwt_token 
        WHERE user_id = #{userId} 
          AND device_info = #{deviceInfo}
          AND is_revoked = FALSE 
          AND expires_at > NOW()
        ORDER BY create_time DESC
    </select>

    <!-- 限制用户同时登录设备数量，撤销最旧的Token -->
    <update id="revokeOldestTokensByUserId">
        UPDATE sys_jwt_token 
        SET is_revoked = TRUE 
        WHERE user_id = #{userId} 
          AND is_revoked = FALSE 
          AND expires_at > NOW()
          AND id NOT IN (
            SELECT * FROM (
              SELECT id FROM sys_jwt_token 
              WHERE user_id = #{userId} 
                AND is_revoked = FALSE 
                AND expires_at > NOW()
              ORDER BY create_time DESC 
              LIMIT #{limit}
            ) AS latest_tokens
          )
    </update>

</mapper>