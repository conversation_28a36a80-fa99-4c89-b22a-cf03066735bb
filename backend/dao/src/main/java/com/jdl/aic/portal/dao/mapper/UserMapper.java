package com.jdl.aic.portal.dao.mapper;

import com.jdl.aic.portal.dao.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户Mapper - 仅保留认证必需的方法
 * 详细的用户资料操作通过外部服务处理
 */
@Mapper
public interface UserMapper {

    /**
     * 插入新用户（注册时使用）
     */
    int insert(User user);

    /**
     * 根据ID查询用户（认证时使用）
     */
    User selectById(@Param("id") Long id);

    /**
     * 根据用户名查询用户（登录时使用）
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户（登录时使用）
     */
    User selectByEmail(@Param("email") String email);

    /**
     * 根据第三方登录信息查询用户（OAuth登录时使用）
     */
    User selectByProviderAndProviderId(@Param("provider") String provider, @Param("providerId") String providerId);

    /**
     * 更新最后登录时间
     */
    int updateLastLoginTime(@Param("id") Long id);

    /**
     * 更新基础用户信息（昵称、头像等）
     */
    int updateBasicInfo(User user);
}