package com.jdl.aic.portal.dao.mapper;

import com.jdl.aic.portal.dao.entity.JwtToken;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface JwtTokenMapper {
    
    /**
     * 插入新的JWT Token记录
     */
    int insert(JwtToken jwtToken);
    
    /**
     * 根据Token ID查询Token记录
     */
    JwtToken selectByTokenId(@Param("tokenId") String tokenId);
    
    /**
     * 根据用户ID查询有效的Token列表
     */
    List<JwtToken> selectValidTokensByUserId(@Param("userId") Long userId);
    
    /**
     * 撤销Token（设置为已撤销状态）
     */
    int revokeToken(@Param("tokenId") String tokenId);
    
    /**
     * 撤销用户的所有Token
     */
    int revokeAllTokensByUserId(@Param("userId") Long userId);
    
    /**
     * 清理过期的Token
     */
    int deleteExpiredTokens();
    
    /**
     * 根据用户ID和设备信息查询Token
     */
    List<JwtToken> selectTokensByUserIdAndDevice(@Param("userId") Long userId, @Param("deviceInfo") String deviceInfo);
    
    /**
     * 限制用户同时登录设备数量，撤销最旧的Token
     */
    int revokeOldestTokensByUserId(@Param("userId") Long userId, @Param("limit") int limit);
}