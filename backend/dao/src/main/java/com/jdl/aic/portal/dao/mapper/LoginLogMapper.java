package com.jdl.aic.portal.dao.mapper;

import com.jdl.aic.portal.dao.entity.LoginLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LoginLogMapper {
    
    int insert(LoginLog loginLog);
    
    List<LoginLog> selectByUserId(@Param("userId") Long userId);
    
    List<LoginLog> selectByUsername(@Param("username") String username);
    
    List<LoginLog> selectAll();
    
    List<LoginLog> selectByStatus(@Param("status") String status);
    
    List<LoginLog> selectRecentLogs(@Param("limit") Integer limit);
}